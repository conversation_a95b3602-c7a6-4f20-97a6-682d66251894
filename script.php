<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

defined('_JEXEC') or die('Restricted access');

// @codingStandardsIgnoreLine because of no namespace set
class com_dropboxInstallerScript
{
    /**
     * method to install the component
     *
     * @return void
     */
    public function install($parent)
    {
        //load libraries
        $db = & JFactory::getDBO();

        $table_fields = $db->getTableColumns('#__dropbox');


        //this updates need to run at any update
        //they do not harm if they run multiple times
        //in future files in /sql/updates should be used
        if (!isset($table_fields['box_type'])) {
            $query = 'ALTER TABLE `#__dropbox`
					ADD `box_type` VARCHAR(15) NOT NULL,
					ADD `username` VARCHAR(255) NOT NULL,
					ADD `password` VARCHAR(255) NOT NULL';
            $db->setQuery($query);
            $db->execute();
        }

        $query = 'UPDATE `#__dropbox` SET `box_type` = "dropbox" WHERE box_type=""';
        $db->setQuery($query);
        $db->execute();
        $query = 'ALTER TABLE `#__dropbox` CHANGE `dropbox_token` `dropbox_token` TEXT';
        $db->setQuery($query);
        $db->execute();

        // Check if a column called `dropbox_expiry_time` exists
        $query = $db->getQuery(true);
        $query->select('COUNT(*) as count')
            ->from('information_schema.columns')
            ->where('column_name="dropbox_expiry_time"');
        $db->setQuery($query);
        $result = $db->loadAssoc();
        $countOfColumns = (int)$result['count'];

        // if the required column doesn't exist
        if ($countOfColumns == 0) {
            $query = 'ALTER TABLE `#__dropbox` ADD `dropbox_expiry_time` bigint AFTER `dropbox_token`';
            $db->setQuery($query);
            $db->execute();
        }

        // Check if a column called `dropbox_refresh_token` exists
        $query_for_refresh_token = $db->getQuery(true);
        $query_for_refresh_token->select('COUNT(*) as count')
            ->from('information_schema.columns')
            ->where('column_name="dropbox_refresh_token"');
        $db->setQuery($query_for_refresh_token);
        $result = $db->loadAssoc();
        $countOfColumns = (int)$result['count'];

        // if the required column doesn't exist
        if ($countOfColumns === 0) {
            $query = 'ALTER TABLE `#__dropbox` ADD `dropbox_refresh_token` TEXT  DEFAULT NULL AFTER `dropbox_token`';
            $db->setQuery($query);
            $db->execute();
        }

        // $parent is the class calling this method
        $parent->getParent()->setRedirectURL('index.php?option=com_dropbox');
    }


    /**
     * method to run after an install/update/uninstall method
     *
     * @return void
     */
    public function postflight($type, $parent)
    {
        // $parent is the class calling this method
        // $type is the type of change (install, update or discover_install, uninstall)
        echo '<p>' . JText::_('COM_DROPBOX_POSTFLIGHT_' . $type . '_TEXT') . '</p>';
    }
}
