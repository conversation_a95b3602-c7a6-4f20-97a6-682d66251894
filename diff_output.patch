diff --git a/admin/controllers/dropbox.php b/admin/controllers/dropbox.php
index 4c3ac60..2edc721 100755
--- a/admin/controllers/dropbox.php
+++ b/admin/controllers/dropbox.php
@@ -28,7 +28,7 @@ class DropboxControllerDropbox extends JControllerForm
      *
      * @return void
      */
-    public function clear()
+    public function clear(): void
     {
         $model = $this->getModel('dropbox');
 
@@ -49,15 +49,17 @@ class DropboxControllerDropbox extends JControllerForm
      *
      * @access public
      */
-    public function connect()
+    public function connect(): void
     {
-        if (isset($_POST['jform']['dropbox_secret']) && $_POST['jform']['dropbox_secret'] !== '') {
+        $jform         = filter_input(INPUT_POST, 'jform', FILTER_DEFAULT, FILTER_REQUIRE_ARRAY);
+        $dropboxSecret = $jform['dropbox_secret'] ?? '';
+
+        if ($dropboxSecret !== '') {
             $application = JFactory::getApplication();
             $application->enqueueMessage(JText::_('COM_DROPBOX_TOKEN_MUST_BE_EMPTY'), 'error');
         }
 
-        $this->registerTask('apply', 'save');
-        $this->execute("apply");
+        $this->applyAndSave();
     }
 
     /**
@@ -65,9 +67,19 @@ class DropboxControllerDropbox extends JControllerForm
      *
      * @access public
      */
-    public function preview()
+    public function preview(): void
+    {
+        $this->applyAndSave();
+    }
+
+    /**
+     * Register apply task and execute it
+     *
+     * @access private
+     */
+    private function applyAndSave(): void
     {
         $this->registerTask('apply', 'save');
-        $this->execute("apply");
+        $this->execute('apply');
     }
 }
diff --git a/admin/helpers/dropbox.php b/admin/helpers/dropbox.php
index 9a97a25..2611300 100755
--- a/admin/helpers/dropbox.php
+++ b/admin/helpers/dropbox.php
@@ -9,13 +9,21 @@
 // @codingStandardsIgnoreLine because of no namespace set
 abstract class DropboxHelper
 {
+    public const ACTIONS = [
+        'core.admin',
+        'core.manage',
+        'core.create',
+        'core.edit',
+        'core.delete',
+    ];
+
     /**
      * Get the actions
      */
-    public static function getActions($messageId = 0)
+    public static function getActions($messageId = 0): \stdClass
     {
         $user      = JFactory::getUser();
-        $result    = new JObject();
+        $result    = new \stdClass();
 
         if (empty($messageId)) {
             $assetName = 'com_dropbox';
@@ -23,12 +31,8 @@ abstract class DropboxHelper
             $assetName = 'com_dropbox.message.' . (int) $messageId;
         }
 
-        $actions = [
-        'core.admin', 'core.manage', 'core.create', 'core.edit', 'core.delete',
-        ];
-
-        foreach ($actions as $action) {
-            $result->set($action, $user->authorise($action, $assetName));
+        foreach (self::ACTIONS as $action) {
+            $result->$action = $user->authorise($action, $assetName);
         }
 
         return $result;
diff --git a/site/helpers/dropbox.php b/site/helpers/dropbox.php
index 15c3f8c..a5c0467 100644
--- a/site/helpers/dropbox.php
+++ b/site/helpers/dropbox.php
@@ -1,30 +1,80 @@
 <?php
 
+enum PathType: string
+{
+    case FILE   = 'file';
+    case FOLDER = 'folder';
+    case SEARCH = 'search';
+
+    // Method to get forbidden characters pattern based on type
+    public function getForbiddenPattern(): string
+    {
+        return match ($this) {
+            self::FOLDER => '#[:\#\*"@+=;!&%<>\]\'\\\\|\[]#',
+            self::FILE, self::SEARCH => '#[:\#\*"@+=;!&%<>\]\/\'\\\\|\[]#'
+        };
+    }
+
+    // Method to check if type should use dashes
+    public function shouldUseDashes(): bool
+    {
+        return $this !== self::SEARCH;
+    }
+
+    // Convert string to enum safely
+    public static function fromString(string $value): self
+    {
+        return self::tryFrom($value) ?? throw new InvalidArgumentException("Invalid path type: $value");
+    }
+}
+
+// TRAIT for string processing utilities
+trait StringProcessingTrait
+{
+    private function normalizeWhitespace(string $str): string
+    {
+        // Replace double byte whitespaces to single byte
+        $str = preg_replace('/\xE3\x80\x80/', ' ', $str);
+        // Replace any multiple whitespace with a single white space
+        return preg_replace('/\s+/', ' ', $str);
+    }
+
+    private function processDots(string $str): string
+    {
+        return preg_replace([
+            '/\/\.+\//',    // dots between slashes
+            '/^\.+\//',     // dot at start with slash
+            '/\/\.+$/',     // dots at end with slash
+            '/(\.)\1+/',    // duplicate dots
+            '/^\.$/',        // single dot
+        ], [
+            "/\x20/",
+            "\x20/",
+            "/\x20",
+            "\x20",
+            "\x20",
+        ], $str);
+    }
+}
+
 // @codingStandardsIgnoreLine because of no namespace set
 class DropboxHelper
 {
+    use StringProcessingTrait;
+
     /**
-     * helper function to use from makeFilenameSafe and makeFoldernameSafe because both are so simmilar
+     * helper function to use from makeFilenameSafe and makeFoldernameSafe because both are so similar
      *
-     * @param string $string folder or file name
-     * @param string $type   file|folder|search
+     * @param  string   $string folder or file name
+     * @param  PathType $type   file|folder|search
+     * @return string
      */
-    protected static function makeFileFoldernameSafe($string, $type = "file")
+    protected static function makeFileFoldernameSafe(string $string, PathType $type = PathType::FILE): string
     {
-        if (
-            $type !== 'file'
-            && $type !== 'folder'
-            && $type !== 'search'
-        ) {
-            throw new InvalidArgumentException("invalid type");
-        }
-        //replace double byte whitespaces to single byte
-        $str = preg_replace('/\xE3\x80\x80/', ' ', $string);
+        // Use trait method for whitespace normalization
+        $str = (new self())->normalizeWhitespace($string);
 
-        //replace any multiple whitespace with a single white space
-        $str = preg_replace('/\s+/', ' ', $str);
-
-        if ($type !== 'search') {
+        if ($type->shouldUseDashes()) {
             // remove any '-' from the string as they will be used as concatenator.
             $str = str_replace('-', ' ', $str);
         }
@@ -33,11 +83,7 @@ class DropboxHelper
         $str = strip_tags($str);
 
         //replace forbidden characters by whitespaces
-        if ($type === "folder") {
-            $str = preg_replace('#[:\#\*"@+=;!&%<>\]\'\\\\|\[]#', "-", $str);
-        } else {
-            $str = preg_replace('#[:\#\*"@+=;!&%<>\]\/\'\\\\|\[]#', "-", $str);
-        }
+        $str = preg_replace($type->getForbiddenPattern(), "-", $str);
 
         //delete all '?'
         $str = str_replace('?', '', $str);
@@ -45,34 +91,16 @@ class DropboxHelper
         //trim white spaces at beginning and end of alias
         $str = trim($str);
 
-        //replace all dots between slashes if nothing else it there with spaces
-        $str = preg_replace('/\/\.+\//', "/\x20/", $str);
-
-        //replace dot in ./ with space
-        $str = preg_replace('/^\.+\//', "\x20/", $str);
-
-        //replace all dots at ends with spaces
-        $str = preg_replace('/\/\.+$/', "/\x20", $str);
-
-        //replace any duplicate dots with spaces
-        $str = preg_replace('/(\.)\1+/', "\x20", $str);
+        // Process dots
+        $str = (new self())->processDots($str);
 
         // remove any duplicate slashes
         $str = preg_replace('/(\/)\1+/', "/", $str);
 
-        //if only a dot is left over replace it with a space
-        $str = preg_replace('/^\.$/', "\x20", $str);
-
-        if ($type === "search") {
-            //only single whitespaces are allowed
-            $str = preg_replace('#\x20+#', ' ', $str);
-            $str = trim($str);
-        } else {
-            // remove any duplicate whitespace and replace whitespaces by hyphens
-            $str = preg_replace('#\x20+#', '-', $str);
-        }
-
-        return $str;
+        return match ($type) {
+            PathType::SEARCH => trim(preg_replace('#\x20+#', ' ', $str)),
+            PathType::FILE, PathType::FOLDER => preg_replace('#\x20+#', '-', $str)
+        };
     }
 
     /**
@@ -83,9 +111,9 @@ class DropboxHelper
      * @return string
      */
 
-    public static function makeFilenameSafe($filename)
+    public static function makeFilenameSafe(string $filename): string
     {
-        return DropboxHelper::makeFileFoldernameSafe($filename);
+        return self::makeFileFoldernameSafe($filename, PathType::FILE);
     }
 
     /**
@@ -96,37 +124,36 @@ class DropboxHelper
      * @return string
      */
 
-    public static function makeFoldernameSafe($foldername)
+    public static function makeFoldernameSafe(string $foldername): string
     {
-        return DropboxHelper::makeFileFoldernameSafe($foldername, "folder");
+        return self::makeFileFoldernameSafe($foldername, PathType::FOLDER);
     }
 
     /**
      * makes the search query safe
      *
      * @param  string $query
-     * @return mixed
+     * @return string
      */
-    public static function makeSearchSafe($query)
+    public static function makeSearchSafe(string $query): string
     {
-        return DropboxHelper::makeFileFoldernameSafe($query, "search");
+        return self::makeFileFoldernameSafe($query, PathType::SEARCH);
     }
+
     /**
      * returns "cleanPath" path without the root of the dropbox connection and the file name
      *
-     * @param  string                   $root (unencoded)
-     * @param  string                   $path from dropboxAPI
-     * @param  string                   $type file|folder
-     * @throws InvalidArgumentException
+     * @param  string          $root (unencoded)
+     * @param  string          $path from dropboxAPI
+     * @param  PathType|string $type file|folder
      * @throws Exception
      * @return string
      */
-    public static function makeCleanPath($root, $path, $type)
+    public static function makeCleanPath(string $root, string $path, PathType|string $type): string
     {
-        if ($type !== 'file' && $type !== 'folder') {
-            throw new InvalidArgumentException(
-                "type can only be 'file' or 'folder'"
-            );
+        // Convert string to enum if needed
+        if (\is_string($type)) {
+            $type = PathType::fromString($type);
         }
 
         if (substr($path, 0, 1) !== '/') {
@@ -156,9 +183,10 @@ class DropboxHelper
         $cleanPath = substr_replace($path, "", $pos, \strlen($root));
 
         // only get the path, not the file name
-        if ($type === 'file') {
-            $cleanPath = \dirname($cleanPath);
-        }
+        $cleanPath = match ($type) {
+            PathType::FILE   => \dirname($cleanPath),
+            PathType::FOLDER => $cleanPath
+        };
 
         if ($cleanPath === ".") {
             $cleanPath = "";
@@ -175,17 +203,19 @@ class DropboxHelper
         return $cleanPath;
     }
 
-    public static function humanFileSize($size, $unit = "")
+    public static function humanFileSize(int|float $size, string $unit = ""): string
     {
-        if ((! $unit && $size >= 1 << 30) || $unit == "GB") {
-            return number_format($size / (1 << 30), 2) . "GB";
-        }
-        if ((! $unit && $size >= 1 << 20) || $unit == "MB") {
-            return number_format($size / (1 << 20), 2) . "MB";
-        }
-        if ((! $unit && $size >= 1 << 10) || $unit == "KB") {
-            return number_format($size / (1 << 10), 2) . "KB";
-        }
-        return number_format($size) . " bytes";
+        static $units = [
+            'GB' => 1 << 30,
+            'MB' => 1 << 20,
+            'KB' => 1 << 10,
+        ];
+
+        return match (true) {
+            ((!$unit && $size >= $units['GB']) || $unit === "GB") => number_format($size / $units['GB'], 2) . "GB",
+            ((!$unit && $size >= $units['MB']) || $unit === "MB") => number_format($size / $units['MB'], 2) . "MB",
+            ((!$unit && $size >= $units['KB']) || $unit === "KB") => number_format($size / $units['KB'], 2) . "KB",
+            default                                               => number_format($size) . " bytes"
+        };
     }
 }
