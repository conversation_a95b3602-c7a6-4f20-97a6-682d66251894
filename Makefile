# the path to the root of the extension directory
# the default value set from inside the docker container setup
DROPBOX_EXT_DIR:=/var/www/html/ext/dropbox

# docker image tags
JOOMLA_V4.4_PHP8_1_TAG=4.4-php8.1-apache
JOOMLA_V4.4_PHP8_2_TAG=4.4-php8.2-apache
JOOMLA_V5.1_PHP8_1_TAG=5.1-php8.1-apache
JOOMLA_V5.1_PHP8_2_TAG=5.1-php8.2-apache

FONT_BOLD:=$(shell tput bold)
FONT_RESET:=$(shell tput sgr0)

# docker compose command, defaults to v3; for v2:
# set this as docker-compose using the environment variable
DCO:=docker compose
DOCKER:=docker

.PHONY: help
help:
	$(info List of available commands:)
	$(info ================================)
	$(info )
	$(info 1. $(FONT_BOLD)install$(FONT_RESET)			- install dependencies)
	$(info 2. $(FONT_BOLD)install-prod$(FONT_RESET) - run composer installation without dev dependencies)
	$(info 3. $(FONT_BOLD)clean$(FONT_RESET)			- remove dependencies)
	$(info 5. $(FONT_BOLD)docker-run-with-v4-php8.1$(FONT_RESET)		- run docker with Joomla 4.4 with php version 8.1)
	$(info 6. $(FONT_BOLD)docker-run-with-v4-php8.1-ci$(FONT_RESET)		- run docker with Joomla 4.4 with php version 8.1)
	$(info 7. $(FONT_BOLD)docker-run-with-v4-php8.2$(FONT_RESET)		- run docker with Joomla 4.4 with php version 8.2)
	$(info 8. $(FONT_BOLD)docker-run-with-v4-php8.2-ci$(FONT_RESET)		- run docker with Joomla 4.4 with php version 8.2)
	$(info 9. $(FONT_BOLD)docker-run-with-v5-php8.1$(FONT_RESET)		- run docker with Joomla 5.1 with php version 8.1)
	$(info 10. $(FONT_BOLD)docker-run-with-v5-php8.1-ci$(FONT_RESET)		- run docker with Joomla 5.1 with php version 8.1)
	$(info 11. $(FONT_BOLD)docker-run-with-v5-php8.2$(FONT_RESET)		- run docker with Joomla 5.1 with php version 8.2)
	$(info 12. $(FONT_BOLD)docker-run-with-v5-php8.2-ci$(FONT_RESET)		- run docker with Joomla 5.1 with php version 8.2)
	$(info 13. $(FONT_BOLD)docker-clean$(FONT_RESET) 		- stop and remove docker containers)
	$(info 14. $(FONT_BOLD)docker-update$(FONT_RESET)		- pulls the latest docker images)
	$(info 15. $(FONT_BOLD)code-sniff$(FONT_RESET) 			- run code sniffer)
	$(info 16. $(FONT_BOLD)build$(FONT_RESET) 			- build the extension zip file)
	$(info 17. $(FONT_BOLD)help$(FONT_RESET) 			- show this help)
	$(info 18. $(FONT_BOLD)complete-setup$(FONT_RESET) 			- executes the installation scripts to setup Joomla and the extension)
	$(info )

.PHONY: clean
clean:
	rm -rf site/vendor

.PHONY: install
install:
	composer install

.PHONY: install-prod
install-prod:
	composer install --no-dev

.PHONY: docker-run-with-v4-php8.1
docker-run-with-v4-php8.1:
	JOOMLA_VERSION=$(JOOMLA_V4.4_PHP8_1_TAG) $(DCO) up

.PHONY: docker-run-with-v4-php8.1-ci
docker-run-with-v4-php8.1-ci:
	JOOMLA_VERSION=$(JOOMLA_V4.4_PHP8_1_TAG) $(DCO) up -d
	$(DCO) ps

.PHONY: docker-run-with-v4-php8.2
docker-run-with-v4-php8.2:
	JOOMLA_VERSION=$(JOOMLA_V4.4_PHP8_2_TAG) $(DCO) up

.PHONY: docker-run-with-v4-php8.2-ci
docker-run-with-v4-php8.2-ci:
	JOOMLA_VERSION=$(JOOMLA_V4.4_PHP8_2_TAG) $(DCO) up -d

.PHONY: docker-run-with-v5-php8.1
docker-run-with-v5-php8.1:
	JOOMLA_VERSION=$(JOOMLA_V5.1_PHP8_1_TAG) $(DCO) up

.PHONY: docker-run-with-v5-php8.1-ci
docker-run-with-v5-php8.1-ci:
	JOOMLA_VERSION=$(JOOMLA_V5.1_PHP8_1_TAG) $(DCO) up -d
	$(DCO) ps

.PHONY: docker-run-with-v5-php8.2
docker-run-with-v5-php8.2:
	JOOMLA_VERSION=$(JOOMLA_V5.1_PHP8_2_TAG) $(DCO) up

.PHONY: docker-run-with-v5-php8.2-ci
docker-run-with-v5-php8.2-ci:
	JOOMLA_VERSION=$(JOOMLA_V5.1_PHP8_2_TAG) $(DCO) up -d

.PHONY: docker-clean
docker-clean:
	$(DCO) down -v --remove-orphans --rmi all

docker-update:
	JOOMLA_VERSION=$(JOOMLA_V4.4_PHP8_1_TAG) $(DCO) pull joomla
	JOOMLA_VERSION=$(JOOMLA_V4.4_PHP8_2_TAG) $(DCO) pull joomla
	JOOMLA_VERSION=$(JOOMLA_V5.1_PHP8_1_TAG) $(DCO) pull joomla
	JOOMLA_VERSION=$(JOOMLA_V5.1_PHP8_2_TAG) $(DCO) pull joomla
	$(DCO) pull mysql

.PHONY: cs-fixer
cs-fixer:
	site/vendor/bin/php-cs-fixer fix --allow-risky=yes

.PHONY: cs-checker
cs-checker:
	site/vendor/bin/php-cs-fixer fix --dry-run --diff --allow-risky=yes

.PHONY: code-sniff
code-sniff:
	site/vendor/bin/phpcs -n -p \
		--standard=PSR12 \
		--ignore=site/vendor/*,tests/e2e/*,tests/vendor/* \
		--colors \
		--extensions=php .

code-sniff-fix:
	site/vendor/bin/phpcbf -n -p \
		--standard=PSR12 \
		--ignore=site/vendor/*,tests/e2e/*,tests/vendor/* \
		--colors \
		--extensions=php .

.PHONY: build
build:
	bash -x build/build.sh $(VERSION)

.PHONY: complete-setup
complete-setup:
	npm --prefix $(PWD)/tests/e2e install
	npm run --prefix $(PWD)/tests/e2e setup-joomla
	npm run --prefix $(PWD)/tests/e2e install-extension
