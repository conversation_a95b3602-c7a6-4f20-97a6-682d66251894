const assert = require("assert")
const {getVisibility} = require("../utils.js")
const {When, Then} = require("@cucumber/cucumber")

const DropboxJoomlaHome = require("../pageObjects/dropbox-joomla-home")

When(
    "the user deletes the connection of the {string} folder through the Delete Connection link",
    async function (folderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        await dropboxJoomlaHomeObject.clickOnDeleteConnectionBtn(folderName)
    }
)

Then(
    "the delete connection button for {string} should not be visible",
    async function (folderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const deleteConnectionBtnSelector = dropboxJoomlaHomeObject.getDeleteConnectionBtnSelectorByFolderName(folderName)
        const isVisible = await getVisibility(this.page, deleteConnectionBtnSelector)
        assert.strictEqual(
            isVisible,
            false,
            "Expected the delete connection button to be hidden but it is visible"
        )
    }
)
