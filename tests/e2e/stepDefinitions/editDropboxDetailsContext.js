const {Given, When, Then} = require("@cucumber/cucumber")
const assert = require("assert")
const {getVisibility} = require("../utils.js")
const DropboxEdit = require("../pageObjects/dropbox-edit")
const DropboxView = require("../pageObjects/dropbox-view")
const DropboxJoomlaHome = require("../pageObjects/dropbox-joomla-home")

Given(
    "the connection status for the folder {string} has been set to {string}",
    async function (folderName, connectionStatus) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const actualConnectionStatus = await dropboxJoomlaHomeObject.getConnectionStatusForFolder(folderName)
        assert.strictEqual(
            actualConnectionStatus,
            connectionStatus,
            `Expected connection status to be ${connectionStatus} but found ${actualConnectionStatus}`
        )
    }
)

When(
    "the user renames the folder {string} to {string}",
    async function (folderName, newFolderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        await dropboxJoomlaHomeObject.openEditMenuForFolderByName(folderName)
        await dropboxJoomlaHomeObject.fillFolderNameField(newFolderName)
        await dropboxJoomlaHomeObject.clickOnSaveAndClose()
    }
)

When(
    "the user changes the access type to {string} for the folder {string}",
    async function (accessType, folderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        await dropboxJoomlaHomeObject.openEditMenuForFolderByName(folderName)
        await dropboxJoomlaHomeObject.chooseLabelFromDropdown(
            dropboxJoomlaHomeObject.getEditPageSelectors().accessDropdownSelector,
            accessType
        )
        await dropboxJoomlaHomeObject.clickOnSaveAndClose()
    }
)

When(
    "the user clears the dropbox code section of {string}",
    async function (folderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        await dropboxJoomlaHomeObject.openEditMenuForFolderByName(folderName)
        await this.page.fill(dropboxJoomlaHomeObject.getEditPageSelectors().tokenFieldSelector, "")
        await dropboxJoomlaHomeObject.clickOnSaveAndClose()
    }
)

Then(
    /^the user (should|should not) see the folder "([^"]*)" in the boxes section$/,
    async function (shouldOrShouldNot, folderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        shouldOrShouldNot = shouldOrShouldNot === "should"
        const selectorForFolder = dropboxJoomlaHomeObject.getSelectorForFolder(folderName)
        const isVisible = await getVisibility(this.page, selectorForFolder)
        assert.strictEqual(
            isVisible,
            shouldOrShouldNot,
            `Expected folder '${folderName}'` +
            ` to be ${shouldOrShouldNot ? "visible" : "invisible"}` +
            ` but found '${isVisible ? "visible" : "invisible"}'`
        )
    }
)

Then(
    "the user should see {string} in the access section for the folder {string}",
    async function (accessType, folderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const obtainedAccessType =
            await dropboxJoomlaHomeObject.getAccessTypeForFolder(folderName)
        assert.strictEqual(
            obtainedAccessType,
            accessType,
            `Expected access type for folder ${folderName} to be ${accessType} but found ${obtainedAccessType}`
        )
    }
)

Then(
    "the connection status for the folder {string} should be {string}",
    async function (folderName, connectionStatus) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const status = await dropboxJoomlaHomeObject.getConnectionStatusForFolder(folderName)
        assert.strictEqual(
            status,
            connectionStatus,
            `Expected Connection status to be ${connectionStatus} but found ${status}`
        )
    }
)

Then(
    "the dropbox preview page for {string} should be {string}",
    async function (folderName, authorizationStatus) {
        const dropboxViewObject = new DropboxView(this.page,
            this.folderStore.getFolderId(folderName)
        )

        // preview the page and check if the content loads or not
        await dropboxViewObject.navigateToThePreviewPage()
        const contentLoads = await dropboxViewObject.getVisibilityOfPreviewPageContent()

        // for cases where the authorization status is `authorized`
        if (authorizationStatus === "authorized") {
            assert.strictEqual(
                contentLoads,
                true,
                "Expected content to load but it did not"
            )
            return
        }

        // for cases where the authorization status is `unauthorized`
        if (authorizationStatus === "forbidden") {
            assert.strictEqual(
                contentLoads,
                false,
                "Expected content to not load but it did"
            )

            // check if the error message is displayed
            const errorMessage = await dropboxViewObject.getErrorMessage()
            const validErrorMessageExists = errorMessage.includes(
                dropboxViewObject.getUnauthorizedErrorMessage()
            )

            assert.strictEqual(
                validErrorMessageExists,
                true,
                "Expected error message to be displayed"
            )
            return
        }

        throw new Error(`Unknown authorization status ${authorizationStatus}`)
    }
)

Given("the user has navigated to {string} tab in edit page for folder {string}", async function(tabName, folderName){
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    const dropboxEditObject = new DropboxEdit(this.page)
    await dropboxJoomlaHomeObject.browse()
    await dropboxJoomlaHomeObject.openEditMenuForFolderByName(folderName)
    await dropboxEditObject.clickOnTab(tabName)
})

When("the user sets the date format as {string}", async function(dateFormat){
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    const dropboxEditObject = new DropboxEdit(this.page)
    await dropboxEditObject.fillDateFormatField(dateFormat)
    await dropboxJoomlaHomeObject.clickOnSaveAndClose()
})

When("the user sets the date format as {string} and the timezone as {string}", async function(dateFormat, timezone){
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    const dropboxEditObject = new DropboxEdit(this.page)
    await dropboxEditObject.fillDateFormatField(dateFormat)
    await dropboxEditObject.fillTimezoneField(timezone)
    await dropboxJoomlaHomeObject.clickOnSaveAndClose()
})

When("the user sets the default sorting field as {string}", async function(sortFieldName){
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    const dropboxEditObject = new DropboxEdit(this.page)
    await dropboxEditObject.selectDefaultSortingField(sortFieldName)
    await dropboxJoomlaHomeObject.clickOnSaveAndClose()
})

When("the user sets the default sorting direction to {string}", async function(sortingDirection){
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    const dropboxEditObject = new DropboxEdit(this.page)
    await dropboxEditObject.setDefaultSortingDirection(sortingDirection)
    await dropboxJoomlaHomeObject.clickOnSaveAndClose()
})
