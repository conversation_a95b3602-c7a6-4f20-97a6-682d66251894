const {Given} = require("@cucumber/cucumber")
const assert = require("assert")
const {getAlphaNumeric} = require("../utils")
const Home = require("../pageObjects/home")
const Login = require("../pageObjects/login")

Given("the user has logged in", async function () {
    const homeObject = new Home(this.page)
    const loginObject = new Login(this.page)
    const {id, pswd} = loginObject.getCredentials()
    const expectedTitle = getAlphaNumeric(homeObject.getStaticValues().heading)
    await loginObject.browseToLoginPage()
    await loginObject.login(id, pswd)


    let actualTitle = await homeObject.getPageTitle()
    actualTitle = getAlphaNumeric(actualTitle)

    assert.strictEqual(
        actualTitle,
        expectedTitle,
        `Expected heading ${expectedTitle} not found on the landing page after login\n`
        + `Actual heading: ${actualTitle}\n`
        + `Actual page title: ${await this.page.title()}\n`
        + `Actual page url: ${await this.page.url()}\n`
    )
})
