const assert = require("assert")
const {When, Then} = require("@cucumber/cucumber")
const {existsSync} = require("fs")
const {getVisibility, displayTypeToBool, elementNameToSelector, getInnerText} = require("../utils")
const DropboxJoomlaHome = require("../pageObjects/dropbox-joomla-home")
const DropboxView = require("./../pageObjects/dropbox-view")
const DropboxJoomlaLogs = require("./../pageObjects/dropbox-joomla-logs")
const DropboxEdit = require("./../pageObjects/dropbox-edit")

When(
    "the user changes the {string} parameter to {string}",
    async function (paramName, visibility) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const dropboxEditObject = new DropboxEdit(this.page)
        await dropboxJoomlaHomeObject.chooseLabelFromDropdown(
            elementNameToSelector(paramName, dropboxEditObject, "edit", null),
            visibility
        )
        await dropboxJoomlaHomeObject.clickOnSaveAndClose()
    }
)

Then(
    "the {string} section in the preview page of the folder {string} should be {string}",
    async function (elementName, folderName, visibilityType) {
        const dropboxViewObject = new DropboxView(this.page, this.folderStore.getFolderId(folderName))
        await checkParamVisibility(this.page, elementNameToSelector(elementName, dropboxViewObject, "view", this.folderStore.getFolderId(folderName)), visibilityType)
    }
)

/**
 * Function to check if the parameter's visibility matches the expected value.
 * @param page
 * @param {string} paramSelector - The selector of the parameter to check.
 * @param {string} visibilityType - The expected display type of the parameter.
 */
async function checkParamVisibility(page, paramSelector, visibilityType) {
    const dropboxViewObject = new DropboxView(page)
    assert.strictEqual(
        await dropboxViewObject.getVisibilityOfPreviewPageContent(),
        true,
        "Expected content of page to be loaded successfully, but it was not"
    )
    const paramVisibility = await getVisibility(page, paramSelector)
    assert.strictEqual(
        paramVisibility,
        displayTypeToBool(visibilityType),
        `${paramSelector} expected to be ${visibilityType}, but it was not`
    )
}

Then("the logs for file {string} for folder {string} should be {string}", async function(fileName, folderName, visibilityType){
    const dropJoomlaLogsObject = new DropboxJoomlaLogs(this.page)
    const {formattedFileLogsSelector} = dropJoomlaLogsObject.getFormattedLogsSelector(fileName, folderName)
    const paramVisibility = await getVisibility(this.page, formattedFileLogsSelector)
    assert.strictEqual(
        paramVisibility,
        displayTypeToBool(visibilityType),
        `Logs for ${fileName} expected to be ${visibilityType}, but it was not`
    )
})

Then("the log direction of file {string} for folder {string} should be {string}", async function(fileName, folderName, expectedDirection){
    const dropJoomlaLogsObject = new DropboxJoomlaLogs(this.page)
    const {formattedFileLogsDirectionSelector} = dropJoomlaLogsObject.getFormattedLogsSelector(fileName, folderName)
    const actualDirection = await getInnerText(this.page, formattedFileLogsDirectionSelector)
    assert.strictEqual(
        expectedDirection,
        actualDirection,
        `Expected direction: '${expectedDirection}' but found: '${actualDirection}'`
    )
})

Then("the file {string} should be {string}", async function(fileName, expectedAction){
    if (!["previewed", "downloaded"].includes(expectedAction)) {
        throw new Error(`Expected action to be either 'previewed' or 'downloaded' but got ${expectedAction}`)
    }
    if(expectedAction === "previewed"){
        const currentPageUrl = this.page.url()
        const actualFileName = currentPageUrl.split("&file=")

        assert.strictEqual(
            fileName,
            actualFileName[1],
            "Expected image to be previewed but downloaded"
        )
    }
    if(expectedAction === "downloaded"){
        const dropboxViewObject = new DropboxView(this.page)
        const downloadPath = dropboxViewObject.getFilePath(fileName)
        assert(
            existsSync(downloadPath),
            true,
            "Expected the file to be downloaded but found the result to be false"
        )
        dropboxViewObject.removeDownloadedFile(fileName)
    }
})
