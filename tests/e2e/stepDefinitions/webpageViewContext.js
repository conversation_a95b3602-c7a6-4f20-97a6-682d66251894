const assert = require("assert")
const {expect} = require("@playwright/test")
const {Given, When, Then} = require("@cucumber/cucumber")
const config = require("../config")
const {getAlphaNumeric} = require("../utils")
const {storeAccessTokenForFolder} = require("../databaseHelper/databaseController")
const DropboxAPIHelper = require("../dropboxAPIHelper.js")
const DropboxJoomlaHome = require("../pageObjects/dropbox-joomla-home")
const DropboxView = require("../pageObjects/dropbox-view")
const DropboxEdit = require("../pageObjects/dropbox-edit")

/**
 * Ensures that the user is in the preview page of a connected folder
 *
 * @returns {Promise<void>}
 */
async function thenTheUserShouldBeInThePreviewPage(page) {
    const dropboxViewObject = new DropboxView(page)
    const status = await dropboxViewObject.getVisibilityOfPreviewPageContent()
    assert.ok(
        status,
        "Error while fetching the content of the preview page."
    )
}

/**
 * Confirm that the files on the file listing table
 * are in the expected order
 *
 * @param page
 * @param {Array<string>} expectedOrder - Files in the order they are expected
 *
 * @returns {void}
 */
async function checkTheFileListingOrder(page, expectedOrder){
    const dropboxView = new DropboxView(page)
    const allFilesName = await dropboxView.getAllDisplayedFilesName(page)

    const expectedFiles = expectedOrder.split(", ")
    for (let i = 0; i < expectedFiles.length; i++){
        assert.ok(
            allFilesName[i] === expectedFiles[i],
            `Expected files to be sorted in order ${expectedOrder} but found in order ${allFilesName.join(", ")}`
        )
    }
}

/**
 * Checks whether the sorting icon is ascending or descending
 *
 * @param page
 * @param {string} sortingOrder - ascending or descending
 * @param {string} columnHeading - heading of the column for which the icon is to be checked
 */
async function checkTheSortingIcon(page, sortingOrder, columnHeading){
    const ascRegex = /^(ascending|ASCENDING|asc|ASC)$/
    const descRegex =/^(descending|DESCENDING|desc|DESC)$/

    const dropboxViewObject = new DropboxView(page)
    if (sortingOrder.match(ascRegex)){
        const state = await dropboxViewObject.getAscSortingIconVisibility(columnHeading)
        assert.ok(
            state,
            "Expected the sorting icon order to be ascending but found to be false."
        )
    } else if (sortingOrder.match(descRegex)){
        const state = await dropboxViewObject.getDescSortingIconVisibility(columnHeading)
        assert.ok(
            state,
            "Expected the sorting icon order to be descending but found to be false."
        )
    }else {
        throw new Error("Unsupported sorting order")
    }
}

Given(
    "the user has successfully created a dropbox folder as {string}",
    async function (folderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)

        // the user needs to be in the Dropbox Manager page to execute the step
        await dropboxJoomlaHomeObject.clickOnNewBtn()

        // Checking whether the user sees the content of 'New Dropbox' page
        let actualHeading = await dropboxJoomlaHomeObject.getPageTitle()
        let {headingText: expectedHeading} = dropboxJoomlaHomeObject.getStaticValues()
        expectedHeading = dropboxJoomlaHomeObject.getParsedStaticValues(expectedHeading)
        assert.strictEqual(
            getAlphaNumeric(actualHeading),
            expectedHeading,
            `User expected to be on the ${expectedHeading} page, but found on: ${actualHeading}`
        )

        // fill up folder name
        await dropboxJoomlaHomeObject.fillFolderNameField(folderName)
        // clicking on the save button makes the connect button visible
        await dropboxJoomlaHomeObject.clickOnSaveBtn(this.page)
        // wait whether the connect button to be visible
        await expect(await dropboxJoomlaHomeObject.elementLocator(dropboxJoomlaHomeObject.getEditPageSelectors().connectBtnSelector)).toBeVisible()
        // get the access token here
        const accessToken = await DropboxAPIHelper.fetchAccessToken(config.DROPBOX_REFRESH_TOKEN)
        storeAccessTokenForFolder(accessToken, folderName)
        await dropboxJoomlaHomeObject.reloadPage()
        // check for the preview button to be visible
        await expect(await dropboxJoomlaHomeObject.elementLocator(dropboxJoomlaHomeObject.getEditPageSelectors().previewBtnSelector)).toBeVisible()
        // grab the id from the url
        const id = await dropboxJoomlaHomeObject.getIdFromUrl()
        // close the edit page now
        const closeBtnSelector = dropboxJoomlaHomeObject.getEditPageSelectors().closeBtnSelector
        const closeBtnLocator = await dropboxJoomlaHomeObject.elementLocator(closeBtnSelector)
        await closeBtnLocator.click()
        this.folderStore.addFolder(id, folderName, "public", true)
    }
)

Given(
    "the user has visited the dropbox preview page for the folder {string}",
    async function (folderName) {
        const isFolderCreated = this.folderStore.isFolderCreated(folderName)
        if (!isFolderCreated) {
            throw new Error("Folder has not been created yet. Navigation is not possible.")
        }
        const dropboxViewObject = new DropboxView(this.page,
            this.folderStore.getFolderId(folderName)
        )
        await dropboxViewObject.navigateToThePreviewPage()
        await thenTheUserShouldBeInThePreviewPage(this.page)
    }
)

async function previewPageForFolder(page, folderStore, folderName){
    const dropboxViewObject = new DropboxView(page, folderStore.getFolderId(folderName)
    )
    const isFolderCreated = folderStore.isFolderCreated(folderName)
    if (!isFolderCreated) {
        throw new Error("Folder not yet created")
    }
    await dropboxViewObject.navigateToThePreviewPage()
}

Given("the user has previewed the page for the folder {string} using the webUI",async function(folderName){
    await previewPageForFolder(this.page, this.folderStore, folderName)
})

When(
    "the user previews the page for the folder {string} using the webUI",
    async function (folderName) {
        await previewPageForFolder(this.page, this.folderStore, folderName)
    }
)

Then("the user should be in the preview page", async function () {
    await thenTheUserShouldBeInThePreviewPage(this.page)
})

Then("the modification date for folder {string} on file listing table should be in format {string}", async function(folderName, dateFormat){
    const dropboxViewObject = new DropboxView(this.page, this.folderStore.getFolderId(folderName))
    const actualDateValue = await dropboxViewObject.getModifiedDate()
    const dateFormatRegex = new RegExp(dateFormat)
    assert.ok(
        dateFormatRegex.test(actualDateValue),
        `Expected date format to be in pattern '${dateFormat}' but found to be '${actualDateValue}'`
    )
})

Then("the timezone on dates for folder {string} on the file listing table should be on timezone {string}", async function(folderName, expectedTimezone){
    const dropboxViewObject = new DropboxView(this.page, this.folderStore.getFolderId(folderName))
    const actualDateValue = await dropboxViewObject.getModifiedDate()
    const expectedTimezoneArray = expectedTimezone.split(", ")

    // The reason we're splitting the actualDateValue and grabbing the last item
    // is because the value of the modified date is seen as eg: 2022-01-11 +0545
    // the timezone in date format seen at the last of the date string
    const actualTimezone = actualDateValue.split(" ").pop()
    assert.ok(
        expectedTimezoneArray.includes(actualTimezone),
        `Expected timezone to be one of '${expectedTimezoneArray}' but found to be '${actualTimezone}'`
    )
})

Then("the files on the file listing table should be listed with order {string}", async function(expectedOrder){
    await checkTheFileListingOrder(this.page, expectedOrder)
})

Then("the user should be in the root directory", async function(){
    // root directory is the main preview page for the folder
    await thenTheUserShouldBeInThePreviewPage(this.page)
})

When("the user adds a description for upload as {string} with toggle editor {string}", async function(description, toogleOption){
    const dropboxEditObject = new DropboxEdit(this.page)
    await dropboxEditObject.addDescriptionForUpload(description, toogleOption)
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    await dropboxJoomlaHomeObject.clickOnSaveAndClose()
})

Then("the description for upload in the preview page of the folder {string} should be {string}", async function(folderName, expectedDescription){
    const dropboxViewObject = new DropboxView(this.page, this.folderStore.getFolderId(folderName))
    const actualDescription = await dropboxViewObject.getDescriptionForUpload()
    assert.strictEqual(
        expectedDescription,
        actualDescription,
        `Expected description for upload to be ${expectedDescription} but found ${actualDescription}`
    )
})

Then("the user should see sub-folder {string} in the file listing table", async function(subFolderName){
    const dropboxViewObject = new DropboxView(this.page)
    const allFilesName = await dropboxViewObject.getAllDisplayedFilesName()

    assert.strictEqual(
        allFilesName.includes(subFolderName),
        true,
        `Expected sub-folder ${subFolderName} to be present in file listing table but found to be not present.`
    )
})

Then("the name of the uploaded file for folder {string} on file listing table should be in format {string}", async function(folderName, fileNameFormat){
    const dropboxViewObject = new DropboxView(this.page, this.folderStore.getFolderId(folderName))
    const actualFileName = await dropboxViewObject.getFileName(this.page)
    const fileNameRegex = new RegExp(fileNameFormat)
    assert.ok(
        fileNameRegex.test(actualFileName),
        `Expected name format to be in pattern '${fileNameFormat}' but found to be '${actualFileName}'`
    )
})

Given("the user has logged in with username {string} and password {string}", async function(username, password){
    const dropboxViewObject = new DropboxView(this.page)
    await dropboxViewObject.navigateToLoginPage()
    await dropboxViewObject.login(username, password)
})

When("the user sorts the file listing table by {string}", async function (columnHeading) {
    const dropboxViewObject = new DropboxView(this.page)
    await dropboxViewObject.sortFiles(columnHeading)
})

Then("the sorting icon should be {string} for the column {string}",async function (sortingOrder, columnHeading) {
    await checkTheSortingIcon(this.page, sortingOrder, columnHeading)
})


When("the user searches for {string}", async function (keyword) {
    const dropboxViewObject = new DropboxView(this.page)
    await dropboxViewObject.searchFor(keyword.trim())
})

Then("the file listing table should contain only the following entries {string} after searching", async function (filesList){
    const dropboxViewObject = new DropboxView(this.page)

    // the reset button must be visible before checking search results
    await expect(this.page.locator(dropboxViewObject.getResetSearchButtonSelector())).toBeVisible()

    const allFilesName = await dropboxViewObject.getAllDisplayedFilesName(this.page)
    const filesArray = filesList.split(",").map(element => element.trim())

    // check the items count before checking actual items
    assert.strictEqual(
        filesArray.length,
        allFilesName.length,
        `Expected total count of items in file listing table to be ${filesArray.length} but found to be ${allFilesName.length}`
    )

    // check the actual items
    for (let i = 0; i < filesArray.length; i++){
        assert.ok(
            allFilesName.includes(filesArray[i]),
            `Expected item ${filesArray[i]} to be included in the file listing table but found to be false`
        )
    }
})

Given("the user has created the following sub-folders at once for folder {string} using Dropbox API", async function (parentFolder, dataTable) {
    const folderNameArray = []

    for (let row of dataTable.rows()) {
        folderNameArray.push(`${parentFolder}/${row[0]}`)
    }
    await DropboxAPIHelper.createMultipleFolders(folderNameArray)
})
