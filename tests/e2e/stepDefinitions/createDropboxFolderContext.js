const assert = require("assert")
const { Given, When, Then } = require("@cucumber/cucumber")

const {getVisibility, replaceInLineCode, getSlicedFolderName} = require("../utils.js")
const config = require("../config.js")
const DropboxAPIHelper = require("../dropboxAPIHelper.js")
const {
    storeAccessTokenForFolder,
} = require("../databaseHelper/databaseController.js")
const DropboxJoomlaHome = require("../pageObjects/dropbox-joomla-home")
const DropboxJoomlaLogs = require("../pageObjects/dropbox-joomla-logs")

// declared globally because it's being used by different steps.
let folderNameToCreate = null

Given("the user has browsed to the Dropbox Page", async function () {
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    await dropboxJoomlaHomeObject.browse()

    // checking whether dropbox page is the current page
    let headingText = await dropboxJoomlaHomeObject.getPageTitle()
    let {pageTitle} = dropboxJoomlaHomeObject.getStaticValues()
    pageTitle = dropboxJoomlaHomeObject.getParsedStaticValues(pageTitle)
    headingText = dropboxJoomlaHomeObject.getParsedStaticValues(headingText)
    assert.strictEqual(
        headingText,
        pageTitle,
        "User expected to be on Dropbox Page, but the observed page title do not" +
            " match with the expected page title."
    )
})

When("the user browses to the Dropbox Logs", async function(){
    const dropboxHomeObject = new DropboxJoomlaHome(this.page)
    const dropboxJoomlaLogsObject = new DropboxJoomlaLogs(this.page)
    await dropboxHomeObject.browse()
    await dropboxJoomlaLogsObject.clickOnLogsTab()
})

When("the user browses to the Dropbox Page", async function () {
    const dropboxHomeObject = new DropboxJoomlaHome(this.page)
    await dropboxHomeObject.browse()
})

const createDropboxFolder = async (page, folderName) => {
    const dropboxHomeObject = new DropboxJoomlaHome(page)
    await dropboxHomeObject.clickOnNewBtn()
    // fill up folder name
    await dropboxHomeObject.fillFolderNameField(folderName)
    await dropboxHomeObject.clickOnSaveBtn()
}

const addDropboxFolder = async (page, folderStore, folderName) => {
    folderNameToCreate = folderName
    await createDropboxFolder(page, folderNameToCreate)
    await page.waitForURL("**/administrator/index.php?**layout=edit&id**")
    const pageUrl = await page.url()
    const newId = pageUrl.split("&id=")
    assert.strictEqual(
        newId.length,
        2,
        `Expected a dropbox folder id in the URL but not found.\nURL: ${pageUrl}`
    )
    folderStore.addFolder(newId[1], folderName, "public", false)
}

When(
    "the user creates a new dropbox folder as {string}",
    async function (folderName) {
        const folderNameToCreate = replaceInLineCode(folderName)
        await addDropboxFolder(this.page, this.folderStore, folderNameToCreate)
    }
)

When(
    "the user tries to create a new dropbox folder with a length of 260 characters",
    async function () {
        const dropboxHomeObject = new DropboxJoomlaHome(this.page)
        folderNameToCreate = "a".repeat(260)
        await dropboxHomeObject.clickOnNewBtn()
        // fill up folder name
        await dropboxHomeObject.fillFolderNameField(folderNameToCreate)
    }
)

When(
    "the user tries to create a new dropbox folder as {string}",
    async function (folderNameToCreate) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        await dropboxJoomlaHomeObject.clickOnNewBtn()
        // fill up folder name
        await dropboxJoomlaHomeObject.fillFolderNameField(folderNameToCreate)
    }
)

When("the user saves it without connecting to dropbox", async function () {
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    await dropboxJoomlaHomeObject.clickOnSaveAndClose()
})

When(
    "the user connects to the dropbox account and saves the folder",
    async function () {
        // waiting till the connect button visible
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        await  dropboxJoomlaHomeObject.waitForConnectButton()
        // get the access token here and call the function
        const accessToken = await DropboxAPIHelper.fetchAccessToken(config.DROPBOX_REFRESH_TOKEN)
        await storeAccessTokenForFolder(accessToken, folderNameToCreate)
        // The reason why we're clicking on "Cancel" button but not on the "Save&Close" button
        // is because the access token stored in the database from the function
        // "storeAccessTokenForFolder" would be overwritten to null if we saved the folder.
        await dropboxJoomlaHomeObject.clickOnCancelButton()
        const folderId = await this.folderStore.getFolderId(folderNameToCreate)
        this.folderStore.updateFolder(folderId, {isConnected: true})
    }
)

When("the user tries to create a new folder with space", async function () {
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    await dropboxJoomlaHomeObject.clickOnNewBtn()
    await dropboxJoomlaHomeObject.fillFolderNameField(" ")
})

When("the user provides token {string}", async function (tokenValue) {
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    await dropboxJoomlaHomeObject.fillTokenField(tokenValue)
    await dropboxJoomlaHomeObject.clickOnSaveBtn()
})

Then("the user should see the following error alert message", async function (expectedMessage) {
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    const errorMessage = await dropboxJoomlaHomeObject.getErrorMessageText()
    assert.strictEqual(
        errorMessage.trim(),
        expectedMessage.trim(),
        `Expected error alert message: '${expectedMessage}' but found: '${errorMessage}'`
    )
})

Then(
    "the user {string} see the {string} button in edit page",
    async function (preference, buttonName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const {connectBtnSelector, previewBtnSelector} = dropboxJoomlaHomeObject.getEditPageSelectors()
        let selectorValue = undefined
        let requiredVisibility = undefined
        switch (buttonName) {
        case "Connect":
            selectorValue = connectBtnSelector
            break
        case "Preview":
            selectorValue = previewBtnSelector
            break
        }
        switch (preference) {
        case "should":
            requiredVisibility = true
            break
        case "should not":
            requiredVisibility = false
            break
        }
        if (requiredVisibility) {
            await dropboxJoomlaHomeObject.waitForSelector(selectorValue)
        }
        const buttonVisibility = await getVisibility(this.page, selectorValue)
        assert.strictEqual(
            buttonVisibility,
            requiredVisibility,
            `Expected visibility of button ${buttonName} to be ${requiredVisibility}, but observed visibility is ${buttonVisibility}`
        )
    }
)

Then(
    "the connection status for {string} should be {string}",
    async function (folderName, connectionStatus) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const fName = replaceInLineCode(folderName)
        const slicedFolderName = getSlicedFolderName(fName)

        const receivedData = await dropboxJoomlaHomeObject.getDropboxTableData()
        const expectedFolderId = this.folderStore.getFolderId(fName)
        let folderInformationFromTable = receivedData.filter((item) => item.folderName === slicedFolderName && item.folderId === expectedFolderId)

        if (folderInformationFromTable.length === 0){
            assert.fail("Expected folder not found in the files list\nList contains:\n" + JSON.stringify(receivedData, null, 2))
        } else {
            folderInformationFromTable = folderInformationFromTable[0]
        }
        assert.strictEqual(
            folderInformationFromTable.folderStatus,
            connectionStatus,
            `Status is not as expected.\nExpected status: "${connectionStatus}" but found ${folderInformationFromTable.folderStatus}`
        )
    }
)

Then(
    "the user {string} see the preview button for folder {string}",
    async function (preference, folderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const fName = replaceInLineCode(folderName)
        const slicedFolderName = getSlicedFolderName(fName)
        const shouldOrShouldNot = preference === "should"
        const isVisible = await dropboxJoomlaHomeObject.checkPreviewBtnVisibility(slicedFolderName)
        const expectedVisibility = shouldOrShouldNot ? "visible" : "not visible"
        const actualVisibility = isVisible ? "visible" : "not visible"
        assert.strictEqual(
            isVisible,
            shouldOrShouldNot,
            `Expected Preview button to be "${expectedVisibility}" but found "${actualVisibility}" for the folder ${slicedFolderName}`
        )
    }
)

Then(
    "the user should see the Connect button for {string}",
    async function (folderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const connectBtnVisibility = getVisibility(
            this.page,
            dropboxJoomlaHomeObject.getConnectBtnSelectorByFolderName(folderName)
        )
        assert.ok(
            connectBtnVisibility,
            "Expected Connect button to be visible but found not visible"
        )
    }
)

When(
    "the user creates a new dropbox folder as {string} with the access type {string}",
    async function (folderName, accessType) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        await dropboxJoomlaHomeObject.clickOnNewBtn()
        await dropboxJoomlaHomeObject.fillFolderNameField(folderName)
        await dropboxJoomlaHomeObject.clickOnAccessTypeDropDown()
        await dropboxJoomlaHomeObject.selectOnAccessTypeOption(accessType)
        await dropboxJoomlaHomeObject.clickOnSaveAndClose()
    }
)

Then("the user should see the preview button for both folders {string}", async function (folderName) {
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    const receivedData = await dropboxJoomlaHomeObject.getDropboxTableDataForFolder(folderName)

    assert.deepStrictEqual("object", typeof receivedData, "Expected the received data to be an object")
    assert.deepStrictEqual(2, receivedData.length, "Expected the received data to be an array of length 2")
    // now every thing should be same except folderId
    delete receivedData[0].folderId
    delete receivedData[1].folderId
    assert.deepStrictEqual(
        receivedData[0],
        receivedData[1],
        "Expected both the folder connections to be equivalent but found to be false"
    )
})
