const {Given, When, Then} = require("@cucumber/cucumber")
const assert = require("assert")
const {getAlphaNumeric} = require("../utils.js")
const Login = require("../pageObjects/login")
const Home = require("../pageObjects/home")

Given("the user has browsed to the login page", async function () {
    const loginObject = new Login(this.page)
    await loginObject.browseToLoginPage()
    const isLoginPageVisible = await loginObject.getPageVisibility()
    assert.strictEqual(isLoginPageVisible, true)
})

When(
    "the user tries to log in using username {string} and password {string}",
    async function (username, password) {
        const loginObject = new Login(this.page)
        const {id, pswd} = loginObject.getCredentials()
        if (username === "valid") {
            username = id
            if (password === "valid") {
                password = pswd
            }
        }
        await loginObject.login(username, password)
    }
)

Then("the user should be on the Homepage", async function () {
    const homeObject = new Home(this.page)
    const titleText = getAlphaNumeric(await homeObject.getPageTitle())
    const pageTitle = getAlphaNumeric(homeObject.getStaticValues().heading)
    assert.strictEqual(titleText, pageTitle, "The user expected to be on the Homepage but is not")
})

Then("the user should see the message {string}", async function (errorMsg) {
    const loginObject = new Login(this.page)
    const msgText = await loginObject.getErrorMsg()
    assert.strictEqual(msgText, errorMsg, `Expected error message: ${errorMsg} but found ${msgText}`)
})
