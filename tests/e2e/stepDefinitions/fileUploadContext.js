const assert = require("assert")
const {Given, When, Then} = require("@cucumber/cucumber")
const {existsSync} = require("fs")
const config = require("../config")
const {parseDataTable} = require("../utils")
const DropboxView = require("../pageObjects/dropbox-view")
const DropboxJoomlaHome = require("../pageObjects/dropbox-joomla-home")
const DropboxAPIHelper = require("../dropboxAPIHelper.js")

/**
 * Uploads a single file to the dropbox connected folder using extension UI
 *
 * @param page
 * @param folderId
 * @param subFolder - Name of the sub-folder to create
 * @param fileName - Name of the file to upload
 *
 * @return {Promise<void>}
 */
async function uploadAFileForFolder(page, folderId, subFolder, fileName) {
    const dropboxViewObject = new DropboxView(page, folderId)
    if (subFolder) {
        await dropboxViewObject.fillDestinationFolder(subFolder)
    }
    await dropboxViewObject.uploadSingleFile(fileName)
    await dropboxViewObject.waitForUploadToFinish()
}

/**
 * Asserts the current active directory in the breadcrumbs of the preview page
 *
 * @param page
 * @param folderId
 * @param expectedCurrDir - Expected current directory name
 *
 * @returns {Promise<void>}
 */
async function thenTheCurrentDirectoryShouldBe(page, folderId, expectedCurrDir) {
    const dropboxViewObject = new DropboxView(page,
        folderId
    )
    const actualCurrDir = await dropboxViewObject.getCurrentActiveDirectory()
    assert.strictEqual(
        actualCurrDir,
        expectedCurrDir,
        `Expected current directory to be: "${expectedCurrDir}" but, found to be "${actualCurrDir}"`
    )
}

/**
 * Asserts the presence of the provided file in the files list
 *
 * @param page
 * @param fileName - Name of the file to assert
 *
 * @returns {Promise<void>}
 */
async function thenTheFileShouldBeInFilesList(page, fileName) {
    const dropboxViewObject = new DropboxView(page)
    const isFilesTablePresent = await dropboxViewObject.getFilesTableVisibility()
    assert.strictEqual(isFilesTablePresent, true)
    const actualFilesList = await dropboxViewObject.getAllDisplayedFilesName()
    assert.strictEqual(actualFilesList.includes(fileName), true)
}

/**
 * Uploads multiple files at once for a folder
 *
 * @param page
 * @param folderStore
 * @param {String} folderName - name of the folder
 * @param {Array<String>} dataTable - table containing file names
 */
async function uploadMultipleFilesForFolder(page, folderStore, folderName, dataTable){
    const dropboxViewObject = new DropboxView(page,
        folderStore.getFolderId(folderName)
    )
    const fileNameArray = []
    for (let row of dataTable.rows()) {
        fileNameArray.push(row[0])
    }
    await dropboxViewObject.uploadMultipleFiles(fileNameArray)
    await dropboxViewObject.waitForUploadToFinish()
}

/**
 * Assert the presence of the provided files in the files list
 *
 *
 * @returns {Promise<void>}
 * @param page
 * @param dataTable
 */
async function thenTheFilesShouldBeInFilesList(page, dataTable) {
    const expectedItems  = parseDataTable(dataTable)
    const dropboxViewObject = new DropboxView(page)
    const isFilesTablePresent = await dropboxViewObject.getFilesTableVisibility()
    assert.ok(isFilesTablePresent, "Expected the file to be present in files list but found to be false")
    const actualFilesList = await dropboxViewObject.getAllDisplayedFilesName()
    // Since actual files list may contain other files as well
    // Do not use deepStrictEqual for this case
    for (let item of expectedItems)[
        assert(
            actualFilesList.includes(item),
            true,
            `Expected item ${item} to be in actual files list but found to be false`
        )
    ]
}

Given(
    "the user has created a sub-folder {string} with a file {string} for folder {string}",
    async function (subFolderName, fileName, folderName) {
        await uploadAFileForFolder(this.page, this.folderStore.getFolderId(folderName), subFolderName, fileName)
        await thenTheCurrentDirectoryShouldBe(this.page, this.folderStore.getFolderId(folderName), subFolderName)
        await thenTheFileShouldBeInFilesList(this.page, fileName)
    }
)

Given("the user has uploaded a file {string} for folder {string} using Dropbox API", async function(fileName, folderName){
    await DropboxAPIHelper.uploadSingleFileOnFolder(folderName, fileName)
})

Given("the user has uploaded a file {string} for sub-folder {string} of folder {string} using Dropbox API", async function(fileName, subFolderName, folderName){
    await DropboxAPIHelper.uploadSingleFileOnFolder(`${folderName}/${subFolderName}`, fileName)
})

Given("the user has uploaded a file {string} to the sub-folder named by the user-id of folder {string} using Dropbox API", async function(fileName, folderName){
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    const subFolderName = await dropboxJoomlaHomeObject.getUserId()
    await DropboxAPIHelper.uploadSingleFileOnFolder(`${folderName}/${subFolderName}`, fileName)
})

Given("the user has uploaded the following files at once for folder {string} using Dropbox API", async function(folderName, dataTable){
    const fileNameArray = []

    for (let row of dataTable.rows()) {
        fileNameArray.push(row[0])
    }
    await DropboxAPIHelper.uploadMultipleFileOnFolder(folderName, fileNameArray)
})

When("the user uploads a file {string} for folder {string}",{timeout: config.UPLOAD_TIMEOUT}, async function (fileName, folderName) {
    await uploadAFileForFolder(this.page, this.folderStore.getFolderId(folderName), "", fileName)
})

When("the user uploads following files at once for folder {string}",{timeout: config.UPLOAD_TIMEOUT}, async function (folderName, dataTable) {
    await uploadMultipleFilesForFolder(this.page, this.folderStore, folderName, dataTable)
})

When(
    "the user uploads a file {string} to sub-folder {string} for folder {string}",
    async function (fileName, subFolder, folderName) {
        await uploadAFileForFolder(this.page, this.folderStore.getFolderId(folderName), subFolder, fileName)
    }
)

When(
    "the user uploads a file {string} in the sub-folder {string} for folder {string}",
    async function (fileName, subFolder, folderName) {
        const dropboxViewObject = new DropboxView(this.page,
            this.folderStore.getFolderId(folderName)
        )
        await dropboxViewObject.openSubFolderFromFileList(subFolder)
        await dropboxViewObject.uploadSingleFile(fileName)
        await dropboxViewObject.waitForUploadToFinish()
    }
)

Then(
    "the user should see {string} as the current directory for folder {string}",
    async function (expectedCurrDir, folderName) {
        await thenTheCurrentDirectoryShouldBe(this.page, this.folderStore.getFolderId(folderName), expectedCurrDir)
    }
)

Then(
    "the user should see {string} in the files list",
    async function (fileName) {
        await thenTheFileShouldBeInFilesList(this.page, fileName)
    }
)

Then(
    "the user should see following files in file list",
    async function (dataTable) {
        await thenTheFilesShouldBeInFilesList(this.page, dataTable)
    }
)

Given(
    "the user has successfully uploaded the following files to the folder {string}",{timeout: config.UPLOAD_TIMEOUT},
    async function (folderName,dataTable) {
        // upload files
        await uploadMultipleFilesForFolder(this.page, this.folderStore, folderName, dataTable)
        // assert that files are uploaded
        await thenTheFilesShouldBeInFilesList(this.page, dataTable)
    }
)

When("the user {string} the file {string} from the folder {string}", async function (action, fileName, folderName) {
    if (!["previews", "downloads"].includes(action)) {
        throw new Error(`Expected action to be either 'previews' or 'downloads' but got ${action}`)
    }
    const dropboxViewObject = new DropboxView(this.page,
        this.folderStore.getFolderId(folderName)
    )

    if(action === "previews") {
        await dropboxViewObject.clickOnFile(fileName)
    }
    if(action === "downloads") {
        // download the file
        // which will be saved at /tmp folder
        await dropboxViewObject.downloadSingleFile(fileName)
    }
})


Then("the file {string} should be downloaded", function (fileName) {
    const dropboxViewObject = new DropboxView(this.page)
    const downloadPath = dropboxViewObject.getFilePath(fileName)
    // assert that the file exists
    assert(
        existsSync(downloadPath),
        true,
        "Expected the file to be downloaded but found the result to be false"
    )

    // Remove the downloaded file
    dropboxViewObject.removeDownloadedFile(fileName)
})

Given("the user has uploaded the following files at once for folder {string} sequentially using Dropbox API", async function (folderName, dataTable) {
    const fileNameArray = parseDataTable(dataTable)
    const dropboxViewObject = new DropboxView(this.page,
        this.folderStore.getFolderId(folderName)
    )

    // upload files sequentially
    for (let i = 0;i < fileNameArray.length;i++){
        await DropboxAPIHelper.uploadSingleFileOnFolder(folderName,fileNameArray[i])
        // sleep for 1 sec
        await new Promise(r => setTimeout(r, 1000))

        // assert that file is uploaded
        await dropboxViewObject.navigateToThePreviewPage()
        await thenTheFileShouldBeInFilesList(this.page, fileNameArray[i])
    }
})
