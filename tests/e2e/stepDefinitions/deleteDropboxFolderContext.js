const { When, Then } = require("@cucumber/cucumber")
const { expect } = require("@playwright/test")

const DropboxJoomlaHome = require("../pageObjects/dropbox-joomla-home")

When(
    "the user deletes the folder {string} using the webUI",
    async function (folderName) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        await dropboxJoomlaHomeObject.deleteDropboxFolder(folderName)
    }
)

When("the user deletes all dropbox folders using the webUI", async function () {
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    await dropboxJoomlaHomeObject.deleteAllFolders()
})

Then(
    "the total number of folders in the boxes table should be {int}",
    async function (expectedFolderNumber) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const actualFolderNumber = await dropboxJoomlaHomeObject.getTotalFoldersCount()
        await expect(actualFolderNumber).toBe(expectedFolderNumber)
    }
)

Then(
    "the user should see the following alert message",
    async function (expectedAlertMessage) {
        const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
        const actualAlertMessage = await dropboxJoomlaHomeObject.getAlertMessage(this.page)
        expect(actualAlertMessage).toBe(expectedAlertMessage)
    }
)
