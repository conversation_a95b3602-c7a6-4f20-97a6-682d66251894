const {Then, When} = require("@cucumber/cucumber")
const assert = require("assert")
const { clickOnElement, displayTypeToBool, elementNameToSelector } = require("../utils.js")
const DropboxView = require("../pageObjects/dropbox-view")
const DropboxEdit = require("../pageObjects/dropbox-edit")
const DropboxJoomlaHome = require("../pageObjects/dropbox-joomla-home")

Then(
    "the size of the image in the preview page of {string} should be {string}",
    async function (folderName, thumbnailSize) {
        const dropboxViewObject = new DropboxView(this.page,
            this.folderStore.getFolderId(folderName)
        )
        await dropboxViewObject.navigateToThePreviewPage()

        assert.strictEqual(
            (await dropboxViewObject.getNumberOfImagesInGallery()) > 0,
            true,
            "Expected to have at least 1 image in the gallery but found none"
        )

        // convert 'max. 32px' to '32'
        const match = thumbnailSize.match(/\d+/g)[0]
        thumbnailSize = match ? match : match[0]

        // check if the image height is also given in the thumbnail size, eg: '1024px/768px'
        // Then split the dimensions into width and height [width, height]
        const heightGiven = thumbnailSize.indexOf("/") === -1 ? false : true
        const dimension = thumbnailSize.split("/")
        // fetch dimension
        const {firstImageDimension:observedDimension} = await dropboxViewObject.getImageDimensions()
        assert.strictEqual(
            observedDimension.width <= dimension[0],
            true,
            `Expected image width to be less than or equal to ${dimension[0]} but found ${observedDimension.width}`
        )

        if (heightGiven) {
            assert.strictEqual(
                observedDimension.height <= dimension[1],
                true,
                `Expected image height to be less than or equal to ${dimension[1]} but found ${observedDimension.height}`
            )
        }
    }
)

Then(
    "the title bar of images in the preview page of {string} should be {string}",
    async function (folderName, visibility) {
        const dropboxViewObject = new DropboxView(this.page,
            this.folderStore.getFolderId(folderName)
        )
        await dropboxViewObject.navigateToThePreviewPage()

        await clickOnElement(this.page, dropboxViewObject.getGalleryViewSelectors().firstImageSelector)
        await dropboxViewObject.waitForImageToLoad()

        const obtainedVisibility = await dropboxViewObject.getVisibilityOfTitleBar()
        const expectedVisibility = displayTypeToBool(visibility)

        assert.strictEqual(
            expectedVisibility,
            obtainedVisibility,
            `Expected visibility to be ${expectedVisibility} but found ${obtainedVisibility}`
        )
    }
)

When("the user sets the {string} parameter to {string}", async function(paramName, thumbnailPerRow){
    const dropboxEditObject = new DropboxEdit(this.page)
    const dropboxJoomlaHomeObject = new DropboxJoomlaHome(this.page)
    await dropboxEditObject.fillInputField(
        elementNameToSelector(paramName, dropboxEditObject, "edit", null),
        thumbnailPerRow
    )
    await dropboxJoomlaHomeObject.clickOnSaveAndClose()
})

Then("the number of image thumbnails per row in the preview page of {string} should be {string}", async function(folderName, thumbnailPerRow){
    const dropboxViewObject = new DropboxView(this.page, this.folderStore.getFolderId(folderName))
    await dropboxViewObject.navigateToThePreviewPage()

    assert.strictEqual(
        (await dropboxViewObject.getNumberOfImagesInGallery()) > 0,
        true,
        "Expected to have at least 1 image in the gallery but found none"
    )
    const imagePerRowCount = await dropboxViewObject.getImagesPerRow()

    assert.strictEqual(
        Number(thumbnailPerRow),
        imagePerRowCount,
        `Expected image thumbnails to be ${thumbnailPerRow} per row but found ${imagePerRowCount} per row.`
    )
})

Then("the border thickness of the previewed image of folder {string} should be {string}", async function(folderName, expectedBorderThickness){
    if(expectedBorderThickness < 5 || expectedBorderThickness > 30){
        throw new Error(`Expected border thickness between 5px and 30px but found ${expectedBorderThickness}px`)
    }
    const dropboxViewObject = new DropboxView(this.page,
        this.folderStore.getFolderId(folderName)
    )
    await dropboxViewObject.navigateToThePreviewPage()

    assert.strictEqual(
        (await dropboxViewObject.getNumberOfImagesInGallery()) > 0,
        true,
        "Expected to have atleast 1 image in the gallery but found none"
    )
    const actualBorderThickness = await dropboxViewObject.getBorderThickness()
    assert.strictEqual(
        Number(expectedBorderThickness),
        actualBorderThickness,
        `Expected border thickness to be ${expectedBorderThickness} but found ${actualBorderThickness}`
    )
})

Then("the border color of the previewed image of folder {string} should be {string}", async function(folderName, expectedBorderColor){
    const dropboxViewObject = new DropboxView(this.page,
        this.folderStore.getFolderId(folderName)
    )
    await dropboxViewObject.navigateToThePreviewPage()

    assert.strictEqual(
        (await dropboxViewObject.getNumberOfImagesInGallery()) > 0,
        true,
        "Expected to have at least 1 image in the gallery but found none"
    )
    const actualBorderColor = await dropboxViewObject.getPictureBorderColor()
    assert.strictEqual(
        expectedBorderColor.replace(/^#+/, ""),
        actualBorderColor,
        `Expected image border color to have hex value ${expectedBorderColor} but found #${actualBorderColor}`
    )
})

Then("the images in the preview page of {string} {string} be navigated using the {string}", async function(folderName, expectedAction, toggleTypes){
    const dropboxViewObject = new DropboxView(this.page,
        this.folderStore.getFolderId(folderName)
    )
    if (toggleTypes !== "keys" && toggleTypes !== "mouse"){
        assert.fail("Toggle type not supported")
    }
    await dropboxViewObject.navigateToThePreviewPage()
    assert.strictEqual(
        (await dropboxViewObject.getNumberOfImagesInGallery()) > 1,
        true,
        "Expected to have at least 2 image in the gallery but found none"
    )
    const actualAction = await dropboxViewObject.canNavigatePictureByKeyOrMouse(displayTypeToBool(expectedAction), toggleTypes)
    assert.strictEqual(
        actualAction,
        displayTypeToBool(expectedAction),
        `Expected image navigation to be ${displayTypeToBool(expectedAction)} but found to be ${actualAction}`
    )
})

Then("the color of the title bar of the previewed image of folder {string} should be {string}", async function(folderName, expectedImageTitleColor){
    const dropboxViewObject = new DropboxView(this.page,
        this.folderStore.getFolderId(folderName)
    )
    await dropboxViewObject.navigateToThePreviewPage()

    assert.strictEqual(
        (await dropboxViewObject.getNumberOfImagesInGallery()) > 0,
        true,
        "Expected to have at least 1 image in the gallery but found none"
    )
    const actualImageTitleColor = await dropboxViewObject.getTitleColor()
    assert.strictEqual(
        expectedImageTitleColor.replace(/^#+/, ""),
        actualImageTitleColor,
        `Expected image title color to have hex value #${expectedImageTitleColor} but found #${actualImageTitleColor}`
    )
})
