## Test plan for the extension

## Requirements:

- [ ] Joomla setup installed & running in the host machine
- [ ] The extension is installed in the Joomla setup

## Folder Creation

### Folder Name

1. create a new connection with a different name combination

| scenario                                                                    | expected                                                                    | result |
|-----------------------------------------------------------------------------|-----------------------------------------------------------------------------|--------|
| normal (greater than 0 less than 256)                                       | folder should be created                                                    |        |
| support different languages (Nepali, Arabic, English)                       | folder should be created                                                    |        |
| with zero length then error occur while navigating to connecting to dropbox | input should be marked as required and connect button should not be visible |        |
| folder name greater than 255                                                | throw error, can't be created                                               |        |

1. create a new connection with an already existing folder name

   **Try this one with:**

    - two same folders
    - three same folders

   **Expected:**

    - [ ] folder should be created successfully in Joomla
    - [ ] both Joomla folder instances should be pointing to the same folder at Dropbox
    -

2. rename the new connection folder name successfully

   **Expected:**

    - [ ] folder should be renamed successfully in Joomla
    - [ ] a new folder with the new name should be created at Dropbox
    - [ ] the old folder should still exist at Dropbox

3. create folder with different `Access` options:

   **Expected:**

   Folder should be created with each of the access options

    - [ ] public
    - [ ] guest
    - [ ] Registered
    - [ ] Special
    - [ ] Super User

### Dropbox Code

> #### What is a dropbox code?
>
> Authentication code provided by the dropbox to the user to access the dropbox account.
> Will be provided while creating a new connection for a folder created using the extension.

Go to `Connect` in the folder connection page.

| steps                                                       | expected                                                                  | result |
|-------------------------------------------------------------|---------------------------------------------------------------------------|--------|
| create a new connection with a valid dropbox code           | connection should be created                                              |        |
| try to create a new connection with the old dropbox code    | connection should not be created and a good error message should be shown |        |
| try to create a new connection with the random dropbox code | connection should not be created and a good error message should be shown |        |

## Parameters:

Different parameters can be set while|after creating a folder connection. The various parameter cases are listed below:

### List Files

| config           | expected                                                                                      | result |
|------------------|-----------------------------------------------------------------------------------------------|--------|
| Yes              | The preview website should show the list of files in the dropbox folder                       |        |
| No               | The preview website should not show the list of files in the dropbox folder                   |        |
| List just folder | The preview website should show the list of folders in the dropbox folder                     |        |
| Global           | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

### Upload

| config | expected                                                                                      | result |
|--------|-----------------------------------------------------------------------------------------------|--------|
| Yes    | The user should be able to upload files to the dropbox folder                                 |        |
| No     | The user should not be able to upload files to the dropbox folder                             |        |
| Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

### Show Search Filed

| config | expected                                                                                      | result |
|--------|-----------------------------------------------------------------------------------------------|--------|
| Yes    | The preview website should show the search field                                              |        |
| No     | The preview website should not show the search field                                          |        |
| Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

### View pictures

| config | expected                                                                                      | result |
|--------|-----------------------------------------------------------------------------------------------|--------|
| Yes    | The user should be able to view the pictures in the dropbox folder                            |        |
| No     | The user should not be able to view the pictures in the dropbox folder                        |        |
| Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

### Log Downloads

| config | expected                                                                                      | result |
|--------|-----------------------------------------------------------------------------------------------|--------|
| Yes    | The user should be able to see the download logs in the dropbox manager page                  |        |
| No     | The user should not be able to see the download logs in the dropbox manager page              |        |
| Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

### Log Uploads

| config | expected                                                                                      | result |
|--------|-----------------------------------------------------------------------------------------------|--------|
| Yes    | The user should be able to see the upload logs in the dropbox manager page                    |        |
| No     | The user should not be able to see the upload logs in the dropbox manager page                |        |
| Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

### View Downloads in Browser

| config | expected                                                                                                           | result |
|--------|--------------------------------------------------------------------------------------------------------------------|--------|
| Yes    | should redirect the file to be download to the browser preview page and `Save As` is necessary for actual download |        |
| No     | should download the file selected instantly without redirecting to the preview page                                |        |
| Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used                      |        |

### Create Private User Directories

_Note: Must be logged in as some user from the Joomla login page rather than the test folder homepage. Otherwise `guest`
named folder is created._

| config                                    | expected                                                                                                                        | result |
|-------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------|--------|
| Yes, name the directories by the userID   | The resources uploaded from Joomla website should be uploaded inside the directory with the name of **UserID** in the dropbox   |        |
| Yes, name the directories by the username | The resources uploaded from Joomla website should be uploaded inside the directory with the name of **username** in the dropbox |        |
| No                                        | No any private directory should be allowed                                                                                      |        |
| Global                                    | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used                                   |        |

## List Function

- Show breadcrumbs

| config     | expected                                                                                      | result |
|------------|-----------------------------------------------------------------------------------------------|--------|
| Yes        | Breadcrumbs should be visible in the preview website                                          |        |
| No         | Breadcrumbs should not be visible in the preview website                                      |        |
| Use Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

- Default Sorting Field

| config        | expected                                                                                      | result |
|---------------|-----------------------------------------------------------------------------------------------|--------|
| Name          | Name should be set as the sorting option for the files' table in the preview website          |        |
| Size          | Size should be set with the sorting option for the files' table in the preview website        |        |
| Modified Date | M. Date should be set with sorting option for the files' table in the preview website         |        |
| Use Global    | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

- Default Sorting Direction

| config     | expected                                                                                      | result |
|------------|-----------------------------------------------------------------------------------------------|--------|
| Ascending  | Sorting direction should be set as `Ascending`                                                |        |
| Descending | Sorting direction should be set as `Descending`                                               |        |
| Use Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

- Show File Modification Date

| config     | expected                                                                                      | result |
|------------|-----------------------------------------------------------------------------------------------|--------|
| Yes        | File modification date should be visible in the files table                                   |        |
| No         | File modification date should not be visible in the files table                               |        |
| Use Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

- Show File size

| config     | expected                                                                                      | result |
|------------|-----------------------------------------------------------------------------------------------|--------|
| Yes        | File size should be visible in the files table                                                |        |
| No         | File size should not be visible in the files table                                            |        |
| Use Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

## Upload function

- Change folder after upload

| config     | expected                                                                                      | result |
|------------|-----------------------------------------------------------------------------------------------|--------|
| Yes        | A successful file upload should redirect user inside the provided folder                      |        |
| No         | A successful file upload should not redirect user inside the provided folder                  |        |
| Use Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

- Allow upload into sub folders (works)

| config     | expected                                                                                      | result |
|------------|-----------------------------------------------------------------------------------------------|--------|
| Yes        | File upload inside an existing folder should be allowed                                       |        |
| No         | File upload inside an existing foldoer should not be allowed                                  |        |
| Use Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

- Description for Upload

    - [ ] The provided description text should be visible below the upload button in the preview website

- Add timestamp to the file name

| config     | expected                                                                                      | result |
|------------|-----------------------------------------------------------------------------------------------|--------|
| Prefix     | Uploaded file should have the timestamp appended in the file name as prefix                   |        |
| Suffix     | Uploaded file should have the timestamp prepended in the file name as suffix                  |        |
| No         | Uploaded file should not have the timestamp in the file name                                  |        |
| Use Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

# Pictures Function

- Thumbnail Size

    - [ ] max. 32px
    - [ ] max. 64px
    - [ ] max. 128px
    - [ ] max. 640px/480px
    - [ ] max. 1024px/768px
    - [ ] Use Global

- Show pic title bar

| config     | expected                                                                                      | result |
|------------|-----------------------------------------------------------------------------------------------|--------|
| Yes        | The image preview page should show the image file name                                        |        |
| No         | The image preview page should not show the image file name                                    |        |
| Use Global | The Joomla configuration (navigate Options button in the Dropbox Manager page) should be used |        |

- Color of title bar text
    - [ ] The provided color should be used for the image file name in the image preview page
