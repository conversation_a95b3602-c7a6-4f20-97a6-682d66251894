require("dotenv").config()
const path = require("path")

const joomlaHostUrl = process.env.JOOMLA_HOST_URL || "http://localhost:8080"
// this gives the absolute path of the project tests root
const pwd = process.env.PWD || process.cwd() // PWD is not available on windows

module.exports = {
    TIMEOUT: 20 * 1000,
    LONG_UI_TIMEOUT: 30 * 1000,
    UPLOAD_TIMEOUT: 180 * 1000,
    TEST_ID: process.env.TEST_ID || "test911",
    IS_RUNNING_ON_CI: process.env.CI || false,
    // these path are relative from the tests/e2e folder
    // absolute path can be provided as well using the env variable
    FILES_FOR_UPLOAD: process.env.FILES_FOR_UPLOAD || path.join(pwd, "filesForUpload"),
    REPORT_DIR: process.env.REPORT_DIR || "reports",

    JOOMLA_HOST_URL: new URL(joomlaHostUrl).href,
    JOOMLA_VERSION: process.env.JOOMLA_VERSION || "4",
    SITE_NAME: process.env.SITE_NAME || "Joomla Test Site",
    JOOMLA_ADMIN_REAL_NAME: process.env.JOOMLA_ADMIN_REAL_NAME || "Test Admin",
    JOOMLA_ADMIN_USERNAME: process.env.JOOMLA_ADMIN_USERNAME || "admin",
    JOOMLA_ADMIN_PASSWORD: process.env.JOOMLA_ADMIN_PASSWORD || "admin123456789",
    JOOMLA_ADMIN_EMAIL: process.env.JOOMLA_ADMIN_EMAIL || "<EMAIL>",
    JOOMLA_DB_HOST_FOR_TEST_DB_CONNECTION: process.env.JOOMLA_DB_HOST_FOR_TEST_DB_CONNECTION || "localhost",

    JOOMLA_DB_HOST: process.env.JOOMLA_DB_HOST || "mysql",
    JOOMLA_DB_PORT: process.env.JOOMLA_DB_PORT || "3306",
    JOOMLA_DB_USER: process.env.JOOMLA_DB_USER || "joomla",
    JOOMLA_DB_PASSWORD: process.env.JOOMLA_DB_PASSWORD || "joomla",
    JOOMLA_DB_NAME: process.env.JOOMLA_DB_NAME || "joomla",
    JOOMLA_DB_TABLE_PREFIX: process.env.JOOMLA_DB_TABLE_PREFIX || "jdb_",
    JOOMLA_DB_TABLE: process.env.JOOMLA_DB_TABLE_PREFIX + "dropbox",
    JOOMLA_DB_LOGS_TABLE: process.env.JOOMLA_DB_TABLE_PREFIX + "dropbox_logs",
    JOOMLA_DB_TEST_ID: process.env.JOOMLA_DB_TEST_ID || "911",

    DROPBOX_EMAIL: process.env.DROPBOX_EMAIL,
    DROPBOX_PASSWORD: process.env.DROPBOX_PASSWORD,
    DROPBOX_REFRESH_TOKEN: process.env.DROPBOX_REFRESH_TOKEN,
    DROPBOX_ACCESS_TOKEN: process.env.DROPBOX_ACCESS_TOKEN,
    DROPBOX_CLIENT_ID: process.env.DROPBOX_CLIENT_ID,
    DROPBOX_CLIENT_SECRET: process.env.DROPBOX_CLIENT_SECRET,

    EXTENSION_DIR: process.env.EXTENSION_DIR || "/var/www/html/ext/dropbox"
}
