/**
 * Function to check whether an element is visible or not
 * @param page
 * @param {string} selectorValue - Selector which is used to locate the element
 * @return {Promise<boolean>} returns true if the element is visible
 */
function getVisibility(page, selectorValue)
{
    return page.locator(selectorValue).isVisible()
}

/**
 * @param page
 * Function to find out all the inner-texts from an element
 * @param {string} selectorValue - Selector which is used to locate the element
 * @return {Promise<string>} returns the inner-texts of the element
 */
function getInnerText(page, selectorValue)
{
    return page.locator(selectorValue).innerText()
}

/**
 * Function to remove all white-spaces from a string
 * @param {string} inputString - Input string that is to be formatted
 * @return {string} returns the formatted string
 */
function getAlphaNumeric(inputString)
{
    // /W selects any non-alphanumeric values
    return inputString.replace(/\W/g, "")
}

/**
 * Function to automate click on an element
 * @param page
 * @param {string} selectorValue - Selector which is used to locate the element
 */
async function clickOnElement(page, selectorValue)
{
    const locatorValue = await page.locator(selectorValue)
    await locatorValue.click()
}

/**
 * Function that formats the url
 * @param {string} inputUrl - URL given which could be formatted or not (eg: www.jankaritech.com)
 * @return {string} Formatted url  (eg: http://www.jankaritech.com/)
 */
function urlFormatter(inputUrl)
{
    const httpPos = inputUrl.indexOf("http")

    if (httpPos === -1) {
        inputUrl = `http://${inputUrl}`
    }

    const endChar = inputUrl[inputUrl.length - 1]

    if (endChar !== "/") {
        inputUrl += "/"
    }

    return inputUrl
}

/**
 * Function that matches a certain display type to either true or false
 * @param {string} displayType - Display type that is to be matched
 * @return {boolean} returns a boolean value if display type is matched else returns null
 */
function displayTypeToBool(displayType)
{
    displayType = displayType.trim().toLowerCase()
    const trueVisbility = ["visible", "displayed","show", "yes", "true", "1", "should"]
    const falseVisbility = ["invisible", "hidden","hide", "no", "false", "0", "should not"]

    if (trueVisbility.includes(displayType)) {
        return true
    }

    if (falseVisbility.includes(displayType)) {
        return false
    }

    throw new Error(
        `Undefined display type ${displayType} If you want to hide the element, use ${
            trueVisbility} If you want to show the element, use ${
            falseVisbility}`
    )
}

/**
 * Function to map elements to their respective selectors
 * @param elementName
 * @param {Object} pageObject - Page object of the page containg that element
 * @param {string} pageName - Name of the page
 * @param dropboxId
 */
function elementNameToSelector(elementName, pageObject, pageName, dropboxId)
{
    let mapper = null

    if(pageName === "edit") {
        mapper = {
            "List Files": pageObject.getParametersTabSelectors().listFilesSelector,
            "Upload": pageObject.getParametersTabSelectors().uploadSelector,
            "View Pictures": pageObject.getParametersTabSelectors().viewPicturesSelector,
            "Show search field": pageObject.getParametersTabSelectors().showSearchFieldSelector,
            "Log Downloads": pageObject.getParametersTabSelectors().logDownloadsSelector,
            "Log Uploads": pageObject.getParametersTabSelectors().logUploadsSelector,
            "View Downloads in Browser": pageObject.getParametersTabSelectors().viewDownloadsInBrowserSelector,
            "Create private user directories": pageObject.getParametersTabSelectors().createPrivateUserDirectoriesSelector,

            "Change folder after upload": pageObject.getUploadFunctionTabSelectors().changeFolderAfterUploadSelector,
            "Allow upload into subfolders": pageObject.getUploadFunctionTabSelectors().allowUploadIntoSubfoldersSelector,
            "Add timestamp to the file name": pageObject.getUploadFunctionTabSelectors().addTimestampToTheFileNameSelector,

            "Thumbnail Size" : pageObject.getPicturesFunctionTabSelectors().thumbnailSizeSelector,
            "Thumbnails per row": pageObject.getPicturesFunctionTabSelectors().thumbnailsPerRowSelector,
            "border thickness": pageObject.getPicturesFunctionTabSelectors().borderThicknessSelector,
            "border color": pageObject.getPicturesFunctionTabSelectors().borderColorSelector,
            "Center Enlarged Pic" : pageObject.getPicturesFunctionTabSelectors().centerEnlargedPicOnScreenSelector,
            "Show pic title bar" : pageObject.getPicturesFunctionTabSelectors().showPicTitleBarSelector,
            "Key Navigation" : pageObject.getPicturesFunctionTabSelectors().keyNavigationSelector,
            "mouse wheel navigation" : pageObject.getPicturesFunctionTabSelectors().mouseWheelNavigationSelector,
            "Color of title bar text" : pageObject.getPicturesFunctionTabSelectors().colorOfTitleBarTextSelector,

            "Show breadcrumbs": pageObject.getListFunctionTabSelectors().breadcrumbsSelector,
            "Show File Size": pageObject.getListFunctionTabSelectors().showFileSizeSelector,
            "Show File Modification Date": pageObject.getListFunctionTabSelectors().showFileModificationDateSelector,
        }
    }
    else if(pageName === "view") {
        mapper = {
            "Search": pageObject.getSearchBarSelector(),
            "Upload": pageObject.getUploadFormSelectors().selector,
            "Files list": pageObject.getListViewSelectors().selector,
            "Gallery": pageObject.getGalleryViewSelectors().selector,
            "Breadcrumb": pageObject.getBreadcrumbSelector() + dropboxId + " h1",
            "File Size" : pageObject.getListViewSelectors().fileSizeColumnSelector + dropboxId,
            "Modified Date" : pageObject.getListViewSelectors().fileModificationDateColumnSelector + dropboxId,
            "Destination directory": pageObject.getDestinationFolderInputSelector(),
        }
    }
    const selectorValue = mapper[elementName]
    if (!selectorValue) {
        throw new Error(`${elementName} is not a valid element name`)
    }
    return selectorValue
}


function getSlicedFolderName(folderName) {
    return folderName.slice(0, 255)
}

function replaceInLineCode(str) {
    switch(str) {
    case "%longFolderNameLength%":
        return "a".repeat(260)
    default:
        return str
    }
}

/**
 * Parses the data table and return an array containing table items
 * @param {Array<Aray<String>} dataTable
 * @returns an array containing table items
 */
function parseDataTable(dataTable) {
    let tableContents = []
    for(let row of dataTable.rows()){
        tableContents.push(row[0])
    }
    return tableContents
}


module.exports = {
    getVisibility,
    getInnerText,
    urlFormatter,
    getAlphaNumeric,
    clickOnElement,
    displayTypeToBool,
    elementNameToSelector,
    replaceInLineCode,
    getSlicedFolderName,
    parseDataTable
}
