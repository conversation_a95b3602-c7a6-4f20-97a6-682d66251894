const mysql = require("mysql2")
const config = require("../config")


const connection = mysql.createConnection({
    // for CI or local setup, the db is mapped to the host machine, so we use localhost
    // even if the Joomla is running on apache locally, and we're using local mysql, the
    // db port should be configured to what it is but the host can be localhost
    host: config.JOOMLA_DB_HOST_FOR_TEST_DB_CONNECTION,
    user: config.JOOMLA_DB_USER,
    password: config.JO<PERSON>LA_DB_PASSWORD,
    database: config.JOOMLA_DB_NAME,
    port: config.JOOMLA_DB_PORT
})

const clearJoomlaDatabase = () => {
    connection.query(
        `DELETE FROM ${config.JOOMLA_DB_TABLE}`,
        (e) => {
            if (e) {
                throw e
            }
        }
    )
}

const clearJoomlaLogsDatabase = () => {
    connection.query(
        `DELETE FROM ${config.JOOMLA_DB_LOGS_TABLE}`,
        (e) => {
            if (e) {
                throw e
            }
        }
    )
}

/**
 * Stores 'Access token' for a folder in database
 *
 * @param {string} accessToken - Access token to be stored
 * @param {string} folderName - Folder name for which the access token is to be stored
 *
 * @returns {void}
 */
const storeAccessTokenForFolder = (accessToken, folderName) => {
    connection.query(
        `UPDATE ${config.JOOMLA_DB_TABLE} SET dropbox_token = "${accessToken}",
        dropbox_secret = "not-a-secret-anymore" WHERE folder = "${folderName}"`
    )
}

const endConnection = () => {
    connection.end()
}

module.exports = { clearJoomlaDatabase, storeAccessTokenForFolder, endConnection, clearJoomlaLogsDatabase }
