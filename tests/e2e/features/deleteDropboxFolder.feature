Feature: Delete a dropbox folder in Joomla
    As a user
    I want to delete a dropbox folder through the extension
    so that the deleted folder does not exist

    Background:
        Given the user has logged in
        And the user has browsed to the Dropbox Page
        And the user has successfully created a dropbox folder as "test-folder1"


    Scenario: Deleting a created dropbox folder
        When the user deletes the folder "test-folder1" using the webUI
        Then the user should see the following alert message
            """
            1 box(es) successfully deleted
            """
        And the user should not see the folder "test-folder1" in the boxes section


    Scenario: Deleting all created dropbox folders
        Given the user has successfully created a dropbox folder as "test-folder2"
        When the user deletes all dropbox folders using the webUI
        Then the user should see the following alert message
            """
            2 box(es) successfully deleted
            """
        And the total number of folders in the boxes table should be 0 


    Scenario: Deleting a dropbox folder out of two created dropbox folder
        Given the user has successfully created a dropbox folder as "test-folder2"
        When the user deletes the folder "test-folder1" using the webUI
        Then the user should see the following alert message
            """
            1 box(es) successfully deleted
            """
        And the user should not see the folder "test-folder1" in the boxes section
        And the user should see the folder "test-folder2" in the boxes section  
