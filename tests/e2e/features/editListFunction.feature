Feature: Edit the list function parameter
    As a user
    I want to change my list functions parameter
    So that I can control how the list of files looks in the preview page

    Background:
        Given the user has logged in
        And the user has browsed to the Dropbox Page
        And the user has successfully created a dropbox folder as 'test-folder'
        And the user has navigated to 'List function' tab in edit page for folder 'test-folder'


    Scenario Outline: Changing the parameter
        When the user changes the '<parameter>' parameter to '<options>'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the '<section>' section in the preview page of the folder 'test-folder' should be '<display-status>'
        Examples:
            | parameter                   | section       | options | display-status |
            | Show breadcrumbs            | Breadcrumb    | No      | hidden         |
            | Show breadcrumbs            | Breadcrumb    | Yes     | visible        |
            | Show File Size              | File Size     | No      | hidden         |
            | Show File Size              | File Size     | Yes     | visible        |
            | Show File Modification Date | Modified Date | No      | hidden         |
            | Show File Modification Date | Modified Date | Yes     | visible        |


    Scenario Outline: Setting date format for file modification date
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        When the user sets the date format as '<date-format>'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the modification date for folder 'test-folder' on file listing table should be in format '<regex>'
        Examples:
            | date-format   | regex                                               |
            | Y-m-d         | ^\d{4}-\d{2}-\d{2}$                                 |
            | Y-m-d h:i:s   | ^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}$              |
            | F j, Y, g:i a | ^\w{3,9}\s\d{1,2},\s\d{4},\s\d{1,2}:\d{2}\s\w{2}$   |
            | D M j G:i:s Y | ^\w{3}\s\w{3}\s\d{1,2}\s\d{1,2}:\d{2}:\d{2}\s\d{4}$ |


    Scenario Outline: Setting timezone for file modification date
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        When the user sets the date format as 'Y-m-d h:i:s T' and the timezone as '<timezone>'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the timezone on dates for folder 'test-folder' on the file listing table should be on timezone '<expected-timezone>'
        Examples:
            | timezone                       | expected-timezone |
            | Asia/Kathmandu                 | +0545             |
            | America/Toronto                | EST, EDT          |
            | Europe/Paris                   | CET, CEST         |
            | America/Argentina/Buenos_Aires | -03               |


    Scenario Outline: Setting default sorting field
        Given the user has uploaded the following files at once for folder 'test-folder' using Dropbox API
            | Filename  |
            | file2.png |
            | file1.png |
            | file3.png |
        When the user sets the default sorting field as '<sorting-field-name>'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the files on the file listing table should be listed with order '<expected-order>'
        Examples:
            | sorting-field-name | expected-order                  |
            | Name               | file1.png, file2.png, file3.png |
            | Modified Date      | file2.png, file1.png, file3.png |
            | Size               | file3.png, file1.png, file2.png |


    Scenario Outline: Setting default sorting direction
        Given the user has uploaded the following files at once for folder 'test-folder' using Dropbox API
            | Filename  |
            | file2.png |
            | file1.png |
            | file3.png |
        When the user sets the default sorting direction to '<sorting-direction>'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the files on the file listing table should be listed with order '<expected-order>'
        Examples:
            | sorting-direction | expected-order                  |
            | Ascending         | file1.png, file2.png, file3.png |
            | Descending        | file3.png, file2.png, file1.png |
