Feature: Disconnect dropbox folders from <PERSON><PERSON><PERSON>
    As a user
    I want to remove connections between <PERSON><PERSON><PERSON> and dropbox folders
    So that the disconnected folders can no longer be accessed from <PERSON><PERSON><PERSON>

    Background:
        Given the user has logged in
        And the user has browsed to the Dropbox Page
        And the user has successfully created a dropbox folder as 'test-folder'


    Scenario: Disconnect a dropbox folder from <PERSON><PERSON><PERSON>
        Given the connection status for the folder 'test-folder' has been set to 'Connected'
        When the user deletes the connection of the 'test-folder' folder through the Delete Connection link
        Then the connection status for 'test-folder' should be 'Not Connected'
        And the delete connection button for 'test-folder' should not be visible
        And the user 'should not' see the preview button for folder 'test-folder'

    @notImplementedYet
    Scenario Outline: Folder/s deleted from overview
        When the user removes the connection '<folders-to-delete>'
        Then the corresponding dropbox folder should no longer be accessable by <PERSON><PERSON><PERSON>
        And the list of folders should only contain the '<remaining-folders>'
        Examples:
            | folders-to-delete                 | remaining-folders        |
            | testFolder1                       | secondFolder,thirdOne    |
            | thirdOne                          | testFolder1,secondFolder |
            | secondFolder,thirdOne             | testFolder1              |
            | testFolder1,secondFolder,thirdOne |                          |
