Feature: Download a resource from preview page
    As a user
    I want to download resources from preview page
    So that I can save them in my local storage

    Background:
        Given the user has logged in
        And the user has browsed to the Dropbox Page
        And the user has successfully created a dropbox folder as "test-folder"
        And the user has uploaded the following files at once for folder 'test-folder' using Dropbox API
            | Filename  |
            | file2.png |
            | file3.png |
            | file4.png |
        And the user has visited the dropbox preview page for the folder "test-folder"

    Scenario Outline: Download a resource
        When the user 'downloads' the file "<file-name>" from the folder "test-folder"
        Then the file "<file-name>" should be downloaded
        Examples:
            | file-name |
            | file2.png |
            | file3.png |
            | file4.png |