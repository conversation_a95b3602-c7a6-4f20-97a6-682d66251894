Feature: Edit the upload function parameter
    As a user
    I want to change my upload functions parameter
    So that I can control how the the files can be uploaded

    Background:
        Given the user has logged in
        And the user has browsed to the Dropbox Page
        And the user has successfully created a dropbox folder as 'test-folder'
        And the user has navigated to 'Upload function' tab in edit page for folder 'test-folder'

    Scenario: Changing option for change folder after upload to Yes
        When the user changes the 'Change folder after upload' parameter to 'Yes'
        And the user previews the page for the folder 'test-folder' using the webUI
        And the user uploads a file 'file1.png' to sub-folder 'subFolder' for folder 'test-folder'
        Then the user should see 'subFolder' as the current directory for folder 'test-folder'
        And the user should see 'file1.png' in the files list


    Scenario: Changing option for change folder after upload to No
        When the user changes the 'Change folder after upload' parameter to 'No'
        And the user previews the page for the folder 'test-folder' using the webUI
        And the user uploads a file 'file1.png' to sub-folder 'subFolder' for folder 'test-folder'
        Then the user should be in the root directory
        And the user should see sub-folder 'subFolder' in the file listing table


    Scenario Outline: Changing option for Allow upload into subfolders
        When the user changes the 'Allow upload into subfolders' parameter to '<options>'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the 'Destination directory' section in the preview page of the folder 'test-folder' should be '<display-status>'
        Examples:
            | options | display-status |
            | No      | hidden         |
            | Yes     | visible        |


    Scenario Outline: Adding description for upload
        When the user adds a description for upload as 'demo description' with toggle editor '<options>'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the description for upload in the preview page of the folder 'test-folder' should be 'demo description'
        Examples:
            | options |
            | Off     |
            | On      |

    @issue-183
    Scenario Outline: Changing add timestamp to the file name
        When the user changes the 'Add timestamp to the file name' parameter to '<options>'
        And the user previews the page for the folder 'test-folder' using the webUI
        And the user uploads a file 'file1.png' for folder 'test-folder'
        Then the name of the uploaded file for folder 'test-folder' on file listing table should be in format '<regex>'
        Examples:
            | options | regex                          |
            | No      | ^\w{4}\d{1}.\w{3}$             |
            | Prefix  | ^\d{8}-\d{6}_\w{4}\d{1}.\w{3}$ |
            | Suffix  | ^\w{4}\d{1}_\d{8}-\d{6}.png$   |