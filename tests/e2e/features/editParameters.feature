Feature: Change `Parameters` for a dropbox folder
    As a user
    I want to edit my created dropbox folder's parameters through the extension
    So that I can change the preview page design

    Background:
        Given the user has logged in
        And the user has browsed to the Dropbox Page
        And the user has successfully created a dropbox folder as 'test-folder'

    Scenario Outline: Changing the parameter
        Given the user has navigated to 'Parameters' tab in edit page for folder 'test-folder'
        When the user changes the '<parameter>' parameter to '<options>'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the '<section>' section in the preview page of the folder 'test-folder' should be '<display-status>'
        Examples:
            | parameter         | section    | options | display-status |
            | List Files        | Files list | No      | hidden         |
            | List Files        | Files list | Yes     | visible        |
            | Upload            | Upload     | No      | hidden         |
            | Upload            | Upload     | Yes     | visible        |
            | Show search field | Search     | No      | hidden         |
            | Show search field | Search     | Yes     | visible        |


    Scenario Outline: Changing parameter for View Picture
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        And the user has navigated to 'Parameters' tab in edit page for folder 'test-folder'
        When the user changes the 'View Pictures' parameter to '<options>'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the 'Gallery' section in the preview page of the folder 'test-folder' should be '<display-status>'
        Examples:
            | options | display-status |
            | No      | hidden         |
            | Yes     | visible        |


    Scenario Outline: Checking logs for Logs Downloads
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        And the user has navigated to 'Parameters' tab in edit page for folder 'test-folder'
        When the user changes the 'Log Downloads' parameter to '<options>'
        And the user previews the page for the folder 'test-folder' using the webUI
        And the user 'downloads' the file 'file1.png' from the folder 'test-folder'
        And the user browses to the Dropbox Logs
        Then the logs for file 'file1.png' for folder 'test-folder' should be '<display-status>'
        Examples:
            | options | display-status |
            | No      | hidden         |
            | Yes     | visible        |


    Scenario Outline: Checking logs for Log Uploads
        Given the user has navigated to 'Parameters' tab in edit page for folder 'test-folder'
        When the user changes the 'Log Uploads' parameter to '<options>'
        And the user previews the page for the folder 'test-folder' using the webUI
        And the user uploads a file 'file1.png' for folder 'test-folder'
        And the user browses to the Dropbox Logs
        Then the logs for file 'file1.png' for folder 'test-folder' should be '<display-status>'
        Examples:
            | options | display-status |
            | No      | hidden         |
            | Yes     | visible        |


    Scenario: Checking Logs direction for file Log Downloads
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        And the user has navigated to 'Parameters' tab in edit page for folder 'test-folder'
        When the user changes the 'Log Downloads' parameter to 'Yes'
        And the user previews the page for the folder 'test-folder' using the webUI
        And the user 'downloads' the file 'file1.png' from the folder 'test-folder'
        And the user browses to the Dropbox Logs
        Then the log direction of file 'file1.png' for folder 'test-folder' should be 'down'


    Scenario: Checking Logs direction for Log Uploads
        Given the user has navigated to 'Parameters' tab in edit page for folder 'test-folder'
        When the user changes the 'Log Uploads' parameter to 'Yes'
        And the user previews the page for the folder 'test-folder' using the webUI
        And the user uploads a file 'file1.png' for folder 'test-folder'
        And the user browses to the Dropbox Logs
        Then the log direction of file 'file1.png' for folder 'test-folder' should be 'up'


    Scenario Outline: Changing parameter for View Downloads in Browser
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        And the user has navigated to 'Parameters' tab in edit page for folder 'test-folder'
        When the user changes the 'View Downloads in Browser' parameter to '<options>'
        And the user previews the page for the folder 'test-folder' using the webUI
        And the user '<action>' the file 'file1.png' from the folder 'test-folder'
        Then the file 'file1.png' should be '<expected>'
        Examples:
            | options | action    | expected   |
            | Yes     | previews  | previewed  |
            | No      | downloads | downloaded |


    Scenario Outline: Changing parameter for create private user directories as guest
        Given the user has uploaded a file 'file1.png' for sub-folder 'guest' of folder 'test-folder' using Dropbox API
        And the user has navigated to 'Parameters' tab in edit page for folder 'test-folder'
        When the user changes the 'Create private user directories' parameter to '<options>'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the user should see 'file1.png' in the files list
        Examples:
            | options                                   |
            | Yes, name the directories by the username |
            | Yes, name the directories by the userID   |


    Scenario: Changing parameter for create private user directories as admin
        Given the user has logged in with username 'admin' and password 'admin123456789'
        And the user has uploaded a file 'file1.png' for sub-folder 'admin' of folder 'test-folder' using Dropbox API
        And the user has navigated to 'Parameters' tab in edit page for folder 'test-folder'
        When the user changes the 'Create private user directories' parameter to 'Yes, name the directories by the username'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the user should see 'file1.png' in the files list
    

    Scenario: Changing parameter for create private user directories as user-id
        Given the user has logged in with username 'admin' and password 'admin123456789'
        And the user has uploaded a file 'file1.png' to the sub-folder named by the user-id of folder 'test-folder' using Dropbox API
        And the user has navigated to 'Parameters' tab in edit page for folder 'test-folder'
        When the user changes the 'Create private user directories' parameter to 'Yes, name the directories by the userID'
        And the user previews the page for the folder 'test-folder' using the webUI
        Then the user should see 'file1.png' in the files list