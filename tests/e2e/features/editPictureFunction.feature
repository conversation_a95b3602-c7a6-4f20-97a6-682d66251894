Feature: Change pictures function
    As a user
    I want to change the way images appear in the preview page
    So that I can make my website look better

    Background:
        Given the user has logged in
        And the user has browsed to the Dropbox Page
        And the user has successfully created a dropbox folder as 'test-folder'


    Scenario Outline: Changing the thumbnails size for pictures of the gallery
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        And the user has navigated to 'Pictures function' tab in edit page for folder 'test-folder'
        When the user changes the 'Thumbnail Size' parameter to '<thumbnail-size>'
        Then the size of the image in the preview page of 'test-folder' should be '<thumbnail-size>'
        Examples:
            | thumbnail-size    |
            | max. 32px         |
            | max. 64px         |
            | max. 128px        |
            | max. 640px/480px  |
            | max. 1024px/768px |


    Scenario Outline: Changing the thumbnails per row
        Given the user has uploaded the following files at once for folder 'test-folder' using Dropbox API
            | Filename  |
            | file1.png |
            | file2.png |
            | file3.png |
            | file4.png |
        And the user has navigated to 'Pictures function' tab in edit page for folder 'test-folder'
        When the user sets the 'Thumbnails per row' parameter to '<thumbnails-per-row>'
        Then the number of image thumbnails per row in the preview page of 'test-folder' should be '<thumbnails-per-row>'
        Examples:
            | thumbnails-per-row |
            | 1                  |
            | 2                  |
            | 3                  |
            | 4                  |


    Scenario: Changing the picture border thickness
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        And the user has navigated to 'Pictures function' tab in edit page for folder 'test-folder'
        When the user sets the 'border thickness' parameter to '20'
        Then the border thickness of the previewed image of folder 'test-folder' should be '20'


    Scenario: Changing the picture border color
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        And the user has navigated to 'Pictures function' tab in edit page for folder 'test-folder'
        When the user sets the 'border color' parameter to '#111111'
        Then the border color of the previewed image of folder 'test-folder' should be '#111111'

    @notImplementedYet @issue-57
    Scenario Outline: Centering enlarged pic on screen
        When the user changes the 'Center Enlarged Pic' parameter to '<options>' in the edit page of folder 'test-folder'
        Then the enlarged image in the preview page of 'test-folder' should be '<position>'
        Examples:
            | options | position     |
            | Yes     | centered     |
            | No      | not-centered |


    Scenario Outline: Displaying picture title bar
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        And the user has navigated to 'Pictures function' tab in edit page for folder 'test-folder'
        When the user changes the 'Show pic title bar' parameter to '<options>'
        Then the title bar of images in the preview page of 'test-folder' should be '<display-status>'
        Examples:
            | options | display-status |
            | Yes     | visible        |
            | No      | hidden         |


    Scenario Outline: Toggle key navigation for pictures
        Given the user has uploaded the following files at once for folder 'test-folder' using Dropbox API
            | Filename  |
            | file1.png |
            | file2.png |
        And the user has navigated to 'Pictures function' tab in edit page for folder 'test-folder'
        When the user changes the 'Key Navigation' parameter to '<options>'
        Then the images in the preview page of 'test-folder' '<should-or-should-not>' be navigated using the 'keys'
        Examples:
            | options | should-or-should-not |
            | Yes     | should               |
            | No      | should not           |


    Scenario: Changing color of title bar text
        Given the user has uploaded a file 'file1.png' for folder 'test-folder' using Dropbox API
        And the user has navigated to 'Pictures function' tab in edit page for folder 'test-folder'
        When the user sets the 'Color of title bar text' parameter to '#111111'
        Then the color of the title bar of the previewed image of folder 'test-folder' should be '#111111'


    Scenario Outline: Toggle mouse navigation for pictures
      Given the user has uploaded the following files at once for folder 'test-folder' using Dropbox API
        | Filename  |
        | file1.png |
        | file2.png |
      And the user has navigated to 'Pictures function' tab in edit page for folder 'test-folder'
      When the user changes the 'mouse wheel navigation' parameter to '<options>'
      Then the images in the preview page of 'test-folder' '<should-or-should-not>' be navigated using the 'mouse'
      Examples:
        | options | should-or-should-not |
        | Yes     | should               |
        | No      | should not           |
