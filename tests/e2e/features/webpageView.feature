Feature: Webpage view for connected dropbox folder
    As a user
    I want to preview my web page
    So that I can see contents of my dropbox folder

    Background:
        Given the user has logged in
        And the user has browsed to the Dropbox Page

    Scenario Outline: The preview is successfully loaded
        Given the user has successfully created a dropbox folder as '<folder-name>'
        When the user previews the page for the folder '<folder-name>' using the webUI
        Then the user should be in the preview page
        Examples:
            | folder-name |
            | new-folder  |


    Scenario Outline: Sorting the file listing table
        Given the user has successfully created a dropbox folder as 'test-folder'
        And the user has visited the dropbox preview page for the folder 'test-folder'
        And the user has uploaded the following files at once for folder 'test-folder' sequentially using Dropbox API
            | filename       |
            | smallfile1.jpg |
            | smallfile2.jpg |
            | smallfile3.jpg |
        When the user sorts the file listing table by '<column-heading>'
        Then the sorting icon should be "<first-sorting-icon>" for the column "<column-heading>" 
        And the files on the file listing table should be listed with order '<first-sorting-order>'
        When the user sorts the file listing table by '<column-heading>'
        Then the sorting icon should be "<second-sorting-icon>" for the column "<column-heading>"
        And the files on the file listing table should be listed with order "<second-sorting-order>"
        Examples:
            | column-heading | first-sorting-icon | first-sorting-order                            | second-sorting-icon | second-sorting-order                           |
            | Name           | descending         | smallfile3.jpg, smallfile2.jpg, smallfile1.jpg | ascending           | smallfile1.jpg, smallfile2.jpg, smallfile3.jpg |
            | Modified Date  | ascending          | smallfile1.jpg, smallfile2.jpg, smallfile3.jpg | descending          | smallfile3.jpg, smallfile2.jpg, smallfile1.jpg |
            | Size           | ascending          | smallfile1.jpg, smallfile2.jpg, smallfile3.jpg | descending          | smallfile3.jpg, smallfile2.jpg, smallfile1.jpg |

    @implementManually
    Scenario Outline: Searching for files
        Given the user has successfully created a dropbox folder as 'test-folder'
        And the user has uploaded the following files at once for folder 'test-folder' using Dropbox API
            | filename       |
            | smallfile1.jpg |
            | smallfile2.jpg |
        And the user has visited the dropbox preview page for the folder 'test-folder'
        When the user searches for '<keyword>'
        Then the file listing table should contain only the following entries "<files>" after searching
        Examples:
        | keyword        | files                          |
        | smallfile1.jpg | smallfile1.jpg                 |
        | smallfile2.jpg | smallfile2.jpg                 |
        | .jpg           | smallfile1.jpg, smallfile2.jpg |

    @implementManually
    Scenario Outline: Searching for folders
        Given the user has successfully created a dropbox folder as 'test-folder'
        And the user has created the following sub-folders at once for folder 'test-folder' using Dropbox API
            | sub-folders  |
            | firstFolder  |
            | secondFolder |
        And the user has visited the dropbox preview page for the folder 'test-folder'
        When the user searches for '<keyword>'
        Then the file listing table should contain only the following entries "<folders>" after searching
        Examples:
            | keyword | folders                   |
            | first   | firstFolder               |
            | second  | secondFolder              |
            | Folder  | firstFolder, secondFolder |
