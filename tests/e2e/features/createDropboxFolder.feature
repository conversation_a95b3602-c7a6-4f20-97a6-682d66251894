Feature: Create a dropbox folder in Joomla
  As a user
  I want to create a dropbox folder through the extension and connect it to my dropbox account
  So that I can control my Dropbox from Joomla

  Background:
    Given the user has logged in
    And the user has browsed to the Dropbox Page


  Scenario Outline: Folder created with no connection to dropbox
    When the user creates a new dropbox folder as '<folder-name>'
    And the user saves it without connecting to dropbox
    Then the connection status for '<folder-name>' should be '<connection>'
    And the user should see the Connect button for '<folder-name>'
    And the user 'should not' see the preview button for folder '<folder-name>'
    Examples:
      | folder-name  | connection    |
      | kiran-joomla | Not Connected |


  Scenario Outline: Folder created with connection to dropbox
    When the user creates a new dropbox folder as '<folder-name>'
    And the user connects to the dropbox account and saves the folder
    Then the connection status for '<folder-name>' should be '<connection>'
    And the user 'should' see the preview button for folder '<folder-name>'
    Examples:
      | folder-name  | connection |
      | joomla-kiran | Connected  |


  Scenario: Folder creation with space
    When the user tries to create a new folder with space
    Then the user should see the following error alert message
      """
      The folder name must have 1 to 255 characters
      """
    And the user 'should not' see the 'Connect' button in edit page
    And the user 'should not' see the 'Preview' button in edit page


  Scenario: Folder creation with valid name but empty token
    When the user creates a new dropbox folder as 'my-folder'
    Then the user 'should' see the 'Connect' button in edit page
    And the user 'should not' see the 'Preview' button in edit page


  Scenario: Folder creation with valid name and token
    When the user creates a new dropbox folder as 'my-folder'
    And the user provides token 'my-token-here'
    Then the user 'should' see the 'Preview' button in edit page
    And the user 'should not' see the 'Connect' button in edit page


  Scenario Outline: Folder creation with different languages and special characters
    When the user creates a new dropbox folder as '<folder-name>'
    And the user connects to the dropbox account and saves the folder
    Then the connection status for '<folder-name>' should be '<connection>'
    And the user 'should' see the preview button for folder '<folder-name>'
    Examples:
      | folder-name | connection |
      | नयाँ फोल्डर | Connected  |
      | ملف جديد    | Connected  |
      | 新建文件夹  | Connected  |
      | @#$         | Connected  |


  Scenario: Folder creation with exceeding name length
    When the user tries to create a new dropbox folder with a length of 260 characters
    Then the user should see the following error alert message
      """
      The folder name must have 1 to 255 characters
      """
    And the user 'should not' see the 'Connect' button in edit page
    And the user 'should not' see the 'Preview' button in edit page

  Scenario Outline: Folder creation with invalid characters
    When the user tries to create a new dropbox folder as '<folder-name>'
    Then the user should see the following error alert message
      """
      The folder name must not contain angle brackets, \, /, :, ?, *, ", | or '
      """
    And the user 'should not' see the 'Connect' button in edit page
    And the user 'should not' see the 'Preview' button in edit page
    Examples:
      | folder-name |
      | a<b         |
      | a>b         |
      | a/b         |
      | a\\b        |
      | a:b         |
      | a*b         |
      | a?b         |
      | a"b         |
      | a\|b        |


  Scenario: Folder creation with valid name and token
    When the user creates a new dropbox folder as 'my-folder'
    And the user provides token 'my-token-here'
    Then the user 'should' see the 'Preview' button in edit page
    And the user 'should not' see the 'Connect' button in edit page


  Scenario: Folder creation with name of an existing folder
    Given the user has successfully created a dropbox folder as "my-folder"
    When the user creates a new dropbox folder as "my-folder"
    And the user connects to the dropbox account and saves the folder
    And the user browses to the Dropbox Page
    Then the user should see the preview button for both folders "my-folder"


  Scenario Outline: Folder creation with different access options
    When the user creates a new dropbox folder as '<folder-name>' with the access type '<access-type>'
    Then the user should see '<access-type>' in the access section for the folder '<folder-name>'
    Examples:
      | folder-name | access-type |
      | my-folder1  | Public      |
      | my-folder2  | Guest       |
      | my-folder1  | Registered  |
      | my-folder1  | Special     |
      | my-folder1  | Super Users |

