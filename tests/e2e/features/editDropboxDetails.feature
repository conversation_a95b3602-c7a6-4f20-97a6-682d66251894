Feature: Edit a dropbox folder details in <PERSON><PERSON><PERSON>
    As a user
    I want to edit my created dropbox folder through the extension
    So that I can make changes

    Background:
        Given the user has logged in
        And the user has browsed to the Dropbox Page


    Scenario Outline: Rename the folder
        Given the user has successfully created a dropbox folder as '<folder-name>'
        When the user renames the folder '<folder-name>' to '<folder-new-name>'
        Then the user should see the folder "<folder-new-name>" in the boxes section
        Examples:
            | folder-name    | folder-new-name |
            | test-folder    | renamed-folder  |
            | एक्स्पेरिमेन्ट        | नयाँनाम            |


    Scenario Outline: Changing access type
        Given the user has successfully created a dropbox folder as '<folder-name>'
        When the user changes the access type to '<new-access-type>' for the folder '<folder-name>'
        Then the user should see '<new-access-type>' in the access section for the folder '<folder-name>'
        And the dropbox preview page for '<folder-name>' should be '<authorization>'
        Examples:
            | folder-name    | new-access-type | authorization |
            | test-folder    | Registered      | forbidden     |
            | एक्स्पेरिमेन्ट        | Guest           | authorized    |
            | test-folder    | Public          | authorized    |
            | एक्स्पेरिमेन्ट        | Super Users     | forbidden     |
            | test-folder    | Special         | forbidden     |


    Scenario Outline: Removing Dropbox Code - revoking dropbox connection
        Given the user has successfully created a dropbox folder as '<folder-name>'
        And the connection status for the folder '<folder-name>' has been set to 'Connected'
        When the user clears the dropbox code section of '<folder-name>'
        Then the connection status for the folder '<folder-name>' should be 'Not Connected'
        Examples:
            | folder-name |
            | test-folder |
