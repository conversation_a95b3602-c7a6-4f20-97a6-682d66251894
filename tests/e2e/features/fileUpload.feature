Feature: Upload files in the dropbox preview page
    As a user
    I want to upload files
    So that I can make them available to other users

    Background:
        Given the user has logged in
        And the user has browsed to the Dropbox Page
        And the user has successfully created a dropbox folder as 'test-folder'
        And the user has visited the dropbox preview page for the folder 'test-folder'


    Scenario Outline: Upload a single file only
        When the user uploads a file '<file-name>' for folder 'test-folder'
        Then the user should see '<file-name>' in the files list
        Examples:
            | file-name |
            | file1.png |


    Scenario: Uploading multiple files at once
        When the user uploads following files at once for folder 'test-folder'
            | Filename  |
            | file2.png |
            | file3.png |
            | file4.png |
        Then the user should see following files in file list
            | Filename  |
            | file2.png |
            | file3.png |
            | file4.png |


    Scenario: Upload a file into sub-folder by typing the name of subfolder
        When the user uploads a file "file1.png" to sub-folder "subFolder" for folder "test-folder"
        Then the user should see 'subFolder' as the current directory for folder "test-folder"
        And the user should see 'file1.png' in the files list


    Scenario Outline: Upload a file by navigating inside a sub-folder
        Given the user has created a sub-folder 'subFolder' with a file 'file1.png' for folder "test-folder"
        When the user uploads a file '<file-name>' for folder 'test-folder'
        And the user should see '<file-name>' in the files list
        Examples:
            | file-name |
            | file2.png |
