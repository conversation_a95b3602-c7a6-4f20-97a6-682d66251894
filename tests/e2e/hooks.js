const util = require("util")
const config = require("./config")
const {chromium} = require("playwright")
const {expect} = require("@playwright/test")
const {endConnection, clearJoomlaDatabase, clearJoomlaLogsDatabase} = require("./databaseHelper/databaseController")
const {setDefaultTimeout, BeforeAll, Before, After, AfterAll, setWorldConstructor} = require("@cucumber/cucumber")
const World = require("./environment/world")

global.expect = expect
global.format = util.format
let browser

setDefaultTimeout(config.TIMEOUT)
setWorldConstructor(World)

BeforeAll(async function () {
    browser = await chromium.launch({
        headless: true, channel: "chrome",
    })
})

Before(async function () {
    await this.createContext(browser)
})

After(async function (scenarioContext) {
    await this.closeContext(scenarioContext)
    clearJoomlaDatabase()
    clearJoomlaLogsDatabase()
    this.deleteFolderStore()
})

AfterAll(async function () {
    await browser.close()
    endConnection()
})
