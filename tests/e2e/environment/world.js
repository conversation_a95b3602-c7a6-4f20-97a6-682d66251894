const { World: CucumberWorld } = require("@cucumber/cucumber")
const CreatedFoldersStore = require("../store/createdFolders")
const config = require("../config")
const path = require("path")
const { unlinkSync } = require("fs")
const DropboxAPIHelper = require("../dropboxAPIHelper")

class World extends CucumberWorld {
    context
    browser
    page
    folderStore

    constructor(options){
        super(options)
        this.context = null
        this.page = null
    }

    async createContext(browser){
        this.context = await browser.newContext()
        this.folderStore = new CreatedFoldersStore()
        if (config.IS_RUNNING_ON_CI) {
            await this.context.tracing.start({screenshots: true, snapshots: true, sources: true})
        }
        this.page = await this.context.newPage()
    }

    async closeContext(scenarioContext){
        if (config.IS_RUNNING_ON_CI) {
            const tracingZipPath = path.join(
                config.REPORT_DIR,
                "tests", `v${config.JOOMLA_VERSION}`,
                `${scenarioContext.pickle.name.replace(/\s+/g, "-")}-${scenarioContext.pickle.id}.zip`
            )
            await this.context.tracing.stop({
                path: tracingZipPath
            })
            if(scenarioContext.result.status === "PASSED"){
                unlinkSync(tracingZipPath)
            }
        }
        await this.page.close()
        await this.context.close()
        const createdFolders = this.folderStore.getFolders()
        for (let i = 0; i<this.folderStore.getCount(); i++) {
            if(createdFolders[i].isConnected){
                await DropboxAPIHelper.deleteFolder(createdFolders[i].name)
            }
        }
    }

    deleteFolderStore(){
        delete this.folderStore
    }
}

module.exports = World
