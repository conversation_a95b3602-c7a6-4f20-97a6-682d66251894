const {format} = require("util")
const config = require("../../config")
const {getVisibility, getInnerText, clickOnElement, getAlphaNumeric} = require("../../utils")

const staticValues = {
    heading: "Dropbox: [New]",
    homeHeading: "Dropbox Manager",
}
const titleSelector = "//h1[@class='page-title']"
const toolbar = {
    newBtn: "#toolbar-new button",
    editBtn: "#toolbar-edit button",
    deleteBtn: "#toolbar-delete button",
    options: "#toolbar-options button",
}
const editPage = {
    previewBtnSelector: "//button[text()[contains(.,'Preview')]]",
    closeBtnSelector: "//button[contains(@class, 'danger') and contains(., 'Close')]",
    errorAlertMessageSelector: "//joomla-alert[@type='danger']//div[@class='alert-message']",
    tokenFieldSelector: "#jform_dropbox_secret",
    folderNameSelector: "#jform_folder",
    saveAndCloseBtnSelector: "#toolbar-save button",
    cancelBtnSelector: "#toolbar-cancel",
    connectBtnSelector: "#toolbar-arrow-right-4 button",
    formTitleSelector: "//h1[@class='page-title']/small",
    saveBtnSelector: "#toolbar-apply button",

    parameters: {
        selector: "#link-form li a[href='#GENERAL']",
        // <select id ='*'> tags
        listSelector: "#jform_params_function_list",
        showSearchSelector: "#jform_params_show_search",
        uploadSelector: "#jform_params_function_upload",
        viewPicturesSelector: "#jform_params_function_pic",
        logDownloadsSelector: "#jform_params_log_downloads",
        logUploadsSelector: "#jform_params_log_uploads",
        viewDownloadInBrowserSelector:
            "#jform_params_view_downloads_in_browser",
        createPrivateUserDirectorySelector:
            "#jform_params_private_user_directories",

        // input fields
        maxSizeOfCacheSelector: "#jform_params_max_cache_size",
        maxFileSizeInCacheSelector: "#jform_params_max_filesize_in_cache",
        emailNotificationSenderAddressSelector:
            "#jform_params_notification_email_sender_address",
        emailNotificationRecipientAddressSelector:
            "#jform_params_notification_email_recipient_address",
    },

    picturesFunction: {
        // <select id ='*'> tags
        thumbSizeSelector: "#jform_params_th_size_pic", // options available : 6
        useRoundBorderSelector: "#jform_params_enl_brdround", // options available : 3
        animationSelector: "#jform_params_enl_ani", // options available : 7
        glideTransparencySelector: "#jform_params_enl_opaglide", // options available : 3
        showUnderBorderSelector: "#jform_params_enl_shadow", // options available : 3
        darkenScreenSelector: "#jform_params_enl_dark", // options available : 4
        centerEnlargedPicSelector: "#jform_params_enl_center", // options available : 3
        enableDragAndDropSelector: "#jform_params_enl_drgdrop", // options available : 3
        preloadPicSelector: "#jform_params_enl_preload", // options available : 3
        showPicTitleSelector: "#jform_params_enl_titlebar", // options available : 3
        keyNavSelector: "#jform_params_enl_keynav", // options available : 3
        mouseWheelNavSelector: "#jform_params_enl_wheelnav", // options available : 3

        // input fields
        thumbPerRowSelector: "#jform_params_th_per_row",
        borderThicknessSelector: "#jform_params_enl_brdsize",
        borderColorSelector: "jform_params_enl_brdcolor",
        borderBgPicSelector: "jform_params_enl_brdbck",
        // following element is labeled as `ani steps (10-30)`
        stepsSelector: "#jform_params_enl_maxstep",
        timeBetweenStepSelector: "#jform_params_enl_speed",
        shadowSizeSelector: "#jform_params_enl_shadowsize",
        shadowColorSelector: "jform_params_enl_shadowcolor",
        shadowIntensitySelector: "#jform_params_enl_shadowintens",
        darknessValueSelector: "#jform_params_enl_darkprct",
        darknessTimeStepSelector: "#jform_params_enl_darksteps",
        titleBarColorSelector: "#jform_params_enl_titletxtcol",
    },

    listFunction: {
        // <select id ='*'> tags
        breadcrumbsSelector: "#jform_params_show_breadcrumbs",
        showSearchSelector: "#jform_params_show_search",
        showFileModificationDateSelector:
            "#jform_params_list_show_modified_date",
        showFileSizeSelector: "#jform_params_list_show_size",

        // input fields
        dateFormatSelector: "#jform_params_date_format",

        //radio buttons
        sortingFieldSelector: "#jform_params_list_sorting_field",
        sortingDirectionSelector: "#jform_params_list_sorting_dir",
    },

    accessDropdownSelector: "#jform_access",
}

const boxes = {
    // is not optimal beause sometimes, the folder name gets lots of spaces added at the end
    folderCheckboxSelector: "//a[contains(text(), '%s')]/ancestor::tr/td/input",
    folderSelectorByName: "//a[contains(text(), '%s')]",
    rowSelectorByName: "//a[contains(text(), '%s')]/ancestor::tr",
    rowSelectorById: "//input[@value='%s']/ancestor::tr",
    connectionSelector: "/td[contains(text(),'Connected')]", // this is to be appended after the row selection
    previewSelector: "//span/parent::a",
    accessTypeViewSelector: "/td[@class='small folder-access'][last()]",
    deleteConnectionBtnSelector: "//a[contains(text(), '%s')]/ancestor::tr//td/a[contains(text(), 'Delete Connection')]",
    connectBtnSelector: "//a[contains(text(), '%s')]/ancestor::tr//td/a[contains(text(), 'Connect')]",
    accessTypeDropDownSelector: "#jform_access_chosen",
    accessTypeFieldSelector: "//div[@id='jform_access_chosen']//ul[contains(@class,'chosen-results')]/li[contains(text(),'%s')]",
    alertMessage: "//div[@class='alert-wrapper']/div[@class='alert-message']",
}

const tables = {
    allItemsCheckboxSelector: "//th/input[@type='checkbox']",
    folderCheckboxSelector: "//a[contains(text(), '%s')]/ancestor::tr/td/input",
    folderCountSelector: "//table/tbody/tr/td[@class='small folder-name']/a",
    folderIdSelector: "(//table/tbody/tr/td[@class='folder-id'])[%s]",
    folderNameSelector: "(//table/tbody/tr/td[@class='small folder-name']/a)[%s]",
    folderStatusSelector: "(//table/tbody/tr/td[@class='small folder-status'])[%s]",
    folderAccessSelector: "(//table/tbody/tr/td[@class='small folder-access'])[%s]",
    folderActionSelector: "(//table/tbody/tr/td[@class='center folder-action'])[%s]",
    folderPreviewSelector: "(//table/tbody/tr/td[@class='center folder-preview']/a)[%s]",
    itemsCountSelector: "//tfoot/tr/td[contains(text(), 'Total:')]",
}

// selectors for users table
const tableHeadSelector = "//table[@id='userList']//thead//tr//th"
const indexSelector = "//table[@id='userList']//tbody//tr//td[contains(text(), 'admin')]//ancestor::table//thead//tr//th[%s]"
const userIdSelector = "//table[@id='userList']//tr//td[contains(text(), 'admin')]//ancestor::tr//td[%s]"

// click on `New` button
const clickOnNewBtn = async (page) => {
    await clickOnElement(page, toolbar.newBtn)
}


/**
 * Function to fill 'folder name' in the folder field
 * @param page
 * @param {string} folderName - Name of the folder
 */
const fillFolderNameField = async(page, folderName) => {
    await page.fill(editPage.folderNameSelector, folderName)
    await page.locator(editPage.folderNameSelector).blur()
}

const fillTokenField = async(page, tokenValue) => {
    await page.fill(editPage.tokenFieldSelector, tokenValue)
}

const clickOnSaveAndClose = async(page) => {
    await clickOnElement(page, editPage.saveAndCloseBtnSelector)
}

const clickOnSaveBtn = async(page) => {
    await clickOnElement(page, editPage.saveBtnSelector)
}


const clickOnCancelButton = async(page) => {
    await page.locator(editPage.cancelBtnSelector).click()
}


/**
 * Function to get the selector for an entire row using the folder name
 * @param {string} folderName - Name of folder
 * @return {string} Selector value for the row containing folder name
 */
const getRowSelectorByFolderName = (folderName) => {
    return format(boxes.rowSelectorByName, folderName)
}

const getCheckboxSelectorByFolderName = (folderName) => {
    return format(boxes.folderCheckboxSelector, folderName)
}

const getDeleteConnectionBtnSelectorByFolderName = (folderName) => {
    return format(boxes.deleteConnectionBtnSelector, folderName)
}

/**
 * Function to get the selector for an entire row using the folder id
 * @param {number} folderId - Id of the folder
 * @return {string} Selector value for the row containing folder id
 */
const getRowSelectorById = (folderId) => {
    return format(boxes.rowSelectorById, folderId)
}

const getConnectBtnSelectorByFolderName = (folderName) => {
    return format(boxes.connectBtnSelector, folderName)
}


/**
 * Function to check the visibility of preview button for a particular folder
 * @param page
 * @param {string} folderName - Name of the folder
 * @return {Promise<boolean>} Returns visibility of the preview button
 */
const checkPreviewBtnVisibility = async(page, folderName) => {
    const rowSelector = getRowSelectorByFolderName(folderName)
    return await checkPreviewBtnFromRow(page, rowSelector)
}

/**
 * Function to choose a label in a dropdown
 * @param page
 * @param selectorValue
 * @param {string} labelValue - Label that is desired to be selected
 */
const chooseLabelFromDropdown = async(page, selectorValue, labelValue) => {
    const locatorValue = await page.locator(selectorValue)
    await locatorValue.selectOption({label: labelValue}, {force: true})
}


/**
 * Function to check the visibility of preview button for a particular folder in a row
 * @param page
 * @param {string} rowSelector - Selector value for a particular row
 * @return {Promise<boolean>} Returns visibility of the preview button in that row
 */
const checkPreviewBtnFromRow = async(page, rowSelector) => {
    const previewBtnSelector = rowSelector + boxes.previewSelector
    return await getVisibility(page, previewBtnSelector)
}

/**
 * Function to get the access type by folder name
 * @param page
 * @param {string} folderName - Name of the folder
 * @return {string} Access type for that folder
 */
const getAccessTypeForFolder = async(page, folderName) => {
    const rowSelector = format(boxes.rowSelectorByName, folderName)
    const selectorValue = rowSelector + boxes.accessTypeViewSelector
    return await getInnerText(page, selectorValue)
}

/**
 * Function to get the connection status (Connected or Not Connected) from an existing row
 * @param page
 * @param {string} rowSelector - Selector value for the entire row containing the folder name
 * @return {Promise<string>} Connection status
 */
const getConnectionStatusForRow = async(page, rowSelector) => {
    const connectionStatusSelector =
        rowSelector + boxes.connectionSelector
    return await getInnerText(page, connectionStatusSelector)
}

/**
 * Function to get the connection status (Connected or Not Connected) for a folder name
 * @param page
 * @param {string} folderName - Name of the folder
 * @return {Promise<string>} Connection status
 */
const getConnectionStatusForFolder = async(page, folderName) => {
    const rowSelector = getRowSelectorByFolderName(folderName)
    return await getConnectionStatusForRow(page, rowSelector)
}

const getPageTitle = async(page) => {
    return await getInnerText(page, titleSelector)
}

/**
 * Function to open the edit menu for a folder
 * Requirement: Should be in the dropbox manager page
 * @param page
 * @param {string} folderName - Name of the folder
 */
const openEditMenuForFolderByName = async(page, folderName)=> {
    const selectorForFolder = format(
        boxes.folderSelectorByName,
        folderName
    )
    await clickOnElement(page, selectorForFolder)
}

/**
 * Requirement: Should be in the dropbox manager page
 */
const clickOnDeleteConnectionBtn = async(page, folderName)=> {
    await page.click(format(boxes.deleteConnectionBtnSelector, folderName))
}

const getDropboxTableData = async(page)=> {
    const tData = []
    // table rows array
    const nameColsCount = await page.locator("//table/tbody/tr/td[@class='small folder-name']/a").count()
    for (let i = 1; i <= nameColsCount; i++) {
        const fId = await page.locator(format(tables.folderIdSelector, i)).innerText()
        const fName = await page.locator(format(tables.folderNameSelector, i)).innerText()
        const fStatus = await page.locator(format(tables.folderStatusSelector, i)).innerText()
        const fAccess = await page.locator(format(tables.folderAccessSelector, i)).innerText()
        const fAction = await page.locator(format(tables.folderActionSelector, i)).innerText()
        const fPreview = await page.locator(format(tables.folderPreviewSelector, i)).isVisible()
        tData.push({
            folderId: fId,
            folderName: fName,
            folderStatus: fStatus,
            folderAccess: fAccess,
            folderAction: fAction,
            isFolderPreviewLinkPresent: fPreview
        })
    }
    return tData
}


/**
 * @param page
 * @param {String} folderName
 *
 * @returns table data for the provided folder name
 */
const getDropboxTableDataForFolder = async(page, folderName)=> {
    return (await getDropboxTableData(page)).filter(item => item.folderName === folderName)
}

/**
 * Clicks on access type dropdown to show the different access type options
 */
const clickOnAccessTypeDropDown = async(page)=> {
    await page.click(boxes.accessTypeDropDownSelector)
}

/**
 * Clicks on the specified access type option from the dropdown
 *
 * @param page
 * @param {String} accessType - The access type
 */
const selectOnAccessTypeOption = async(page, accessType)=> {
    await page.click(format(boxes.accessTypeFieldSelector, accessType))
}

const deleteDropboxFolder = async(page, folderName)=> {
    const selectorForCheckbox = getCheckboxSelectorByFolderName(folderName)
    await page.locator(selectorForCheckbox).check()
    await page.locator(toolbar.deleteBtn).click()
}


const deleteAllFolders = async(page)=> {
    await page.locator(tables.allItemsCheckboxSelector).click()
    await page.locator(toolbar.deleteBtn).click()
}

const getTotalFoldersCount = async(page)=> {
    const actualText = await page.locator(tables.itemsCountSelector).innerText()
    return parseInt(actualText.split(":")[1].trim())
}

const getAlertMessage = async(page)=> {
    return await page.locator(boxes.alertMessage).innerText()
}

const browse = async(page) => {
    const url = new URL("/administrator/index.php?option=com_dropbox", config.JOOMLA_HOST_URL)

    // get the current page url
    const currentPageUrl = await page.url()
    if (currentPageUrl === url.href) return
    await page.goto(url.href)
}

/**
 * Returns the userID from the manage users page
 */
const getUserId = async(page) => {
    const url = new URL("/administrator/index.php?option=com_users&view=users", config.JOOMLA_HOST_URL)
    await page.goto(url.href)

    const colsCount = await page.locator(tableHeadSelector).count()
    let index
    for(let i = 1; i <= colsCount; i++){
        const innerText = await page.locator(format(indexSelector, i)).innerHTML()
        if(innerText.includes("ID")){
            index = i
            break
        }
    }
    return await getInnerText(page, format(userIdSelector, index))
}

const getErrorMessageText = async(page) => {
    return await getInnerText(page, format(editPage.errorAlertMessageSelector))
}

const getStaticValues = () => {
    return {
        pageTitle: staticValues.homeHeading,
        headingText: staticValues.heading
    }
}

const getParsedStaticValues = (inputString) =>{
    return getAlphaNumeric(inputString)
}

const reloadPage = async(page) => {
    await page.reload()
}

const elementLocator = async(page, selector) => {
    return await page.locator(selector)
}

const getIdFromUrl = async(page) => {
    const url = await page.url()
    return url.split("edit&id=").pop()
}

const getSelectorForFolder = (folderName) => {
    return format(boxes.folderSelectorByName, folderName)
}

const waitForConnectButton = async(page) => {
    await page
        .locator(editPage.connectBtnSelector)
        .waitFor({ state: "visible" })
}

const waitForSelector = async(page, selector) => {
    await page
        .locator(selector)
        .waitFor({ state: "visible" })
}

module.exports = {
    waitForSelector,
    getDeleteConnectionBtnSelectorByFolderName,
    waitForConnectButton,
    getSelectorForFolder,
    getParsedStaticValues,
    getIdFromUrl,
    elementLocator,
    reloadPage,
    editPage,
    getStaticValues,
    clickOnNewBtn,
    fillFolderNameField,
    fillTokenField,
    clickOnSaveAndClose,
    clickOnSaveBtn,
    clickOnCancelButton,
    getRowSelectorByFolderName,
    getCheckboxSelectorByFolderName,
    getRowSelectorById,
    checkPreviewBtnVisibility,
    chooseLabelFromDropdown,
    checkPreviewBtnFromRow,
    getAccessTypeForFolder,
    getConnectionStatusForRow,
    getConnectionStatusForFolder,
    getPageTitle,
    openEditMenuForFolderByName,
    clickOnDeleteConnectionBtn,
    getDropboxTableData,
    getDropboxTableDataForFolder,
    clickOnAccessTypeDropDown,
    selectOnAccessTypeOption,
    deleteDropboxFolder,
    deleteAllFolders,
    getTotalFoldersCount,
    getAlertMessage,
    browse,
    getUserId,
    getErrorMessageText,
    getConnectBtnSelectorByFolderName
}
