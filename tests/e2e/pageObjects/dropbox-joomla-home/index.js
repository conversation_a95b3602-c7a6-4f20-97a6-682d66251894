const po = require("./actions")

class DropboxJoomlaHome {
    constructor(page) {
        this.page = page
    }

    async clickOnNewBtn() {
        await po.clickOnNewBtn(this.page)
    }

    async fillFolderNameField(folderName) {
        await po.fillFolderNameField(this.page, folderName)
    }

    async fillTokenField(tokenValue) {
        await po.fillTokenField(this.page, tokenValue)
    }

    async clickOnSaveAndClose() {
        await po.clickOnSaveAndClose(this.page)
    }

    async clickOnSaveBtn() {
        await po.clickOnSaveBtn(this.page)
    }

    async clickOnCancelButton() {
        await po.clickOnCancelButton(this.page)
    }

    async checkPreviewBtnVisibility(folderName) {
        return await po.checkPreviewBtnVisibility(this.page, folderName)
    }

    async chooseLabelFromDropdown(selectorValue, labelValue) {
        await po.chooseLabelFromDropdown (this.page, selectorValue, labelValue)
    }

    async getAccessTypeForFolder(folderName) {
        return po.getAccessTypeForFolder(this.page, folderName)
    }

    async getConnectionStatusForFolder(folderName) {
        return await po.getConnectionStatusForFolder(this.page, folderName)
    }

    async getPageTitle() {
        return await po.getPageTitle(this.page)
    }

    async openEditMenuForFolderByName(folderName) {
        await po.openEditMenuForFolderByName(this.page, folderName)
    }

    async clickOnDeleteConnectionBtn(folderName) {
        await po.clickOnDeleteConnectionBtn(this.page, folderName)
    }

    async getDropboxTableData() {
        return await po.getDropboxTableData(this.page)
    }

    async getDropboxTableDataForFolder(folderName) {
        return await po.getDropboxTableDataForFolder(this.page, folderName)
    }

    async clickOnAccessTypeDropDown() {
        await po.clickOnAccessTypeDropDown(this.page)
    }

    async selectOnAccessTypeOption(accessType) {
        await po.selectOnAccessTypeOption(this.page, accessType)
    }

    async deleteDropboxFolder(folderName) {
        await po.deleteDropboxFolder(this.page, folderName)
    }

    async deleteAllFolders() {
        await po.deleteAllFolders(this.page)
    }

    async getTotalFoldersCount() {
        return await po.getTotalFoldersCount(this.page)
    }

    async getAlertMessage() {
        return await po.getAlertMessage(this.page)
    }

    async browse() {
        await po.browse(this.page)
    }

    async getUserId() {
        return await po.getUserId(this.page)
    }

    async getErrorMessageText() {
        return await po.getErrorMessageText(this.page)
    }

    getStaticValues() {
        return po.getStaticValues()
    }

    getEditPageSelectors(){
        return po.editPage
    }

    async reloadPage(){
        await po.reloadPage(this.page)
    }

    async elementLocator(selector){
        return await po.elementLocator(this.page, selector)
    }

    async getIdFromUrl(){
        return await po.getIdFromUrl(this.page)
    }

    getParsedStaticValues(inputString) {
        return po.getParsedStaticValues(inputString)
    }

    getSelectorForFolder(folderName){
        return po.getSelectorForFolder(folderName)
    }

    async waitForConnectButton(){
        await po.waitForConnectButton(this.page)
    }

    getConnectBtnSelectorByFolderName(folderName){
        return po.getConnectBtnSelectorByFolderName(folderName)
    }

    getDeleteConnectionBtnSelectorByFolderName(folderName){
        return po.getDeleteConnectionBtnSelectorByFolderName(folderName)
    }

    async waitForSelector(selector) {
        await po.waitForSelector(this.page, selector)
    }
}

module.exports = DropboxJoomlaHome
