const {clickOnElement} = require("../../utils")

let staticValues = {
    extensionManageHeading: "Extensions: Manage",
    extensionInstallHeading: "Extensions: Install",
    heading : "Home Dashboard"
}
const titleSelector = "//h1[@class='page-title']"
const componentSelector =
    "//ul[@id='menu']//a[contains(text(), 'Components')]"

/**
 * Function to get the heading of the page
 *
 * @return {string} Heading / title of page
 */
const getPageTitle = async(page) => {
    return await page.locator(titleSelector).innerText()
}

/**
 * Function to click on `Components` menu
 */
const clickOnComponentsSection = async(page) => {
    await clickOnElement(page, componentSelector)
}

module.exports = {
    getPageTitle,
    clickOnComponentsSection,
    staticValues
}
