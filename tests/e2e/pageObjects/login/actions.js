const config = require("../../config")
const adminPageUrl = new URL("/administrator", config.JOOMLA_HOST_URL).href
const alertMessageSelector = ".alert-message"
const usernameFieldSelector = "#mod-login-username"
const passwordFieldSelector = "#mod-login-password"

const loginFormSelector = "form#form-login"

const submitLoginButtonSelector = "#btn-login-submit"

const credentials = {
    id: config.JOOMLA_ADMIN_USERNAME,
    pswd: config.JOOMLA_ADMIN_PASSWORD,
}

/**
 * Function to visit the Joomla Admin Page
 */
const browseToLoginPage = async(page) => {
    await page.goto(adminPageUrl)
}

const getPageVisibility = async(page) => {
    const loginFormLocator = await page.locator(loginFormSelector)
    return await loginFormLocator.isVisible()
}

/**
 * Function to login to the Joomla admin page
 * @param page
 * @param {string} username - Joomla Account Username
 * @param {string} password - Joomla Account Password
 */
const login = async(page, username, password) => {
    await page.fill(usernameFieldSelector, username)
    await page.fill(passwordFieldSelector, password)

    await page.locator(submitLoginButtonSelector).click()
}

const getCredentials = () => {
    return credentials
}

/**
 * Function to get error message that is shown for invalid login credentials
 * @return {string} Error message on invalid login
 */
const getErrorMsg = async(page) => {
    const msgLocator = await page.locator(alertMessageSelector)
    return msgLocator.innerText()
}

module.exports = {
    browseToLoginPage,
    getPageVisibility,
    login,
    getErrorMsg,
    getCredentials
}
