const po = require("./actions")

class Login {
    constructor(page) {
        this.page = page
    }

    async login(username, password) {
        await po.login(this.page, username, password)
    }

    async getErrorMsg() {
        return po.getErrorMsg(this.page)
    }

    async browseToLoginPage() {
        await po.browseToLoginPage(this.page)
    }

    async getPageVisibility() {
        return await po.getPageVisibility(this.page)
    }

    getCredentials() {
        return po.getCredentials()
    }
}

module.exports = Login
