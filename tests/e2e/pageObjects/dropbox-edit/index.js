const po = require("./actions")

class DropboxEdit {
    constructor(page) {
        this.page = page
    }

    async clickOnTab(tabName) {
        await po.clickOnTab(this.page, tabName)
    }

    async fillDateFormatField(dateFormat) {
        await po.fillDateFormatField(this.page, dateFormat)
    }

    async fillTimezoneField(timezone) {
        await po.fillTimezoneField(this.page, timezone)
    }

    async selectDefaultSortingField(sortFieldName) {
        await po.selectDefaultSortingField(this.page, sortFieldName)
    }

    async fillInputField(elementName, thumbnailPerRow) {
        await po.fillInputField(this.page, elementName, thumbnailPerRow)
    }

    async getSortingFieldSelector(sortFieldName) {
        return po.getSortingFieldSelector(sortFieldName)
    }

    async setDefaultSortingDirection(sortingDirection) {
        await po.setDefaultSortingDirection(this.page, sortingDirection)
    }

    async addDescriptionForUpload(description, toogleOption) {
        await po.addDescriptionForUpload(this.page, description, toogleOption)
    }

    getPicturesFunctionTabSelectors() {
        return po.picturesFunctionTab
    }

    getParametersTabSelectors() {
        return po.parametersTab
    }

    getListFunctionTabSelectors() {
        return po.listFunctionTab
    }

    getUploadFunctionTabSelectors() {
        return po.uploadFunctionTab
    }
}

module.exports = DropboxEdit
