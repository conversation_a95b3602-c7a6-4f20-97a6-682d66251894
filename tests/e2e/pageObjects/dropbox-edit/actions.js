const {format} = require("util")

const tabSelector = "//a[contains(text(), '%s')]"

const listFunctionTab = {
    breadcrumbsSelector: "#jform_params_show_breadcrumbs",
    dateFormatSelector: "#jform_params_date_format",
    timezoneSelector: "#jform_params_time_zone",
    sortingFieldSelector: `#jform_params_list_sorting_field${"%s"}`,
    sortingDirectionSelector: `#jform_params_list_sorting_dir${"%s"}`,
    showFileModificationDateSelector: "#jform_params_list_show_modified_date",
    showFileSizeSelector: "#jform_params_list_show_size",
}

const parametersTab = {
    listFilesSelector : "#jform_params_function_list",
    uploadSelector : "#jform_params_function_upload",
    showSearchFieldSelector : "#jform_params_show_search",
    viewPicturesSelector : "#jform_params_function_pic",
    logDownloadsSelector : "#jform_params_log_downloads",
    logUploadsSelector : "#jform_params_log_uploads",
    viewDownloadsInBrowserSelector : "#jform_params_view_downloads_in_browser",
    createPrivateUserDirectoriesSelector : "#jform_params_private_user_directories",
    emailNotificationSenderAddressSelector : "#jform_params_notification_email_sender_address",
    emailNotificationRecipientAddressSelector : "#jform_params_notification_email_recipient_address",
    maxSizeOfCacheSelector : "#jform_params_max_cache_size",
    maxFileSizeInCacheSelector : "#jform_params_max_filesize_in_cache",
    doVerifyingPeersSSLSelector : "jform_params_do_verify_the_peer_ssl_certificate",
}

const uploadFunctionTab = {
    changeFolderAfterUploadSelector: "#jform_params_change_folder_after_upload",
    allowUploadIntoSubfoldersSelector: "#jform_params_allow_subfolder_upload",
    descriptionForUploadSelector: "#jform_params_description_for_upload",
    descriptionForUploadIframeSelector: "#jform_params_description_for_upload_ifr",
    toggleEditorBtnSelector: ".btn.btn-secondary.js-tiny-toggler-button",
    addTimestampToTheFileNameSelector: "#jform_params_add_timestamp_to_upload",
}

const picturesFunctionTab = {
    thumbnailSizeSelector: "#jform_params_th_size_pic",
    thumbnailsPerRowSelector: "#jform_params_th_per_row",
    borderThicknessSelector: "#jform_params_enl_brdsize",
    borderColorSelector: "#jform_params_enl_brdcolor",
    borderBackgroundPicSelector: "#jform_params_enl_brdbck",
    useRoundedBordersSelector: "#jform_params_enl_brdround",
    aniStepsSelector: "#jform_params_enl_maxstep",
    timeBetweenStepsSelector: "#jform_params_enl_speed",
    animationSelector: "#jform_params_enl_ani",
    glideTransparencySelector: "#jform_params_enl_opaglide",
    shadowUnderBorderSelector: "#jform_params_enl_shadow",
    sizeOfShadowSelector: "#jform_params_enl_shadowsize",
    shadowColorSelector: "#jform_params_enl_shadowcolor",
    shadowIntensitySelector: "#jform_params_enl_shadowintens",
    darkenScreenSelector: "#jform_params_enl_dark",
    howDarkTheScreenShouldBeSelector: "#jform_params_enl_darkprct",
    howLongDarkeningShouldTakeSelector: "#jform_params_enl_darksteps",
    centerEnlargedPicOnScreenSelector: "#jform_params_enl_center",
    enableDragDropForPicsSelector: "#jform_params_enl_drgdrop",
    preloadNextPrevPicSelector: "#jform_params_enl_preload",
    showPicTitleBarSelector: "#jform_params_enl_titlebar",
    keyNavigationSelector: "#jform_params_enl_keynav",
    mouseWheelNavigationSelector: "#jform_params_enl_wheelnav",
    colorOfTitleBarTextSelector: "#jform_params_enl_titletxtcol",
}

const clickOnTab = async (page, tabName) => {
    await page.locator(format(tabSelector, tabName)).click()
}

const fillDateFormatField = async (page, dateFormat) => {
    await page.fill(listFunctionTab.dateFormatSelector, dateFormat)
}

const fillTimezoneField = async (page, timezone) => {
    await page.fill(listFunctionTab.timezoneSelector, timezone)
}

const getSortingFieldSelector = (sortFieldName) => {
    let sortFieldId = ""
    switch (sortFieldName) {
    case "Name":
        sortFieldId = 0
        break
    case "Modified Date":
        sortFieldId = 1
        break
    case "Size":
        sortFieldId = 2
        break
    case "Use Global":
        sortFieldId = 3
        break
    }
    return format(listFunctionTab.sortingFieldSelector, sortFieldId)
}

const selectDefaultSortingField = async(page, sortFieldName) => {
    await page.locator(getSortingFieldSelector(sortFieldName)).click()
}

const setDefaultSortingDirection = async(page, sortingDirection) => {
    let sortDirectionId = null
    switch (sortingDirection) {
    case "Ascending":
        sortDirectionId = 0
        break
    case "Descending":
        sortDirectionId = 1
        break
    case "Use Global":
        sortDirectionId = 2
        break
    }
    await page.locator(format(listFunctionTab.sortingDirectionSelector, sortDirectionId)).click()
}

const addDescriptionForUpload = async(page, description, toogleOption) => {
    if (!["On", "Off"].includes(toogleOption)) {
        throw new Error(`Expected toggle option to be either 'On' or 'Off' but got ${toogleOption}`)
    }
    if(toogleOption === "Off") {
        await page.locator(uploadFunctionTab.toggleEditorBtnSelector).click()
        await page.fill(uploadFunctionTab.descriptionForUploadSelector, description)
    }
    if(toogleOption === "On"){
        await page.locator(uploadFunctionTab.descriptionForUploadIframeSelector).click()
        await page.keyboard.type(description)
    }
}

const fillInputField = async(page, elementName, thumbnailPerRow) =>{
    await page.fill(elementName, thumbnailPerRow)
}

module.exports = {
    listFunctionTab,
    uploadFunctionTab,
    picturesFunctionTab,
    parametersTab,
    clickOnTab,
    fillDateFormatField,
    fillTimezoneField,
    getSortingFieldSelector,
    selectDefaultSortingField,
    setDefaultSortingDirection,
    addDescriptionForUpload,
    fillInputField
}
