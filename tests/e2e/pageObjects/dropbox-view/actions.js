const {format} = require("util")
const {expect} = require("@playwright/test")
const path = require("path")
const util = require("util")
const {unlink} = require("fs")
const config = require("../../config")
const {getVisibility, getInnerText,clickOnElement} = require("../../utils")

const mainContentSelector = "main .dropbox_content"
const fileInputSelector = ".dropbox_content input[type='file']"
const destinationFolderInputSelector = ".dropbox_content input.dropbox_upload_des_folder"
const searchBarSelector = "input.dropbox_file_search"
const searchButtonSelector = "//button[text()='search' and @type='submit']"
const resetSearchButtonSelector = "//button[text()='reset' and @type='reset']"
const allFilesNameSelector = ".file_listing_table .db_file_icon .name a"
let uploadIndicatorSelector = "form #dropbox_upload_indicator_"
const singleFileSelector = "//div[contains(@class,'db_file_icon')]/div/a[contains(text(),'%s')]"
const downloadPath = "/tmp/"
const breadcrumbSelector = "#dropbox_wrapper_"


const listView = {
    selector: ".file_listing_table",
    rowsSelector: "//div[@class='file_listing_table']/div",
    subFolderNameSelector: "//div[@class='file_listing_table']//a[.='%s']",
    headingsTextSelector: ".file_listing_header_cell_text",
    fileSizeColumnSelector: ".file_listing_header_cell.size",
    fileModificationDateColumnSelector: ".file_listing_header_cell.modified_date",
    columnHeadingSelector: "//div[@class='file_listing_header_cell_text' and contains(text(), '%s')]",
    ascendingIconSelector: "//div[@class='file_listing_header_cell_text' and contains(text(), '%s')]/following-sibling::div[@class='sorting_icon_asc']",
    descendingIconSelector: "//div[@class='file_listing_header_cell_text' and contains(text(), '%s')]/following-sibling::div[@class='sorting_icon_desc']",
}

const uploadForm = {
    selector: "form[action='']", // used a blank action
    // destinationFolderSelector: `#dropbox_dest_folder_${const dropboxId}`,
}

const loadingAnimationSelector = "#dropbox_loading_indicator"

let galleryView = {
    selector: "div.dropbox_pic_wrapper",
    imgLoadingAnimationSelector: "#enl_ldrgif",
    allImageSelector: "div.dropbox_pic_wrapper a",
    firstImageSelector: ".dropbox_pic_wrapper a:nth-child(1) img",
    firstImageExpandedViewSelector: "#dropbox_pictures_",
    imagePerRowSelector: "//div[@class='dropbox_pic_clear'][1]/preceding-sibling::a",
    firstImageTitleSelector: ""
}
const imageSelector= "//div[@class='dropbox_pic_wrapper']/a/img[@name='%s']"
const errorView = {
    mainBodySelector: "#content .well",
    errorSummarySelector: "blockquote",
}
const unauthorizedMsg = "You don't have permission to access this"
const loginForm = {
    usernameSelector: "#modlgn-username-16",
    passwordSelector: "#modlgn-passwd-16",
    logInBtnSelector: ".btn.btn-primary.w-100"
}

/**
 * Function to visit the preview page
 */


/**
 * Navigates the user to the preview page of a folder
 *
 *
 * @returns {Promise<void>}
 */
const navigateToThePreviewPage = async(page, dropboxId) => {
    const baseUrl = new URL(`/index.php?option=com_dropbox&id=${dropboxId}`, config.JOOMLA_HOST_URL).href
    await page.goto(baseUrl)
}

/**
* Function to check whether the page content has loaded successfully or not
* Upon token expiry, the page would load but the menu will not load
* @return {Promise<boolean>} Status for whether the menu is loaded or not
*/
const getVisibilityOfPreviewPageContent = async(page) => {
    return await getVisibility(page, mainContentSelector)
}

/**
 * Function to check and get the number of images present in the gallery section
 */
const getNumberOfImagesInGallery = async(page) => {
    const galleryImages = await page.locator(galleryView.allImageSelector)
    return galleryImages.count()
}

/**
 * Function to get dimension of an image in the gallery section
 */
const getImageDimension = async(page, selectorValue) => {
    const imageLocator = await page.locator(selectorValue)
    await expect(imageLocator).toBeVisible()
    const imageDimension = await imageLocator.boundingBox()
    return {
        width: imageDimension.width,
        height: imageDimension.height
    }
}

const getImageDimensions = async(page) => {
    const firstImageDimension = await getImageDimension(page, galleryView.firstImageSelector)
    // const imgLoadingAnimationDimension = await getImageDimension(page, galleryView.imgLoadingAnimationSelector)
    // const allImageSelectorDimension = await getImageDimension(page, galleryView.allImageSelector)
    return {firstImageDimension}
}

/**
 * Function to get names of all files listed in the table
 * @return {Promise<array>} Array of file names
 */
const getAllDisplayedFilesName = async(page) => {
    const allFilesLocator = await page.locator(allFilesNameSelector)
    return allFilesLocator.allInnerTexts()
}

const waitForImageToLoad = async(page, dropboxId) => {
    const locatorVal = await page.locator(galleryView.imgLoadingAnimationSelector)
    await locatorVal.waitFor({state: "visible"})
    const locatorForFirstImage = await page.locator((galleryView.firstImageExpandedViewSelector)+`${dropboxId}_0clonebtns`)
    await locatorForFirstImage.waitFor({state: "visible"})
}

/**
 * Uploads a single file to the dropbox account using extension UI
 *
 * @param page
 * @param {string} fileName - Name of the file to be uploaded
 */
const uploadSingleFile = async(page, fileName) => {
    const uploadPath = path.join(config.FILES_FOR_UPLOAD, fileName)
    const [fileChooser] = await Promise.all([
        page.waitForEvent("filechooser"),
        page.locator(fileInputSelector).click({force: true}),
    ])

    await fileChooser.setFiles(uploadPath)
}

/**
 * Function to upload multiple files to dropbox account
 * @param page
 * @param {array} fileNameArray - Array of file names to be uploaded
 */
const uploadMultipleFiles = async (page, fileNameArray) => {
    const arrayOfFilePaths = []
    for (let fileName of fileNameArray) {
        const uploadPath = path.join(config.FILES_FOR_UPLOAD, fileName)
        arrayOfFilePaths.push(uploadPath)
    }
    const [fileChooser] = await Promise.all([
        page.waitForEvent("filechooser"),
        page.locator(fileInputSelector).click({force: true}),
    ])
    await fileChooser.setFiles(arrayOfFilePaths)
}

/**
 * Function to get the error message
 */
const getErrorMessage = async(page)=> {
    return await page.locator(errorView.errorSummarySelector).innerText()
}

/**
 * Function to get the current directory opened in the preview page
 * Ensure that `Show Breadcrubms` feature is enabled before using this function
 * @return {string} Current directory name
 */
const getCurrentActiveDirectory = async(page, dropboxId) => {
    const breadcrumbLocator = await page.locator(`${breadcrumbSelector}${dropboxId} h1`)
    const breadcrumbText = await breadcrumbLocator.innerText()
    const currDirectoryArr = breadcrumbText.split("/")
    currDirectoryArr.shift()
    return currDirectoryArr.join("/")
}

const waitForUploadToFinish = async(page, dropboxId) => {
    await waitForUploadIndicatorToDisappear(page, dropboxId)
    await waitWhileFilesListLoads(page)
}

const waitWhileFilesListLoads = async(page) => {
    try {

        await page
            .locator(loadingAnimationSelector)
            .waitFor({state: "visible"})
    } catch (e) {
        // ignore the error and go ahead to see
        // if the loading animation is now hidden
        console.error(e)
    } finally {
        // now if we do not see the loading animation as hidden
        // the test will fail but with the provided timeout
        await page
            .locator(loadingAnimationSelector)
            .waitFor({state: "hidden"})
    }
}

/**
 * waits for the loading animation to appear & disappear
 */
const waitForUploadIndicatorToDisappear = async(page, dropboxId) => {
    try {
        await page
            .locator(uploadIndicatorSelector+dropboxId)
            .waitFor({state: "visible"})
    } catch (e) {
        // ignore the error and go ahead to see
        // if the loading animation is now hidden
        console.error(e.getErrorMessage())
    } finally {
        // now if we do not see the loading animation as hidden
        // the test will fail but with the provided timeout
        await page
            .locator(uploadIndicatorSelector+dropboxId)
            .waitFor({state: "hidden"})
    }
}

/**
 * opens a sub-folder in the preview page
 *
 * @param page
 * @param {string} subFolderName - Name of the sub-folder to be opened
 */
const openSubFolderFromFileList = async(page, subFolderName) => {
    try {
        const subFolderNameSelector = format(
            listView.subFolderNameSelector,
            subFolderName
        )
        await page.locator(subFolderNameSelector).click()
        await waitWhileFilesListLoads()
    } catch (error) {
        throw new Error(`Subfolder ${subFolderName} could not be found.\n${error}`)
    }
}

const fillDestinationFolder = async(page, destinationFolder) => {
    await page.waitForSelector(destinationFolderInputSelector)
    await page.locator(destinationFolderInputSelector).fill(
        destinationFolder,
        {delay: 100, force: true}
    )
    const inputValue = await page.locator(destinationFolderInputSelector)
    // check if the value is actually set in the input
    await expect(inputValue).toHaveValue(destinationFolder)
}

const getFilesTableVisibility = async(page) => {
    return await page.locator(listView.selector).isVisible()
}

/**
 * Returns a selector for a file
 * @param page
 * @param {String} fileName - name of the file
 * @returns the selector for the provided filename
 */
const getSingleFileSelector = async(page, fileName) =>{
    return await page.locator(util.format(singleFileSelector,fileName))
}

const getFilePath = (fileName) => {
    return downloadPath + fileName
}

/**
 * Downloads a file from dropbox preview page
 * @param page
 * @param {String} fileName - name of the file
 *
 */
const  downloadSingleFile = async(page, fileName) => {
    // Selector for the file name
    const fileSelector = await getSingleFileSelector(page, fileName)
    // Start waiting for download before clicking. Note no await.
    // Source: https://playwright.dev/docs/downloads
    const downloadPromise = page.waitForEvent("download")
    // Click the item
    fileSelector.click()
    const download = await downloadPromise
    // const suggestedFileName = download.suggestedFilename();
    const filePath = getFilePath(fileName)
    // Save the file
    await download.saveAs(filePath)
}

/**
 * Removes a file from /tmp folder
 * @param {String} fileName - name of the folder
 */
const removeDownloadedFile = (fileName) => {
    const filePath = getFilePath(fileName)
    unlink(filePath, err => {
        if (err) {
            console.info("Unable to delete file due to",err.message)
            throw err
        }
        console.info(`Deleted file ${filePath} successfully`)
    })
}

const getModifiedDate = async(page, dropboxId) => {
    const fileModificationDateValueSelector = `.modified_date.modified_date${dropboxId}`
    const modifiedDateLocator = await page.locator(fileModificationDateValueSelector)
    return await modifiedDateLocator.innerText()
}

// const clickOnElement = async(page, fileName) => {
//     const fileNameSelector = await getSingleFileSelector(page, fileName)
//     await expect(fileNameSelector).toContainText(fileName)
//     await fileNameSelector.click()
// }

const getVisibilityOfDescription = async(page, dropboxId) => {
    const descriptionForUploadSelector = `//div[@id='dropbox_wrapper_${dropboxId}']//form//p`
    return await getVisibility(page, descriptionForUploadSelector)
}

const getDescriptionForUpload = async(page, dropboxId) => {
    const descriptionForUploadSelector = `//div[@id='dropbox_wrapper_${dropboxId}']//form//p`
    return await getInnerText(page, descriptionForUploadSelector)
}

const getFileName = async(page, dropboxId) => {
    const fileNameSelector = `.name.name${dropboxId}`
    const fileNameLocator = await page.locator(fileNameSelector)
    return await fileNameLocator.innerText()
}

const sortFiles = async(page, columnHeading) => {
    await clickOnElement(page, format(listView.columnHeadingSelector,columnHeading))
}

const getImagesPerRow = async(page) => {
    return await page.locator(galleryView.imagePerRowSelector).count()
}

const getBorderThickness = async(page, dropboxId) => {
    galleryView.firstImagePreviewSelector = `//img[@id='dropbox_pictures_${dropboxId}_0clone']`
    galleryView.firstImageBorderSelector = `//div[@id='dropbox_pictures_${dropboxId}_0clonebrd']`
    await page.locator(format(galleryView.firstImageSelector)).click()
    const imageDimension = await getImageDimension(page, galleryView.firstImagePreviewSelector)
    const borderDimension = await getImageDimension(page, galleryView.firstImageBorderSelector)
    return (borderDimension.width - imageDimension.width) / 2
}

/**
 * Function to convert the RGB color value to hex
 */
const convertRGBtoHex = async(colorValue) => {
    // extract r, g and b component from rgb value, as colorValue will have color value in rgb format
    const rgbColor = colorValue.substring(4, colorValue.length-1).replace(/ /g, "").split(",")

    // convert rgb to hex
    return (1 << 24 | rgbColor[0] << 16 | rgbColor[1] << 8 | rgbColor[2]).toString(16).slice(1)
}

const getPictureBorderColor = async(page, dropboxId) => {
    galleryView.firstImageBorderSelector = `//div[@id='dropbox_pictures_${dropboxId}_0clonebrd']`
    await page.locator(galleryView.firstImageSelector).click()

    const imageBorderLocator = await page.locator(galleryView.firstImageBorderSelector)
    await expect(imageBorderLocator).toBeVisible()
    const imageBorderColor = await imageBorderLocator.evaluate((e) => {
        return window.getComputedStyle(e).backgroundColor
    })

    return await convertRGBtoHex(imageBorderColor)
}

const login = async(page, username, password) => {
    await page.fill(loginForm.usernameSelector, username)
    await page.fill(loginForm.passwordSelector, password)
    await page.locator(loginForm.logInBtnSelector).click()
}

/**
 *
 * @param page
 * @param {string} columnHeading - heading of column of file listing table
 * @returns {boolean} - the visibility of the ascending icon for the given column heading
 */
const getAscSortingIconVisibility = (page, columnHeading) => {
    return getVisibility(page, format(listView.ascendingIconSelector, columnHeading))
}

/**
 *
 * @param page
 * @param {string} columnHeading - heading of column of file listing table
 * @returns {boolean}- the visibility of the descending icon for the given column heading
 */
const getDescSortingIconVisibility = (page, columnHeading) => {
    return getVisibility(page, format(listView.descendingIconSelector, columnHeading))
}

/**
 * Searches for the provided keyword item
 *
 * @param page
 * @param {string} keyword - keyword to search for
 */
const searchFor = async(page, keyword) => {
    await page.fill(searchBarSelector,keyword)
    await page.locator(searchButtonSelector).click()
}

const canNavigatePictureByKeyOrMouse = async(page, shouldNavigate, toggleTypes, dropboxId) => {
    const images = await getAllDisplayedFilesName(page)
    const previewedImageSelector = `//img[@id="dropbox_pictures_${dropboxId}_%sclone"]`
    await page.locator(format(imageSelector, images[0])).click()
    const firstPreviewedImageLocator = await page.locator(format(previewedImageSelector, await getImageId(page, images[0])))
    await expect(firstPreviewedImageLocator).toBeVisible()
    if (toggleTypes === "keys"){
        await page.keyboard.press("ArrowRight")
    } else {
        await page.mouse.wheel(0,-400)
    }
    const secondPreviewedImageLocator = await page.locator(format(previewedImageSelector, await getImageId(page, images[1])))
    if(shouldNavigate){
        await page.waitForSelector(format(previewedImageSelector, await getImageId(page, images[1])))
    }
    return await secondPreviewedImageLocator.isVisible()
}

const getImageId = async(page, fileName) => {
    const idLocator = await page.locator(format(imageSelector, fileName))
    const id = await idLocator.getAttribute("id")
    return id.split("_").pop()
}

const getTitleColor = async(page, dropboxId) => {
    await page.locator(galleryView.firstImageSelector).click()
    galleryView.firstImageTitleSelector = `#dropbox_pictures_${dropboxId}_0clonebtns > div`
    const imageTitleLocator = await page.locator(galleryView.firstImageTitleSelector)
    await expect(imageTitleLocator).toBeVisible()
    const imageTitleColor = await imageTitleLocator.evaluate((e) => {
        return window.getComputedStyle(e).color
    })
    return await convertRGBtoHex(imageTitleColor)
}

const navigateToLoginPage = async(page) => {
    await page.goto(config.JOOMLA_HOST_URL)
}

const clickOnFile = async(page, fileName) => {
    const fileNameSelector = await getSingleFileSelector(page, fileName)
    await expect(fileNameSelector).toContainText(fileName)
    await fileNameSelector.click()
}

const getVisibilityOfTitleBar = async(page, dropboxId) => {
    galleryView.firstImageTitleSelector = `#dropbox_pictures_${dropboxId}_0clonebtns > div`
    return getVisibility(
        page,
        galleryView.firstImageTitleSelector
    )
}

module.exports = {
    getVisibilityOfTitleBar,
    getFilePath,
    clickOnFile,
    navigateToLoginPage,
    searchBarSelector,
    downloadPath,
    unauthorizedMsg,
    uploadForm,
    listView,
    galleryView,
    breadcrumbSelector,
    destinationFolderInputSelector,
    navigateToThePreviewPage,
    getVisibilityOfPreviewPageContent,
    getNumberOfImagesInGallery,
    getImageDimension,
    getAllDisplayedFilesName,
    uploadSingleFile,
    uploadMultipleFiles,
    getErrorMessage,
    getCurrentActiveDirectory,
    waitForUploadToFinish,
    waitWhileFilesListLoads,
    waitForUploadIndicatorToDisappear,
    openSubFolderFromFileList,
    fillDestinationFolder,
    getFilesTableVisibility,
    getSingleFileSelector,
    downloadSingleFile,
    removeDownloadedFile,
    getModifiedDate,
    clickOnElement,
    getVisibilityOfDescription,
    getDescriptionForUpload,
    getFileName,
    sortFiles,
    getImagesPerRow,
    getBorderThickness,
    convertRGBtoHex,
    getPictureBorderColor,
    login,
    getAscSortingIconVisibility,
    getDescSortingIconVisibility,
    searchFor,
    canNavigatePictureByKeyOrMouse,
    getImageId,
    getTitleColor,
    getImageDimensions,
    waitForImageToLoad,
    resetSearchButtonSelector
}
