const po = require("./actions")
const config = require("../../config")

class DropboxView {
    constructor(page, id = config.TEST_ID) {
        this.page = page
        this.dropboxId = id
    }

    async navigateToThePreviewPage() {
        await po.navigateToThePreviewPage(this.page, this.dropboxId)
    }

    async getVisibilityOfPreviewPageContent() {
        return await po.getVisibilityOfPreviewPageContent(this.page)
    }

    async getNumberOfImagesInGallery() {
        return await po.getNumberOfImagesInGallery(this.page)
    }

    async getImageDimension(selectorValue) {
        return await po.getImageDimension(this.page, selectorValue)
    }

    async getAllDisplayedFilesName() {
        return await po.getAllDisplayedFilesName(this.page)
    }

    async uploadSingleFile(fileName) {
        await po.uploadSingleFile(this.page, fileName)
    }

    async uploadMultipleFiles(fileNameArray) {
        await po.uploadMultipleFiles(this.page, fileNameArray)
    }

    async getErrorMessage() {
        return await po.getErrorMessage(this.page)
    }

    async getCurrentActiveDirectory() {
        return po.getCurrentActiveDirectory(this.page, this.dropboxId)
    }

    async waitForUploadToFinish() {
        await po.waitForUploadToFinish(this.page, this.dropboxId)
    }

    async waitWhileFilesListLoads() {
        await po.waitWhileFilesListLoads(this.page)
    }

    async waitForUploadIndicatorToDisappear() {
        await po.waitForUploadIndicatorToDisappear(this.page)
    }

    async openSubFolderFromFileList(folderName) {
        await po.openSubFolderFromFileList(this.page, folderName)
    }

    async fillDestinationFolder(destinationFolder) {
        await po.fillDestinationFolder(this.page, destinationFolder)
    }

    async getFilesTableVisibility() {
        return await po.getFilesTableVisibility(this.page)
    }

    async getSingleFileSelector(fileName) {
        return await po.getSingleFileSelector(this.page, fileName)
    }

    async downloadSingleFile(fileName) {
        await po.downloadSingleFile(this.page, fileName)
    }

    removeDownloadedFile(fileName) {
        po.removeDownloadedFile(fileName)
    }

    async getModifiedDate() {
        return await po.getModifiedDate(this.page, this.dropboxId)
    }

    getDescriptionForUpload() {
        return po.getDescriptionForUpload(this.page, this.dropboxId)
    }

    async getFileName() {
        return await po.getFileName(this.page, this.dropboxId)
    }

    sortFiles(columnHeading) {
        return po.sortFiles(this.page, columnHeading)
    }

    async getImagesPerRow() {
        return await po.getImagesPerRow(this.page)
    }

    async getBorderThickness() {
        return await po.getBorderThickness(this.page, this.dropboxId)
    }

    async convertRGBtoHex(colorValue) {
        return await po.convertRGBtoHex(this.page, colorValue)
    }

    async getPictureBorderColor() {
        return await po.getPictureBorderColor(this.page, this.dropboxId)
    }

    async getTitleColor() {
        return await po.getTitleColor(this.page, this.dropboxId)
    }

    async login(username, password) {
        await po.login(this.page, username, password)
    }

    async navigateToLoginPage() {
        await po.navigateToLoginPage(this.page)
    }

    async getAscSortingIconVisibility(columnHeading) {
        return po.getAscSortingIconVisibility(this.page, columnHeading)
    }

    async getDescSortingIconVisibility(columnHeading) {
        return po.getDescSortingIconVisibility(this.page, columnHeading)
    }

    async searchFor(keyword) {
        await po.searchFor(this.page, keyword)
    }

    async canNavigatePictureByKeyOrMouse(shouldNavigate, toggleTypes) {
        return await po.canNavigatePictureByKeyOrMouse(this.page, shouldNavigate, toggleTypes, this.dropboxId)
    }

    async getImageId(fileName) {
        return await po.getImageId(this.page, fileName)
    }
    async clickOnFile(fileName) {
        await po.clickOnFile(this.page, fileName)
    }

    getUnauthorizedErrorMessage() {
        return po.unauthorizedMsg
    }

    getFilePath(fileName) {
        return po.getFilePath(fileName)
    }

    async getImageDimensions() {
        return await po.getImageDimensions(this.page)
    }

    async waitForImageToLoad() {
        await po.waitForImageToLoad(this.page, this.dropboxId)
    }

    getResetSearchButtonSelector() {
        return po.resetSearchButtonSelector
    }

    getSearchBarSelector() {
        return po.searchBarSelector
    }

    getUploadFormSelectors() {
        return po.uploadForm
    }

    getListViewSelectors() {
        return po.listView
    }

    getGalleryViewSelectors() {
        return po.galleryView
    }

    getBreadcrumbSelector() {
        return po.breadcrumbSelector
    }

    getDestinationFolderInputSelector() {
        return po.destinationFolderInputSelector
    }

    async getVisibilityOfTitleBar() {
        return await po.getVisibilityOfTitleBar(this.page, this.dropboxId)
    }
}
module.exports = DropboxView
