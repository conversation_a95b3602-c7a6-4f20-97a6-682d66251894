const {format} = require("util")
const {clickOnElement} = require("../../utils")

const logsSelector = "//a[@aria-label='Logs']"
const fileLogsSelector = "//td[@class='file-name' and contains(text(), '%s')]//ancestor::tr//td[@class='folder-name' and contains(text(), '%s')]"
const fileLogsDirectionSelector = "//td[@class='file-name' and contains(text(), '%s')]//ancestor::tr//td[@class='folder-name' and contains(text(), '%s')]//ancestor::tr//td[@class='direction']"

const clickOnLogsTab = async(page) => {
    await clickOnElement(page, logsSelector)
}

const getFormattedLogsSelector = (fileName, folderName) =>{
    const formattedFileLogsSelector = format(fileLogsSelector, fileName, folderName)
    const formattedFileLogsDirectionSelector = format(fileLogsDirectionSelector, fileName, folderName)
    return {formattedFileLogsSelector, formattedFileLogsDirectionSelector}
}

module.exports = {clickOnLogsTab, getFormattedLogsSelector}
