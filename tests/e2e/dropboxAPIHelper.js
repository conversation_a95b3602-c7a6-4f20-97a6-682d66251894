const { expect } = require("@playwright/test")
const { default: axios } = require("axios")
const config = require("./config")
const qs = require("qs")
const path = require("path")
const fs = require("fs")

class DropboxAPIHelper {
    static async fetchAccessToken(refreshToken) {
        try {
            const response = await axios.post(
                "https://api.dropbox.com/oauth2/token",
                qs.stringify({
                    refresh_token: refreshToken,
                    grant_type: "refresh_token",
                    client_id: config.DROPBOX_CLIENT_ID,
                    client_secret: config.DROPBOX_CLIENT_SECRET,
                })
            )
    
            return response?.data?.access_token
        } catch (err) {
            throw new Error("Error while fetching access token. \nMessage: '" + err.message + "'")
        }
    }

    /*
    * removes the slashes if present at start or end of the file and folder name
    */
    static removeSlash(name){
        return name.replace(/^\/|\/$/g, "")
    }

    static async sendRequestToDropbox(method, url, data, header) {
        const accessToken = await this.fetchAccessToken(config.DROPBOX_REFRESH_TOKEN)
        return await axios({
            method,
            url,
            data,
            headers: {
                ... header,
                Authorization: `Bearer ${accessToken}`,
            },
        })
    }

    static async uploadSingleFileOnFolder(folderName, fileName) {
        folderName = this.removeSlash(folderName)
        fileName = this.removeSlash(fileName)
        const uploadPath = path.join(config.FILES_FOR_UPLOAD, fileName)
        let fileToUploadBuffer = fs.readFileSync(uploadPath)
        const response = await this.sendRequestToDropbox(
            "post",
            "https://content.dropboxapi.com/2/files/upload",
            fileToUploadBuffer,
            {
                "Content-Type": "application/octet-stream",
                "Dropbox-API-Arg": `{"autorename":false,"mode":"add","mute":false,"path":"/${folderName}/${fileName}","strict_conflict":false}`
            },
        )
        expect(response.status).toBe(200)
        console.info(`Uploaded file "${fileName}" successfully in folder "${folderName}" of the associated Dropbox server`)
    }

    static async uploadMultipleFileOnFolder(folderName, fileNameArray) {     
        for(let i = 0; i < fileNameArray.length; i++){
            await this.uploadSingleFileOnFolder(folderName, fileNameArray[i])
        }
    }

    static async deleteFolder(folderName){
        folderName = this.removeSlash(folderName)
        try{
            const response = await this.sendRequestToDropbox(
                "post",
                "https://api.dropboxapi.com/2/files/delete_v2",
                {
                    path: `/${folderName}`,
                },
                {
                    "Content-Type": "application/json",
                },
            )
            expect(response.status).toBe(200)
            console.info(`Deleted folder "${folderName}" from associated Dropbox server`)
        } catch (err) {
            if (
                err.response.status === 409 &&
                err.response.data.error_summary.includes("path_lookup/not_found")
            ) {
                console.info(`'Folder "${folderName}" not found.'`)
            } else {
                throw new Error(
                    "Error while deleting directory." +
                    "\nDirectory: '" + folderName + "'" +
                    "'\nStatus: '" + err.response.status + "'" +
                    "\nMessage: '" + err.message + "'" +
                    "'\nResponse: '" + JSON.stringify(err.response.data, null, 2) + "'"
                )
            }
        }
    }

    /**
     * Creates a folder at dropbox server with provided folder name
     * 
     * @param {string} folderName
     */
    static async createFolder(folderName){
        folderName = this.removeSlash(folderName)
        const response = await this.sendRequestToDropbox(
            "post",
            "https://api.dropboxapi.com/2/files/create_folder_v2",
            {
                autorename: false,
                path: `/${folderName}`,
            },
            {
                "Content-Type": "application/json",
            },
        )
        expect(response.status).toBe(200)
        console.info(`Created folder "${folderName}" successfully in the associated Dropbox server.\n`)
    }

    static async createMultipleFolders(foldersArray) {
        for(let i = 0; i < foldersArray.length; i++){
            await this.createFolder(foldersArray[i])
        }
    }
}

module.exports = DropboxAPIHelper
