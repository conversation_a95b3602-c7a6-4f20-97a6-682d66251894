/**
 * A store to remember the test created folders.
 *
 * Following folder informations are stored:
 * - folder ID: integer
 * - folder name: string
 * - folder access: string
 * - folder connection status: boolean
 *
 * We can use this store for followings:
 * - Navigate to the folder preview page
 * - Access the folder informations throught the test run
 * - any many more :)
 */
class CreatedFoldersStore {
    constructor() {
        this.folders = []
    }

    addFolder(id, name, access, isConnected) {
        this.folders.push({
            id, name, access, isConnected
        })
    }

    getFolders() {
        return this.folders
    }

    getFolderId(folderName) {
        return this.folders.find(folder => folder.name === folderName).id
    }

    clearFolders() {
        this.folders = []
    }

    removeFolder(folderName) {
        this.folders = this.folders.filter(folder => folder.name !== folderName)
    }

    isFolderCreated(folderName) {
        return this.folders.some(folder => folder.name === folderName)
    }

    getCount() {
        return this.folders.length
    }

    updateFolder(id, {name, access, isConnected} = {}) {
        const folder = this.folders.find(folder => folder.id === id)
        if(folder){
            folder.name = name || folder.name
            folder.access = access || folder.access
            folder.isConnected = isConnected || folder.isConnected
        }
    }
}

module.exports = CreatedFoldersStore
