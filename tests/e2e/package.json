{"name": "e2e", "description": "End to end tests for the Joomla-Dropbox extension plugin", "scripts": {"strict-node": "node --unhandled-rejections=strict", "turn-off-stats": "npm run strict-node scripts/turnOffStatisticsStatus.js", "setup-extension": "npm run install-extension && npm run turn-off-stats", "test:e2e": "cucumber-js --config cucumber.config.js --retry 1", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"axios": "^0.27.2", "dotenv": "^16.0.1", "qs": "^6.10.5"}, "devDependencies": {"@cucumber/cucumber": "^8.9.0", "@cucumber/pretty-formatter": "^1.0.0-alpha.2", "@playwright/test": "^1.45.3", "eslint": "^8.29.0", "mysql2": "^2.3.3", "playwright": "^1.45.3"}}