/**
 * This script will uninstall the dropbox joomla extension from Joomla v4
 *
 * Needs JOOMLA_VERSION env variable to be set.
 *
 * Why needed?
 * ===========
 * To automate the uninstallation process of the extension.
 */
const path = require("path")
const config = require("../config")
const {chromium} = require("playwright")
const {expect} = require("@playwright/test")

let exitCode = 0
const pluginsUrl = new URL("/administrator/index.php?option=com_installer&view=manage", config.JOOMLA_HOST_URL).href
const unInstallSuccessMessage = "Thanks for using this component. We're sorry to see you go."
const selectors = {
    usernameField: "#mod-login-username",
    passwordField: "#mod-login-password",
    loginButton: "#btn-login-submit",
    searchField: "#filter_search",
    searchButton: ".filter-search-bar__button",
    formCheckInput: "#cb0",
    uninstallButton: "#toolbar-delete button",
    alertInfoParagraph: ".alert-info p"
}
const expectedDialogMessage = "Are you sure you want to uninstall? Confirming will permanently delete the selected item(s)!"

const unInstall = async () => {
    // setup browser, context and page as global variables
    const browser = await chromium.launch({
        headless: true, channel: "chrome"
    })
    const context = await browser.newContext()

    if (config.IS_RUNNING_ON_CI) {
        await context.tracing.start({
            screenshots: true,
            snapshots: true,
            sources: true
        })
    }

    const page = await context.newPage()

    try {
        await page.goto(pluginsUrl)
        // login as the admin user
        await page.fill(selectors.usernameField, config.JOOMLA_ADMIN_USERNAME)
        await page.fill(selectors.passwordField, config.JOOMLA_ADMIN_PASSWORD)
        await Promise.all([
            page.waitForNavigation(),
            page.locator(selectors.loginButton).click()
        ])

        // search for the plugin
        await page.type(selectors.searchField, "dropbox")
        await page.locator(selectors.searchButton).click()

        // count the form check inputs after search
        const formCheckInputCount = await page
            .locator(selectors.formCheckInput)
            .count()

        // if the count is 0, then the plugin is not installed
        if (formCheckInputCount === 0) {
            console.info("The component is not installed yet.")
            return exitCode
        }

        await page.locator(selectors.formCheckInput).first().check()
        await page.waitForSelector(selectors.uninstallButton, {state: "visible"})

        await page.on("dialog", async (dialog) => {
            expect(dialog.message()).toEqual(expectedDialogMessage)
            await dialog.accept()
        })

        await page.locator(selectors.uninstallButton).click()

        await expect(page.locator(selectors.alertInfoParagraph).innerText())
            .resolves.toContain(unInstallSuccessMessage)
        console.info("The component uninstalled successfully.")
    } catch (e) {
        console.info("Failed to perform uninstallation of the plugin." +
            " Make sure JOOMLA_VERSION env variable is set.")
        console.error(e)
        exitCode = 1
    } finally {
        if (config.IS_RUNNING_ON_CI) {
            await context.tracing.stop({
                path: path.join(config.REPORT_DIR, "setup", "v3", "tracing.zip"),
            })
        }
        await page.close()
        await context.close()
        await browser.close()
    }
}

unInstall().then(() => {
    process.exit(exitCode)
})
