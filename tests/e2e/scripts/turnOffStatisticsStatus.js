/**
 * This script will disable the statistics plugin in Joomla v4
 *
 * Why needed?
 * ===========
 * The statistics plugin is enabled by default in Joomla v4. While running the
 * tests we want to assert the alert messages on various actions but these statistics
 * notifications will always interfere with the tests. So we need to disable the plugin.
 */

const {chromium} = require("playwright")
const config = require("../config")

let exitCode = 0
const pluginsUrl = "/administrator/index.php?option=com_plugins&view=plugins"
const selectors = {
    usernameField: "#mod-login-username",
    passwordField: "#mod-login-password",
    loginButton: "#form-login #btn-login-submit",
    searchField: "#filter_search",
    searchButton: ".filter-search-bar__button",
    editButton: "a[title='Edit System - Joomla! Statistics']",
    statusSelectField: "#general #jform_enabled",
    applyButton: "#toolbar-apply button"
}

const disableStatus = async () => {
    // setup browser, context and page as global variables
    const browser = await chromium.launch({
        headless: true, channel: "chrome"
    })
    const context = await browser.newContext()
    const page = await context.newPage()

    try {
        // fill the admin configuration form
        await page.goto(
            new URL(pluginsUrl, config.JOOMLA_HOST_URL).href
        )
        // login as the admin user
        await page.fill(selectors.usernameField, config.JOOMLA_ADMIN_USERNAME)
        await page.fill(selectors.passwordField, config.JOOMLA_ADMIN_PASSWORD)
        await Promise.all([
            page.waitForNavigation(),
            page.locator(selectors.loginButton).click()
        ])

        // search for the plugin
        await page.fill(selectors.searchField, "Statistics")
        await page.locator(selectors.searchButton).click()

        // open the plugin admin
        await Promise.all([
            page.waitForNavigation(),
            page.locator(selectors.editButton).click()
        ])
        const statusSelectField = page.locator(selectors.statusSelectField)
        await statusSelectField.selectOption({label: "Disabled"})
        await page.locator(selectors.applyButton).click()
        console.info("Statistics plugin disabled successfully")
    } catch (e) {
        console.error(e)
        exitCode = 1
    } finally {
        await page.close()
        await context.close()
        await browser.close()
    }
}

disableStatus().then(() => {
    process.exit(exitCode)
})
