# DropboxExtensionForJ<PERSON><PERSON>

### To run End-to-End tests

#### Pre-requisite

`1` Clone this repository

`2` Install Joomla

- These tests were performed on Joomla v3.9.
- Click the link [here](https://linuxhostsupport.com/blog/how-to-install-joomla-3-9-on-ubuntu-20-04/) to install Joomla-3.9

`3` :arrow_down: To install the Dropbox component:

    - Go to `Install` sub-menu in the `Extension` menu from Joomla Home Page

    - Select the option `Install from Folder`

    - Provide the path of this repository and click on `Check and Install`

  Or you can simply run the dropboxInstallation.feature file to automate the installation step.
### Run the tests:

`Step 1`: Inside the repo, navigate to `tests/e2e`. Then run the following command to install all the required packages.

```
npm install
```

`Step 2`: Open the file `.env-example` and fill up the necessary parameters. After that, rename the file to `.env`

### To finally run the tests, use the following commands while being in the `*/tests/e2e/` directory

```
npm i

npm run test:e2e features/'name-of-feature-file'
```

## Understanding the requirements for two hosts
>The CI uses two environment variables to reference the joomla's database host.

1. `JOOMLA_DATA_BASE_HOST`
2. `TEST_RUNNNER_DB_HOST`

- The host machine does not run `Joomla` and `MySQL` locally.
- `Joomla` and `MySQL` are two different containers inside a single isolated environment provided by docker-compose.
- Joomla uses the first environment variable `JOOMLA_DATA_BASE_HOST` to connect with MySQL. (Both share a same isolated environment)
- However, the tests runner can not connect to the database using `JOOMLA_DATA_BASE_HOST` as the tests run outside the environment provided by docker-compose.
- MySQL container uses port 3306 of the isolated environment.
- To be able to access this MySQL database from outside, the port number 3306 of isolated environment has been mapped into port 3306 of host machine.
- Doing so, accessing the port 3306 of host machine would give access to port 3306 of the isolated environment.
- Now the test runner can access the database by connecting to the server at port 3306 of host machine.

### That's why Joomla would need `JOOMLA_DATA_BASE_HOST` whereas the test runner would need `TEST_RUNNNER_DB_HOST` to connect to MySQL. <br> Here, `JOOMLA_DATA_BASE_HOST` is a virtual network created by docker-composer and `TEST_RUNNNER_DB_HOST` is the host machine's IP address.

## FAQ

1. ### Which host would be used for the tests?
    If `TEST_RUNNNER_DB_HOST` is provided, then that would be used as host. If not, `JOOMLA_DATA_BASE_HOST` would be used.

2. ### If I provided both `JOOMLA_DATA_BASE_HOST` and `TEST_RUNNNER_DB_HOST` then which one would be used while setting up Joomla?
    Joomla would always use `JOOMLA_DATA_BASE_HOST` as host if it is provided. Incase it is not provided, 'localhost' would be used but `TEST_RUNNNER_DB_HOST` would never be used by Joomla itself.

3. ### What would happen if I do not provide any of `JOOMLA_DATA_BASE_HOST` and `TEST_RUNNNER_DB_HOST`?
    If neither of them is provided, then the host would be setup as 'localhost' by both Joomla and test runner.

4. ### What would happen if I only provide `TEST_RUNNNER_DB_HOST`?
    Joomla would then use `localhost` as database host and test runner would use `TEST_RUNNER_DB_HOST`.

5. ### If I am running this locally, which of these two variables do I need to set up?
    While running locally, none of these need to be set. But if you want you can set them up as `localhost`.