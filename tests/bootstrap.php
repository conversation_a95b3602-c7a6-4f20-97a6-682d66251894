<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/vendor/phpunit/phpunit/src/Framework/TestCase.php';
require_once __DIR__ . '/vendor/phpunit/dbunit/src/DataSet/XmlDataSet.php';
require_once __DIR__ . '/vendor/phpunit/dbunit/src/TestCase.php';
require_once __DIR__ . '/lib/dropboxExtensionTestCase.php';
require_once __DIR__ . '/ui/lib/uiTest.php';
require_once 'testConfiguration.php';

$classLoader = new \Composer\Autoload\ClassLoader();
$classLoader->addPsr4("Test\\", __DIR__ . "/ui", true);
$classLoader->addPsr4("Test\\PageObject\\", __DIR__ . "/ui/lib", true);

$classLoader->register();

$testConfig   = new TestConfiguration();
$pathToJoomla = $testConfig->getValue("pathToJoomla");

if (!is_dir(realpath($pathToJoomla))) {
    throw new Exception("Could not find the folder: $pathToJoomla");
}

//Fake some required variables

$_SERVER['HTTP_HOST']      = 'localhost';
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI']    = '';

//start

\define('_JEXEC', 1);

// Fix magic quotes.
ini_set('magic_quotes_runtime', 0);

// Maximise error reporting.
ini_set('zend.ze1_compatibility_mode', '0');
error_reporting(E_ALL & ~E_STRICT);
ini_set('display_errors', 1);

if (!\defined('DS')) {
    \define('DS', DIRECTORY_SEPARATOR);
}

if (!\defined('JPATH_PLATFORM')) {
    \define('JPATH_PLATFORM', realpath($pathToJoomla . '/libraries'));
}
if (!\defined('JPATH_LIBRARIES')) {
    \define('JPATH_LIBRARIES', realpath($pathToJoomla . '/libraries'));
}
if (!\defined('JPATH_BASE')) {
    \define('JPATH_BASE', realpath($pathToJoomla));
}
if (!\defined('JPATH_ROOT')) {
    \define('JPATH_ROOT', realpath(JPATH_BASE));
}
if (!\defined('JPATH_CACHE')) {
    \define('JPATH_CACHE', JPATH_BASE . '/cache');
}
if (!\defined('JPATH_CONFIGURATION')) {
    \define('JPATH_CONFIGURATION', JPATH_BASE);
}
if (!\defined('JPATH_SITE')) {
    \define('JPATH_SITE', JPATH_ROOT);
}
if (!\defined('JPATH_ADMINISTRATOR')) {
    \define('JPATH_ADMINISTRATOR', JPATH_ROOT . '/administrator');
}
if (!\defined('JPATH_INSTALLATION')) {
    \define('JPATH_INSTALLATION', JPATH_ROOT . '/installation');
}
if (!\defined('JPATH_MANIFESTS')) {
    \define('JPATH_MANIFESTS', JPATH_ADMINISTRATOR . '/manifests');
}
if (!\defined('JPATH_PLUGINS')) {
    \define('JPATH_PLUGINS', JPATH_BASE . '/plugins');
}
if (!\defined('JPATH_THEMES')) {
    \define('JPATH_THEMES', JPATH_BASE . '/templates');
}
if (!\defined('JDEBUG')) {
    \define('JDEBUG', false);
}

if (!\defined('JPATH_COMPONENT')) {
    \define('JPATH_COMPONENT', JPATH_BASE . '/components/com_dropbox');
}

// Import the platform in legacy mode.
require_once JPATH_PLATFORM . '/import.legacy.php';
require_once JPATH_BASE . '/configuration.php';

// Force library to be in JError legacy mode
JError::setErrorHandling(E_NOTICE, 'message');
JError::setErrorHandling(E_WARNING, 'message');

// Bootstrap the CMS libraries.
require_once JPATH_LIBRARIES . '/cms.php';

//TODO: you will need to adjust this to match your paths
$srcPath = realpath("../");
JTable::addIncludePath(JPATH_COMPONENT . '/tables');
require_once JPATH_COMPONENT . "/models/dropbox.php";
require_once JPATH_COMPONENT . "/controller.php";

\define('JPATH_LIBRARIES_JOOMLA', JPATH_LIBRARIES . DS . 'joomla');
\define('JPATH_METHODS', JPATH_ROOT . DS . 'methods');

// Load the library importer, database + table classes and configuration
require_once JPATH_LIBRARIES . DS . 'import.legacy.php';
require_once JPATH_LIBRARIES . DS . 'legacy' . DS . 'request' . DS . 'request.php';
require_once JPATH_LIBRARIES . DS . 'cms' . DS . 'version' . DS . 'version.php';
require_once JPATH_LIBRARIES_JOOMLA . DS . 'object' . DS . 'object.php';
require_once JPATH_LIBRARIES_JOOMLA . DS . 'filesystem' . DS . 'folder.php';
require_once JPATH_LIBRARIES_JOOMLA . DS . 'filesystem' . DS . 'file.php';

require_once JPATH_LIBRARIES_JOOMLA . DS . 'database' . DS . 'database.php';
require_once JPATH_LIBRARIES_JOOMLA . DS . 'table' . DS . 'table.php';
require_once JPATH_CONFIGURATION . DS . 'configuration.php';

// Import plugin
jimport('joomla.plugin.plugin');

// Define configuration
jimport('joomla.registry.registry');

// Create the JConfig object
$config = new JConfig();

// Get the global configuration object
$registry = JFactory::getConfig();

// Load the configuration values into the registry
$registry->loadObject($config);

// initialise a session so that it is not started later after header info has been sent to Pear Printer.
// Prevents this error: session_start(): Cannot send session cookie - headers already sent by (output started at C:\xampp\php\PEAR\PHPUnit\Util\Printer.php:173)
$session = JFactory::getSession();
$app     = JFactory::getApplication('site');

// load language to prevent the error:
// Call to a member function getTag() on null
// libraries/cms/application/site.php:328

$app->loadLanguage();
