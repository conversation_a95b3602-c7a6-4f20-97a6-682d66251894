<?php

// @codingStandardsIgnoreLine because of no namespace set
class DropboxModelListFolderHelper
{
    /**
     *
     * @var DropboxModelDropbox
     */
    protected $model;

    // @codingStandardsIgnoreLine because of function pattern
    public function listRootFolder($id, $folderToList = "", $chRoot, $expectedPrefixInCleanPath = "")
    {
        $this->model->setup($id);
        $this->model->changeFolder($folderToList);
        $result = $this->model->listFolder();
        PHPUnit\Framework\Assert::assertArrayHasKey("entries", $result);
        $subset = [
                '.tag'         => 'folder',
                'name'         => 'sub folder with spaces',
                'path_lower'   => '/' . $chRoot . '/sub folder with spaces',
                'path_display' => '/' . $chRoot . '/sub folder with spaces',
                'cleanPath'    => $expectedPrefixInCleanPath . '/sub folder with spaces',
        ];
        PHPUnit\Framework\Assert::assertTrue(
            $this->isEntryInDropboxResult($result, $subset),
            "cannot find '" . $subset ['name'] . "' in result"
        );

        $subset = [
                '.tag'         => 'folder',
                'name'         => 'special char äöüß and more नेपाल',
                'path_lower'   => '/' . $chRoot . '/special char äöüß and more नेपाल',
                'path_display' => '/' . $chRoot . '/special char äöüß and more नेपाल',
                'cleanPath'    => $expectedPrefixInCleanPath . '/special char äöüß and more नेपाल',
        ];
        PHPUnit\Framework\Assert::assertTrue(
            $this->isEntryInDropboxResult($result, $subset),
            "cannot find '" . $subset ['name'] . "' in result"
        );

        $subset = [
                '.tag'         => 'file',
                'name'         => 'simple_file.txt',
                'path_lower'   => '/' . $chRoot . '/simple_file.txt',
                'path_display' => '/' . $chRoot . '/simple_file.txt',
                'cleanPath'    => $expectedPrefixInCleanPath,
        ];
        PHPUnit\Framework\Assert::assertTrue(
            $this->isEntryInDropboxResult($result, $subset),
            "cannot find '" . $subset ['name'] . "' in result"
        );

        $subset = [
                '.tag'         => 'file',
                'name'         => 'special char äöüß &%.txt',
                'path_lower'   => '/' . $chRoot . '/special char äöüß &%.txt',
                'path_display' => '/' . $chRoot . '/special char äöüß &%.txt',
                'cleanPath'    => $expectedPrefixInCleanPath,
        ];
        PHPUnit\Framework\Assert::assertTrue(
            $this->isEntryInDropboxResult($result, $subset),
            "cannot find '" . $subset ['name'] . "' in result"
        );
    }

    /**
     * checks if the result that came from dropbox contains the given file/folder entry
     * compares ".tag" , "name", "path_display" and "cleanPath" only
     *
     * @param  array   $result
     * @param  array   $subset
     * @return boolean
     */
    public function isEntryInDropboxResult($result, $subset)
    {
        $foundSubset = false;

        foreach ($result ['entries'] as $entry) {
            if (
                $entry ['.tag'] === $subset ['.tag']
                && $entry ['name'] === $subset ['name']
                && $entry ['path_display'] === $subset ['path_display']
                && $entry ['cleanPath'] === $subset ['cleanPath']
            ) {
                $foundSubset = true;
                break;
            }
        }

        return $foundSubset;
    }

    public function __construct(DropboxModelDropbox $model)
    {
        $this->model = $model;
    }
}
