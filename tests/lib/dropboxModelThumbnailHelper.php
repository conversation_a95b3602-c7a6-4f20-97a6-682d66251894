<?php

// @codingStandardsIgnoreLine because of no namespace set
class DropboxModelThumbnailHelper
{
    /**
     *
     * @var DropboxModelDropbox
     */
    protected $model;

    protected $tbSizes =  [
            'thumb'  => 32,
            'small'  => 64,
            'medium' => 128,
            'large'  => 640,
            'huge'   => 1024,
    ];

    /**
     * downloads Thumbnail and checks it for correct image type and size
     * returns result of getimagesize()
     *
     * @param  int    $id
     * @param  string $path
     * @param  string $tbSize
     * @return array
     */
    public function getThumbnail($id, $path, $tbSize)
    {
        $this->model->setup($id);
        $expectedPixelSize = $this->tbSizes[$tbSize];
        $thumbNail         = $this->model->getThumbnail(rawurlencode($path), $tbSize);
        PHPUnit\Framework\Assert::assertNotEmpty($thumbNail);
        $tmpfname = tempnam(sys_get_temp_dir(), 'dropboxExtensionUnitTest');
        PHPUnit\Framework\Assert::assertIsWritable(
            $tmpfname,
            "Could not create temp file"
        );
        $tempFileHandle = fopen($tmpfname, "w");
        fwrite($tempFileHandle, $thumbNail);
        fclose($tempFileHandle);

        //it has to be a JPG file
        PHPUnit\Framework\Assert::assertEquals(
            2,
            exif_imagetype($tmpfname)
        );

        // check the pixel size
        $pixelSize = getimagesize($tmpfname);
        PHPUnit\Framework\Assert::assertLessThanOrEqual(
            $expectedPixelSize,
            $pixelSize [0]
        );
        PHPUnit\Framework\Assert::assertLessThanOrEqual(
            $expectedPixelSize,
            $pixelSize [1]
        );

        unlink($tmpfname);
        return $pixelSize;
    }

    public function getTbSizes()
    {
        return $this->tbSizes;
    }

    public function __construct(DropboxModelDropbox $model)
    {
        $this->model = $model;
    }
}
