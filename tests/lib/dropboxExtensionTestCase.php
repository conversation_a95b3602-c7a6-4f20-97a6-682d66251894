<?php

// @codingStandardsIgnoreLine because of no namespace set
class DropboxExtensionTestCase extends \PHPUnit\DbUnit\TestCase
{
    use \PHPUnit\DbUnit\TestCaseTrait;

    /**
     * @var type \PDO
     */
    private static $pdo = null;

    /**
     *
     * @var DefaultConnection
     */
    private $conn = null;

    /**
     * Jo<PERSON>la Configuration
     *
     * @var JConfig
     */
    private static $jConfig = null;

    /**
     * Dataset for Dropbox Connection
     *
     * @return Dropbox_DbUnit_ArrayDataSet
     */
    protected function getDataSet()
    {
        self::$jConfig = new JConfig();

        include_once \dirname(__FILE__) . DS . '..' . DS . 'data' . DS . 'arrayDataset.php';
        return new Dropbox_DbUnit_ArrayDataSet(
            [
                self::$jConfig->dbprefix . "dropbox" => [
                    [
                    'id'             => '10001',
                    'dropbox_token'  => 'QxbiGRnZuYgAAAAAAAAAzNfRiTmuEYoOqpYW0DYchK_5-yLJTmEnmAkHXMauDoRK',
                    'dropbox_secret' => 'QxbiGRnZuYgAAAAAAAAAyyEs4sutyEwlbHAOYY8t0DI',
                    'folder'         => 'unit_tests',
                    'params'         => '{"function_list":"","function_upload":"","function_pic":"","log_downloads":"","log_uploads":"","view_downloads_in_browser":"","private_user_directories":"","notification_email_sender_address":"","notification_email_recipient_address":"","max_cache_size":"","max_filesize_in_cache":"","do_verify_the_peer_ssl_certificate":"","show_breadcrumbs":"","show_search":"1","date_format":"","time_zone":"","list_sorting_field":"","list_sorting_dir":"","list_show_modified_date":"","list_show_size":"","change_folder_after_upload":"","allow_subfolder_upload":"","description_for_upload":"","add_timestamp_to_upload":"","th_size_pic":"small","th_per_row":"","enl_brdsize":"","enl_brdcolor":"","enl_brdbck":"","enl_brdround":"","enl_maxstep":"","enl_speed":"","enl_ani":"","enl_opaglide":"","enl_shadow":"","enl_shadowsize":"10","enl_shadowcolor":"","enl_shadowintens":"","enl_dark":"","enl_darkprct":"50","enl_darksteps":"20","enl_center":"","enl_drgdrop":"","enl_preload":"","enl_titlebar":"","enl_keynav":"","enl_wheelnav":"","enl_titletxtcol":""}',
                    'state'          => '0',
                    'access'         => '1',
                    'box_type'       => 'dropbox',
                    'username'       => '',
                    'password'       => '',
                    ],
                    [
                    'id'             => '10002',
                    'dropbox_token'  => 'QxbiGRnZuYgAAAAAAAAAzNfRiTmuEYoOqpYW0DYchK_5-yLJTmEnmAkHXMauDoRK',
                    'dropbox_secret' => 'QxbiGRnZuYgAAAAAAAAAyyEs4sutyEwlbHAOYY8t0DI',
                    'folder'         => 'unit_tests/files',
                    'params'         => '{"function_list":"","function_upload":"","function_pic":"","log_downloads":"","log_uploads":"","view_downloads_in_browser":"","private_user_directories":"","notification_email_sender_address":"","notification_email_recipient_address":"","max_cache_size":"","max_filesize_in_cache":"","do_verify_the_peer_ssl_certificate":"","show_breadcrumbs":"","show_search":"1","date_format":"","time_zone":"","list_sorting_field":"","list_sorting_dir":"","list_show_modified_date":"","list_show_size":"","change_folder_after_upload":"","allow_subfolder_upload":"","description_for_upload":"","add_timestamp_to_upload":"","th_size_pic":"small","th_per_row":"","enl_brdsize":"","enl_brdcolor":"","enl_brdbck":"","enl_brdround":"","enl_maxstep":"","enl_speed":"","enl_ani":"","enl_opaglide":"","enl_shadow":"","enl_shadowsize":"10","enl_shadowcolor":"","enl_shadowintens":"","enl_dark":"","enl_darkprct":"50","enl_darksteps":"20","enl_center":"","enl_drgdrop":"","enl_preload":"","enl_titlebar":"","enl_keynav":"","enl_wheelnav":"","enl_titletxtcol":""}',
                    'state'          => '0',
                    'access'         => '1',
                    'box_type'       => 'dropbox',
                    'username'       => '',
                    'password'       => '',
                    ],
                    [
                    'id'             => '10003',
                    'dropbox_token'  => 'QxbiGRnZuYgAAAAAAAAAzNfRiTmuEYoOqpYW0DYchK_5-yLJTmEnmAkHXMauDoRK',
                    'dropbox_secret' => 'QxbiGRnZuYgAAAAAAAAAyyEs4sutyEwlbHAOYY8t0DI',
                    'folder'         => 'unit_tests/files/',
                    'params'         => '{"function_list":"","function_upload":"","function_pic":"","log_downloads":"","log_uploads":"","view_downloads_in_browser":"","private_user_directories":"","notification_email_sender_address":"","notification_email_recipient_address":"","max_cache_size":"","max_filesize_in_cache":"","do_verify_the_peer_ssl_certificate":"","show_breadcrumbs":"","show_search":"1","date_format":"","time_zone":"","list_sorting_field":"","list_sorting_dir":"","list_show_modified_date":"","list_show_size":"","change_folder_after_upload":"","allow_subfolder_upload":"","description_for_upload":"","add_timestamp_to_upload":"","th_size_pic":"small","th_per_row":"","enl_brdsize":"","enl_brdcolor":"","enl_brdbck":"","enl_brdround":"","enl_maxstep":"","enl_speed":"","enl_ani":"","enl_opaglide":"","enl_shadow":"","enl_shadowsize":"10","enl_shadowcolor":"","enl_shadowintens":"","enl_dark":"","enl_darkprct":"50","enl_darksteps":"20","enl_center":"","enl_drgdrop":"","enl_preload":"","enl_titlebar":"","enl_keynav":"","enl_wheelnav":"","enl_titletxtcol":""}',
                    'state'          => '0',
                    'access'         => '1',
                    'box_type'       => 'dropbox',
                    'username'       => '',
                    'password'       => '',
                    ],
                    [
                    'id'             => '10004',
                    'dropbox_token'  => 'QxbiGRnZuYgAAAAAAAAAzNfRiTmuEYoOqpYW0DYchK_5-yLJTmEnmAkHXMauDoRK',
                    'dropbox_secret' => 'QxbiGRnZuYgAAAAAAAAAyyEs4sutyEwlbHAOYY8t0DI',
                    'folder'         => '/unit_tests/files',
                    'params'         => '{"function_list":"","function_upload":"","function_pic":"","log_downloads":"","log_uploads":"","view_downloads_in_browser":"","private_user_directories":"","notification_email_sender_address":"","notification_email_recipient_address":"","max_cache_size":"","max_filesize_in_cache":"","do_verify_the_peer_ssl_certificate":"","show_breadcrumbs":"","show_search":"1","date_format":"","time_zone":"","list_sorting_field":"","list_sorting_dir":"","list_show_modified_date":"","list_show_size":"","change_folder_after_upload":"","allow_subfolder_upload":"","description_for_upload":"","add_timestamp_to_upload":"","th_size_pic":"small","th_per_row":"","enl_brdsize":"","enl_brdcolor":"","enl_brdbck":"","enl_brdround":"","enl_maxstep":"","enl_speed":"","enl_ani":"","enl_opaglide":"","enl_shadow":"","enl_shadowsize":"10","enl_shadowcolor":"","enl_shadowintens":"","enl_dark":"","enl_darkprct":"50","enl_darksteps":"20","enl_center":"","enl_drgdrop":"","enl_preload":"","enl_titlebar":"","enl_keynav":"","enl_wheelnav":"","enl_titletxtcol":""}',
                    'state'          => '0',
                    'access'         => '1',
                    'box_type'       => 'dropbox',
                    'username'       => '',
                    'password'       => '',
                    ],
                ],
            ]
        );
    }

    /**
     *
     * @return PHPUnit_Extensions_Database_DB_IDatabaseConnection
     */
    public function getConnection()
    {
        self::$jConfig = new JConfig();

        if ($this->conn === null) {
            try {
                self::$pdo = new PDO(
                    'mysql:host=' . self::$jConfig->host . ';dbname=' . self::$jConfig->db,
                    self::$jConfig->user,
                    self::$jConfig->password,
                    [
                            PDO::ATTR_PERSISTENT => true,
                    ]
                );
                $this->conn = $this->createDefaultDBConnection(
                    self::$pdo,
                    self::$jConfig->db
                );
            } catch (PDOException $e) {
                echo $e->getMessage();
            }
        }

        self::initDatabase();
        return $this->conn;
    }

    /**
     * Initializes the database.
     */
    public static function initDatabase()
    {
        $query = "CREATE TABLE IF NOT EXISTS 
				  `" . self::$jConfig->dbprefix . "dropbox` (
				  `id` int(11) NOT NULL,
				  `dropbox_token` varchar(255) NOT NULL,
				  `dropbox_secret` varchar(50) NOT NULL,
				  `dropbox_state` int(1) NOT NULL DEFAULT '0',
				  `folder` varchar(255) NOT NULL,
				  `params` text NOT NULL,
				  `state` tinyint(3) NOT NULL DEFAULT '0',
				  `access` int(11) NOT NULL DEFAULT '0',
				  `box_type` varchar(15) NOT NULL,
				  `username` varchar(255) NOT NULL,
				  `password` varchar(255) NOT NULL
				);
				ALTER TABLE `" . self::$jConfig->dbprefix . "dropbox`
				  ADD PRIMARY KEY (`id`); ";

        self::$pdo->execute($query);
    }

    protected function tearDown()
    {
        parent::tearDown();
        self::$pdo = null;
        //we should also set the PDOStatement Object (return of $pdo->query to null
        //but even then PDO would keep on creating new connections
        //PDO::ATTR_PERSISTENT => true in getConnection seems to work better
        //as it reuses the connection
    }

    protected function generateRandomString($length = 10)
    {
        $characters       = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = \strlen($characters);
        $randomString     = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}
