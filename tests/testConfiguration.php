<?php

// @codingStandardsIgnoreLine because of no namespace set
class TestConfiguration
{
    protected $settings = [
            'pathToJoomla'  => "/home/<USER>/www/test_joomla35",
            'baseUrl'       => 'http://localhost/test_joomla35',
            'adminUserName' => 'admin',
            'adminPassword' => 'admin',
            'dBUserName'    => '<EMAIL>',
            'dBPassword'    => 'U4MbbLj8Jjm7Yw6s',
            'dropboxId'     => 10001,
    ];

    public function getValue($value)
    {
        return $this->settings[$value];
    }
}
