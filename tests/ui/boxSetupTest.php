<?php

use Test\PageObject as PageObject;

// @codingStandardsIgnoreLine because of no namespace set
class BoxSetupTest extends UITest
{
    protected $dropboxPage;

    public function setUp()
    {
        parent::setUp();

        $this->dropboxPage = new PageObject\DropboxPage(
            $this->webDriver,
            $this->baseUrl
        );
        $this->loginToAdmin();
        $this->dropboxPage->open($this->baseUrl);
    }

    //TODO make more variations
    public function testCreateNewBox()
    {
        $authCode        = "";
        $dropboxEditPage = $this->dropboxPage->createNewBox();
        $folderName      = $this->generateRandomString();
        $dropboxEditPage->setFolderName($folderName);
        $dropboxEditPage->saveAndClose();
        $connectionTRs = $this->dropboxPage->getAllConnectionsOnPage();

        $connectionTD = $connectionTRs [\count($connectionTRs) - 1]->findElements(
            WebDriverBy::xpath("td")
        );

        $dropboxId = (int) $connectionTD [1]->getText();

        $this->assertGreaterThanOrEqual(1, $dropboxId);
        $this->assertEquals($folderName, $connectionTD [2]->getText());
        $this->assertEquals("Not Connected", $connectionTD [3]->getText());
        $this->assertEquals("Public", $connectionTD [4]->getText());
        $this->assertEquals("Connect", $connectionTD [5]->getText());

        $dropboxEditPage       = $this->dropboxPage->editDropbox($folderName);
        $dropboxComLoginPage   = $dropboxEditPage->connectToDropbox();
        $dropboxComAPIAuthPage = $dropboxComLoginPage->login(
            $this->testConfig->getValue('dBUserName'),
            $this->testConfig->getValue('dBPassword')
        );
        $authCode = $dropboxComAPIAuthPage->acceptAuthRequest();

        $dropboxEditPage->fillDropboxSecret($authCode);
        $dropboxEditPage->saveAndClose();
    }

    public function testDeleteConnection()
    {
        $folderName   = "unit_tests";
        $trElement    = $this->dropboxPage->getConnectionByFolderName($folderName);
        $tdElements   = $trElement->findElements(WebDriverBy::xpath("td"));
        $dropboxId    = (int)$tdElements[1]->getText();
        $oldDropboxId = $dropboxId;
        $this->assertGreaterThan(1, $dropboxId);
        $this->assertEquals($tdElements[2]->getText(), $folderName);
        $this->assertEquals($tdElements[3]->getText(), "Connected");
        $this->assertEquals($tdElements[5]->getText(), "Delete Connection");

        $this->dropboxPage->deleteConnectionByFolderName($folderName);
        $successMessage = $this->dropboxPage->getMessage();
        $this->assertContains('Dropbox disconnected', $successMessage->getText());

        $trElement  = $this->dropboxPage->getConnectionByFolderName($folderName);
        $tdElements = $trElement->findElements(WebDriverBy::xpath("td"));
        $dropboxId  = (int)$tdElements[1]->getText();
        $this->assertGreaterThan(1, $dropboxId);
        $this->assertEquals($dropboxId, $oldDropboxId);
        $this->assertEquals($tdElements[2]->getText(), $folderName);
        $this->assertEquals($tdElements[3]->getText(), "Not Connected");
        $this->assertEquals($tdElements[5]->getText(), "Connect");
    }

    public function testDeleteBox()
    {
        $connectionId = "10002";
        $this->dropboxPage->selectBoxById($connectionId);
        $this->dropboxPage->deleteSelectedBoxes();
        $successMessage = $this->dropboxPage->getMessage();
        $this->assertContains(
            '1 box(es) successfully deleted',
            $successMessage->getText()
        );
    }
}
