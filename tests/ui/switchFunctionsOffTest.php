<?php

use Test\PageObject as PageObject;

// @codingStandardsIgnoreLine because of no namespace set
class SwitchFunctionsOffTest extends UITest
{
    protected $dropboxPage;

    public function setUp()
    {
        parent::setUp();

        $this->dropboxPage = new PageObject\DropboxPage(
            $this->webDriver,
            $this->baseUrl
        );
        $this->loginToAdmin();
        $this->dropboxPage->open($this->baseUrl);
    }

    public function testSwitchSearchOff()
    {
        $dropboxEditPage = $this->dropboxPage->editDropbox("unit_tests");
        $dropboxEditPage->changeSetting("List function", "show_search", "No");
        $dropboxEditPage->saveAndClose();
        $dropboxFrontEndPage = new PageObject\DropboxFrontendPage(
            $this->webDriver,
            $this->baseUrl
        );
        $dropboxFrontEndPage->open($this->dropboxId);
        $this->expectException("NoSuchElementException");
        $dropboxFrontEndPage->getSearchInputField();
    }

    public function testSwitchGalleryOff()
    {
        $dropboxEditPage = $this->dropboxPage->editDropbox("unit_tests");
        $dropboxEditPage->changeSetting("Parameters", "function_pic", "No");
        $dropboxEditPage->saveAndClose();
        $dropboxFrontEndPage = new PageObject\DropboxFrontendPage(
            $this->webDriver,
            $this->baseUrl
        );
        $dropboxFrontEndPage->open($this->dropboxId);
        $this->expectException("NoSuchElementException");
        $dropboxFrontEndPage->getGallery();
    }
}
