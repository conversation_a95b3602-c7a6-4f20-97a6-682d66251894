<?php

use Test\PageObject as PageObject;

// @codingStandardsIgnoreLine because of no namespace set
class FolderAndFileActionTest extends UITest
{
    protected $dropboxPage;
    public function setUp()
    {
        parent::setUp();

        $this->dropboxPage = new PageObject\DropboxFrontendPage(
            $this->webDriver,
            $this->baseUrl
        );
        $this->dropboxPage->open($this->dropboxId);
    }

    public function testSimpleFileDownload()
    {
        $fileContent         = $this->dropboxPage->downloadFileByName("simple_file.txt");
        $fileContentExpected = <<<EOT
This is a simple test file
we need some more lines here
and some more

EOT;
        $this->assertEquals($fileContentExpected, $fileContent);
    }

    public function testSimpleFileDownloadInSubfolder()
    {
        $this->dropboxPage->navigateIntoFolder("files");
        $fileContent         = $this->dropboxPage->downloadFileByName("simple_file.txt");
        $fileContentExpected = <<<EOT
This is a simple test file
we need some more lines here
and some more

EOT;
        $this->assertEquals($fileContentExpected, $fileContent);
    }

    public function testSpecialCharacterFileNameDownload()
    {
        $fileContent         = $this->dropboxPage->downloadFileByName("special char äöüß &%.txt");
        $fileContentExpected = <<<EOT
This is a simple test file that contains NON-ASCII characters in the name
we need some more lines here
and some more

EOT;
        $this->assertEquals($fileContentExpected, $fileContent);
    }

    public function testSpecialCharacterFileNameDownloadInSubfolderWithSpecialCharacters()
    {
        $this->dropboxPage->navigateIntoFolder("sub folder with spaces");
        $this->dropboxPage->navigateIntoFolder("special char äöüß and more नेपाल");
        $fileContent         = $this->dropboxPage->downloadFileByName("special char äöüß &%.txt");
        $fileContentExpected = <<<EOT
This is a simple test file that contains NON-ASCII characters in the name
we need some more lines here
and some more

EOT;
        $this->assertEquals($fileContentExpected, $fileContent);
    }

    public function testSimpleFolderChange()
    {
        $this->dropboxPage->navigateIntoFolder("files");
        $this->dropboxPage->findFileByName("harddrive.png");
        $this->dropboxPage->navigateToParentFolder();
        $this->assertInstanceOf(
            "RemoteWebElement",
            $this->dropboxPage->findFileByName("files")
        );
    }

    public function testFolderChangeWithSpecialCharacters()
    {
        $this->dropboxPage->navigateIntoFolder("sub folder with spaces");
        $this->dropboxPage->navigateIntoFolder("special char äöüß and more नेपाल");
        $this->dropboxPage->findFileByName("special char äöüß.txt");
        $this->dropboxPage->navigateToParentFolder();
        $this->assertInstanceOf(
            "RemoteWebElement",
            $this->dropboxPage->findFileByName("special char äöüß and more नेपाल")
        );
    }

    public function testFolderChangeIntoFolderThatLooksLikeURLEncoded()
    {
        $this->dropboxPage->navigateIntoFolder("folder%20test");
        $this->assertInstanceOf(
            "RemoteWebElement",
            $this->dropboxPage->findFileByName("harddrive.png")
        );
    }
}
