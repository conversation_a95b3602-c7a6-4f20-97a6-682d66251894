<?php

use Test\PageObject as PageObject;

// @codingStandardsIgnoreLine because of no namespace set
class galleryTest extends UITest
{
    protected $galleryPage;
    protected $dropboxPage;
    protected $images = [
            'kaefer_hinten.gif',
            'kaefer_hinten.png',
            'Colorado.jpg',
            'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
            'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.png',
    ];

    protected $subFolders = [
            "simple" => ["files"],
            "UTF"    => ["special char äöüß and more नेपाल"],
    ];

    public function setUp()
    {
        parent::setUp();

        $this->dropboxPage = new PageObject\DropboxFrontendPage(
            $this->webDriver,
            $this->baseUrl
        );
        $this->dropboxPage->open($this->dropboxId);

        $this->galleryPage = $this->dropboxPage->getGallery();
    }

    public function testSimpleThumbnailVisibility()
    {
        //we do not use a dataProvider because this is faster
        //as it does not need to re-open the page
        foreach ($this->images as $imageName) {
            $thumbnail = $this->galleryPage->findThumbnailByName($imageName);
            $this->assertThumbnailIsVisible($thumbnail);
        }
    }

    /**
     * @dataProvider subFolders
     * @param string $subFolder
     */
    public function testThumbnailVisibilityInSubFolder($subFolder)
    {
        $this->dropboxPage->navigateIntoFolder($subFolder);
        $this->galleryPage = $this->dropboxPage->getGallery();
        //we do not use a dataProvider because this is faster
        //as it does not need to re-open the page
        foreach ($this->images as $imageName) {
            $thumbnail = $this->galleryPage->findThumbnailByName($imageName);
            $this->assertThumbnailIsVisible($thumbnail);
        }
    }

    public function subFolders()
    {
        return [
                "simple" => ["files"],
                "UTF"    => ["special char äöüß and more नेपाल"],
        ];
    }

    /**
     * asserts that a thumbnail is displayed
     *
     * @param RemoteWebElement $thumbnail
     */
    private function assertThumbnailIsVisible(RemoteWebElement $thumbnail)
    {
        $this->assertTrue($thumbnail->isDisplayed());
        $this->assertGreaterThan(0, $thumbnail->getLocation()->getX());
        $this->assertGreaterThan(0, $thumbnail->getLocation()->getY());

        //TODO would be good to tests here if the size is
        //actually the one that is set in the settings
        $this->assertGreaterThan(0, $thumbnail->getSize()->getHeight());
        $this->assertGreaterThan(0, $thumbnail->getSize()->getWidth());
        $downloadedThumbnail = $this->dropboxPage->downloadFile($thumbnail->getAttribute("src"));
        $this->assertNotFalse($downloadedThumbnail);

        $this->assertNotEmpty($downloadedThumbnail);
        $tmpfname = tempnam(sys_get_temp_dir(), 'dropboxExtensionUnitTest');
        $this->assertIsWritable($tmpfname, "Could not create temp file");
        $tempFileHandle = fopen($tmpfname, "w");
        fwrite($tempFileHandle, $downloadedThumbnail);
        fclose($tempFileHandle);

        //it has to be a JPG file
        $this->assertEquals(2, exif_imagetype($tmpfname));
    }
}
