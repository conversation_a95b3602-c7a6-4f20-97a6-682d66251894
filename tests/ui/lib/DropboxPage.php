<?php

namespace Test\PageObject;

class DropboxPage extends DropboxAdminPage
{
    protected $path                 = "/index.php?option=com_dropbox";
    protected $title                = "Administration - Dropbox Manager";
    protected $newBoxButtonXpath    = "//button[@onclick=\"Joomla.submitbutton('dropbox.add')\"]";
    protected $connectionsTrXpath   = "//table[contains(@class, 'table-striped')]/tbody/tr";
    protected $boxSelectXpath       = ".//input[@value='%d']";
    protected $deleteButtonId       = "toolbar-delete";
    protected $messageClassName     = "alert-message";
    protected $deleteConnectionText = "Delete Connection";

    public function createNewBox()
    {
        $addNewBoxButton = $this->webDriver->findElement(
            \WebDriverBy::xpath($this->newBoxButtonXpath)
        );

        $addNewBoxButton->click();

        return new DropboxEditPage($this->webDriver, $this->baseUrl);
    }

    /**
     * gets all listed connections on page
     *
     * @return RemoteWebElement[]
     */
    public function getAllConnectionsOnPage()
    {
        $trElements = $this->webDriver->findElements(
            \WebDriverBy::xpath($this->connectionsTrXpath)
        );

        return $trElements;
    }

    /**
     *
     * @param  string                            $name
     * @return \Test\PageObject\RemoteWebElement
     */
    public function getConnectionByFolderName($name)
    {
        $trElements = $this->getAllConnectionsOnPage();

        foreach ($trElements as $trElement) {
            $tdElement = $trElement->findElements(\WebDriverBy::xpath("td"));
            if ($tdElement[2]->getText() === $name) {
                return $trElement;
            }
        }
    }

    /**
     *
     * @param  string                            $name
     * @return \Test\PageObject\RemoteWebElement
     */
    public function findEditLinkByFolderName($name)
    {
        $connectionTR = $this->getConnectionByFolderName($name);

        return $connectionTR->findElement(\WebDriverBy::partialLinkText($name));
    }

    /**
     * finds a box based on the id and selects the corresponding checkbox
     *
     * @param  int              $id
     * @return RemoteWebElement
     */
    public function selectBoxById($id)
    {
        return $this->webDriver->findElement(
            \WebDriverBy::xpath(sprintf($this->boxSelectXpath, $id))
        )->click();
    }

    /**
     * finds the "delete" button and clicks it
     *
     * @return RemoteWebElement
     */
    public function deleteSelectedBoxes()
    {
        return $this->webDriver->findElement(
            \WebDriverBy::id($this->deleteButtonId)
        )->click();
    }

    /**
     * finds and returns firsts message
     *
     * @return RemoteWebElement
     */
    public function getMessage()
    {
        return $this->webDriver->findElement(
            \WebDriverBy::className($this->messageClassName)
        );
    }

    /**
     * finds and clicks the "Delete Connection" Link of the connection named "$name"
     *
     * @param  string           $name
     * @return RemoteWebElement
     */
    public function deleteConnectionByFolderName($name)
    {
        $connectionTR = $this->getConnectionByFolderName($name);
        return $connectionTR->findElement(
            \WebDriverBy::linkText($this->deleteConnectionText)
        )->click();
    }

    public function editDropbox($name)
    {
        $this->findEditLinkByFolderName($name)->click();

        return new DropboxEditPage($this->webDriver, $this->baseUrl);
    }
}
