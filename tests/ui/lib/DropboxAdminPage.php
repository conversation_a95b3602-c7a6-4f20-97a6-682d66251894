<?php

namespace Test\PageObject;

abstract class DropboxAdminPage extends SeleniumPage
{
    public function open()
    {
        $this->webDriver->get($this->adminUrl . $this->path);

        \PHPUnit\Framework\Assert::assertContains(
            $this->title,
            $this->webDriver->getTitle()
        );

        \PHPUnit\Framework\Assert::assertEquals(
            $this->adminUrl . $this->path,
            $this->webDriver->getCurrentURL()
        );

        return $this;
    }
}
