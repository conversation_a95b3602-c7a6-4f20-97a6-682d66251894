<?php

namespace Test\PageObject;

class DropboxComLoginPage extends SeleniumPage
{
    protected $path               = "https://www.dropbox.com";
    protected $title              = "Dropbox - API request authorisation - sign in";
    protected $usernameInputXpath = "//input[@type='email']";
    protected $passwordInputXpath = "//input[@type='password']";
    protected $loginButtonXpath   = "//button[@type='submit']";

    public function login($username, $password)
    {
        \PHPUnit\Framework\Assert::assertContains(
            $this->path,
            $this->webDriver->getCurrentURL()
        );

        $this->webDriver->findElement(
            \WebDriverBy::xpath($this->usernameInputXpath)
        )->sendKeys(
            $username
        );

        $this->webDriver->findElement(
            \WebDriverBy::xpath($this->passwordInputXpath)
        )->sendKeys(
            $password
        );

        $loginButton = $this->webDriver->findElement(
            \WebDriverBy::xpath($this->loginButtonXpath)
        );
        $loginButton->click();

        $this->waitTillElementIsStale($loginButton);

        return new DropboxComAPIAuthPage($this->webDriver, $this->adminUrl);
    }
}
