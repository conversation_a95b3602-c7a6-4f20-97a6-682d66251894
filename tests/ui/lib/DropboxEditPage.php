<?php

namespace Test\PageObject;

class DropboxEditPage extends DropboxAdminPage
{
    protected $path                    = "/index.php?option=com_dropbox&view=dropbox&layout=edit";
    protected $title                   = "Administration - Dropbox: [ Edit ]";
    protected $folderNameInputId       = "jform_folder";
    protected $saveAndCloseButtonXpath = "//button[@onclick=\"Joomla.submitbutton('dropbox.save')\"]";
    protected $connectButtonXpath      = "//button[@onclick=\"Joomla.submitbutton('dropbox.connect')\"]";
    protected $dropboxSecretInputId    = 'jform_dropbox_secret';
    protected $settingsFormId          = "link-form";
    protected $paramsDropdownId        = "jform_params_%s_chzn";

    public function setFolderName($name)
    {
        $folderInput = $this->webDriver->findElement(
            \WebDriverBy::id($this->folderNameInputId)
        );
        $folderInput->click();
        $folderInput->sendKeys($name);

        return $this;
    }

    public function saveAndClose()
    {
        $saveAndClose = $this->webDriver->findElement(
            \WebDriverBy::xpath($this->saveAndCloseButtonXpath)
        );

        $saveAndClose->click();

        return new DropboxPage($this->webDriver, $this->baseUrl);
    }

    public function connectToDropbox()
    {
        $this->webDriver->findElement(
            \WebDriverBy::xpath($this->connectButtonXpath)
        )->click();
        $windowHandles = $this->webDriver->getWindowHandles();

        $this->webDriver->switchTo()->window(
            $windowHandles [\count($windowHandles) - 1]
        );

        return new DropboxComLoginPage($this->webDriver, $this->baseUrl);
    }

    public function fillDropboxSecret($code)
    {
        $secretInput = $this->webDriver->findElement(
            \WebDriverBy::id($this->dropboxSecretInputId)
        );
        $secretInput->click();
        $secretInput->sendKeys($code);
    }

    /**
     * changes a setting for the dropbox connection (currently only works on dropdown settings)
     *
     * @param  string           $tab     Tab Name Details|Parameters|List function|Upload function|Pictures function
     * @param  string           $setting internal name of the setting e.g. show_breadcrumbs
     * @param  string           $value   visible value to be changed to mostly "Yes,No,Use Global"
     * @return RemoteWebElement
     */
    public function changeSetting($tab, $setting, $value)
    {
        $settingsForm = $this->webDriver->findElement(
            \WebDriverBy::id($this->settingsFormId)
        );
        $settingsTab = $settingsForm->findElement(\WebDriverBy::linkText($tab));
        $settingsTab->click();

        $select = $settingsForm->findElement(
            \WebDriverBy::id(sprintf($this->paramsDropdownId, $setting))
        );

        \PHPUnit\Framework\Assert::assertTrue(
            $select->isDisplayed(),
            "setting '$setting' not visible in tab '$tab'"
        );

        $select->click();
        return $select->findElement(
            \WebDriverBy::xpath("//li[contains(text(), '" . $value . "')]")
        )->click();
    }
}
