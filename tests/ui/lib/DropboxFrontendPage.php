<?php

namespace Test\PageObject;

class DropboxFrontendPage extends SeleniumPage
{
    protected $path                   = "index.php?option=com_dropbox";
    protected $dropboxWrapper         = "";
    protected $connectionId           = "";
    protected $loadingIndicatorId     = 'dropbox_loading_indicator';
    protected $parentFolderClass      = "directory_up";
    protected $searchInputId          = 'dropbox_file_search_%d';
    protected $allFilesFoldersXpath   = "//div[@class='file_listing_table']//a[@class!='directory_up']";
    protected $resetSearchButtonXpath = "//button[@type='reset']";

    public function open($connectionId)
    {
        $this->webDriver->get(
            $this->baseUrl . "/" . $this->path . "&id=" . $connectionId
        );

        $this->connectionId   = $connectionId;
        $this->dropboxWrapper = $this->webDriver->findElement(
            \WebDriverBy::id("dropbox_wrapper_" . $this->connectionId)
        );

        return $this;
    }

    public function navigateIntoFolder($folderName)
    {
        $this->dropboxWrapper->findElement(\WebDriverBy::linkText($folderName))->click();
        $loadingIndicator = $this->dropboxWrapper->findElement(
            \WebDriverBy::id($this->loadingIndicatorId)
        );
        $this->waitTillElementIsNotDisplayed($loadingIndicator);
        return $this;
    }

    public function navigateToParentFolder()
    {
        $this->dropboxWrapper->findElement(
            \WebDriverBy::className($this->parentFolderClass)
        )->click();
        $loadingIndicator = $this->dropboxWrapper->findElement(
            \WebDriverBy::id($this->loadingIndicatorId)
        );
        $this->waitTillElementIsNotDisplayed($loadingIndicator);

        return $this;
    }

    /**
     * finds a file or folder in the current folder by its name and returns
     * the RemoteWebElement of the link
     *
     * @param  string           $name
     * @return RemoteWebElement
     */
    public function findFileByName($name)
    {
        return $this->dropboxWrapper->findElement(\WebDriverBy::linkText($name));
    }

    /**
     * finds all files/folders in the current view by its name
     *
     * @param  string             $name
     * @return RemoteWebElement[]
     */
    public function findFilesByName($name)
    {
        return $this->dropboxWrapper->findElements(\WebDriverBy::linkText($name));
    }

    /**
     * finds all listed Files and Folders in the current view
     *
     * @return RemoteWebElement[]
     */
    public function getAllFilesAndFolders()
    {
        return $this->dropboxWrapper->findElements(
            \WebDriverBy::xpath($this->allFilesFoldersXpath)
        );
    }

    /**
     * Downloads a file from the current shown folder and returns its content
     *
     * @param  string $fileName
     * @return mixed
     */
    public function downloadFileByName($fileName)
    {
        $fileToDownload = $this->findFileByName($fileName);
        return $this->downloadFile($fileToDownload->getAttribute("href"));
    }

    /**
     * makes a curl request to download a file
     *
     * @param  string $url
     * @return mixed
     */
    public function downloadFile($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        return curl_exec($ch);
    }

    public function getGallery()
    {
        return new DropboxGalleryPage($this->webDriver, $this->baseUrl, $this->connectionId);
    }

    /**
     * resets the search
     *
     * @return RemoteWebElement
     */
    public function resetSearch()
    {
        $this->dropboxWrapper->findElement(
            \WebDriverBy::xpath($this->resetSearchButtonXpath)
        )->click();
        $loadingIndicator = $this->dropboxWrapper->findElement(
            \WebDriverBy::id($this->loadingIndicatorId)
        );
        $this->waitTillElementIsNotDisplayed($loadingIndicator);
        return $this;
    }

    /**
     * performs a search
     *
     * @param  string                               $query
     * @return \Test\PageObject\DropboxFrontendPage
     */
    public function search($query)
    {
        $search = $this->webDriver->findElement(
            \WebDriverBy::id(
                sprintf($this->searchInputId, $this->connectionId)
            )
        )->sendKeys($query);

        $this->webDriver->getKeyboard()->pressKey(\WebDriverKeys::ENTER);
        $loadingIndicator = $this->dropboxWrapper->findElement(
            \WebDriverBy::id($this->loadingIndicatorId)
        );
        $this->waitTillElementIsNotDisplayed($loadingIndicator);
        return $this;
    }

    /**
     * returns the search input field
     *
     * @return RemoteWebElement
     */
    public function getSearchInputField()
    {
        return $this->webDriver->findElement(
            \WebDriverBy::id(
                sprintf($this->searchInputId, $this->connectionId)
            )
        );
    }

    public function uploadFile($file, $destinationDirectory)
    {
        throw new \Exception("not implemented");
    }
}
