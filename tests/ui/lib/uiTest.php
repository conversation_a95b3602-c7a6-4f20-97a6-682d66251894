<?php

// @codingStandardsIgnoreLine because of no namespace set
class UITest extends DropboxExtensionTestCase
{
    /**
     *
     * @var \RemoteWebDriver
     */
    protected $webDriver;
    protected $testConfig;
    protected $dropboxId;
    protected $adminUrl;
    protected $baseUrl;

    public function __construct($name = null, array $data = [], $dataName = '')
    {
        parent::__construct($name, $data, $dataName);
        $this->testConfig = new TestConfiguration();
        $this->baseUrl    = $this->testConfig->getValue("baseUrl");
        $this->adminUrl   = $this->baseUrl . "/administrator";
        $this->dropboxId  = $this->testConfig->getValue("dropboxId");
    }

    protected function getFullUrl()
    {
        return $this->testConfig->getValue('baseUrl') .
                "/index.php?option=com_dropbox&view=dropbox&id=" .
                $this->testConfig->getValue('dropboxId');
    }

    protected function waitForUserInput()
    {
        if (trim(fgets(fopen("php://stdin", "r"))) != \chr(13)) {
            return;
        }
    }

    protected function assertElementNotFound($by)
    {
        $els = $this->webDriver->findElements($by);
        if (\count($els)) {
            $this->fail("Unexpectedly element was found");
        }
        // increment assertion counter
        $this->assertTrue(true);
    }

    //TODO this really needs to go in a PageObject
    protected function loginToAdmin()
    {
        $this->webDriver->get($this->adminUrl);

        $login = $this->webDriver->findElement(WebDriverBy::id("mod-login-username"));
        $login->click();
        $login->sendKeys($this->testConfig->getValue('adminUserName'));

        $login = $this->webDriver->findElement(WebDriverBy::id("mod-login-password"));
        $login->click();
        $login->sendKeys($this->testConfig->getValue('adminPassword'));

        $login = $this->webDriver->findElement(WebDriverBy::xpath("//form[@id='form-login']/fieldset/div[4]/div/div/button"));
        $login->click();
    }

    //TODO this really needs to go in a PageObject
    protected function changeDbSetting($id, $category, $setting, $value)
    {
        $this->webDriver->get($this->adminUrl . "/index.php?option=com_dropbox&view=dropbox&layout=edit&id=" . (int)$id);

        $listFunctionLink =  $this->webDriver->findElement(WebDriverBy::linkText($category));
        $listFunctionLink->click();

        $select = $this->webDriver->findElement(WebDriverBy::xpath("//div[@id='jform_params_" . $setting . "_chzn']/a/span"));
        $select->click();

        $selectNoOption = $this->webDriver->findElement(WebDriverBy::xpath("//div[@id='jform_params_" . $setting . "_chzn']/div/ul/li[contains(text(), '" . $value . "')]"));
        $selectNoOption->click();

        $saveAndClose = $this->webDriver->findElement(WebDriverBy::xpath("//button[@onclick=\"Joomla.submitbutton('dropbox.save')\"]"));

        $saveAndClose->click();

        $successMessage = $this->webDriver->findElement(WebDriverBy::className('alert-message'));

        $this->assertContains('Item successfully saved.', $successMessage->getText());
    }

    public function setUp()
    {
        parent::setUp();
        $capabilities =  [
                \WebDriverCapabilityType::BROWSER_NAME => 'chrome',
        ];
        $this->webDriver = RemoteWebDriver::create(
            'http://localhost:4444/wd/hub',
            $capabilities,
            60 * 1000, // Connection timeout in milliseconds
            60 * 1000  // Request timeout in milliseconds
        );
    }
    public function tearDown()
    {
        $this->webDriver->quit();
        parent::tearDown();
    }
}
