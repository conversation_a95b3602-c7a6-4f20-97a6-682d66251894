<?php

namespace Test\PageObject;

abstract class SeleniumPage
{
    protected $webDriver;
    protected $adminUrl;
    protected $baseUrl;

    public function __construct(\RemoteWebDriver $webDriver, $baseUrl)
    {
        $this->webDriver = $webDriver;
        $this->baseUrl   = $baseUrl;
        $this->adminUrl  = $baseUrl . "/administrator/";
    }

    public function waitTillElementIsStale($element, $timeout = 30)
    {
        for ($i = 0; $i <= $timeout; $i++) {
            try {
                $element->findElements(\WebDriverBy::id("does-not-matter"));
            } catch (\StaleElementReferenceException $e) {
                return true;
            }
            sleep(1);
        }
    }

    public function waitTillElementIsNotDisplayed($element, $timeout = 30)
    {
        for ($counter = 0; $counter <= $timeout; $counter++) {
            try {
                if ($element->isDisplayed() == false) {
                    break;
                }
            } catch (\Exception $e) {
                break;
            }

            sleep(1);
        }
    }
}
