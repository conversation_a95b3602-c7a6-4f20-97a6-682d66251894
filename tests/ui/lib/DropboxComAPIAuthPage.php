<?php

namespace Test\PageObject;

class DropboxComAPIAuthPage extends SeleniumPage
{
    protected $path               = "https://www.dropbox.com/oauth2/authorize";
    protected $acceptButtonXpath  = ".//*/button[@name='allow_access']";
    protected $authCodeInputXpath = ".//*[@id='auth-code']/input";

    /**
     * clicks on the "Accept" button and returns the auth code displayed by dropbox
     * closes the dropbox.com tab after getting the auth code
     *
     * @return string|null
     */
    public function acceptAuthRequest()
    {
        \PHPUnit\Framework\Assert::assertContains(
            $this->path,
            $this->webDriver->getCurrentURL()
        );
        $acceptButton = $this->webDriver->findElement(
            \WebDriverBy::xpath($this->acceptButtonXpath)
        );
        $acceptButton->click();

        $authInput = $this->webDriver->findElement(
            \WebDriverBy::xpath($this->authCodeInputXpath)
        );
        $authCode = $authInput->getAttribute("value");

        $windowHandles = $this->webDriver->getWindowHandles();
        $this->webDriver->close();
        $this->webDriver->switchTo()->window(
            $windowHandles[0]
        );

        return $authCode;
    }
}
