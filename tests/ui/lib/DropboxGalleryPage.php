<?php

namespace Test\PageObject;

class DropboxGalleryPage extends SeleniumPage
{
    protected $path                   = "index.php?option=com_dropbox";
    protected $dropboxPicWrapper      = null;
    protected $dropboxPicWrapperXpath = ".//*[@id='dropbox_wrapper_%d']/div[@class='dropbox_pic_wrapper']";
    protected $loadingIndicatorId     = 'dropbox_loading_indicator';
    protected $parentFolderClass      = "directory_up";

    public function __construct($webDriver, $baseUrl, $connectionId)
    {
        parent::__construct($webDriver, $baseUrl);
        $this->dropboxPicWrapper = $this->webDriver->findElement(
            \WebDriverBy::xpath(
                sprintf($this->dropboxPicWrapperXpath, $connectionId)
            )
        );

        return $this;
    }

    /**
     * finds and returns a thumbnail by its name
     *
     * @param  string           $name
     * @param  int              $timeout
     * @return RemoteWebElement
     */
    public function findThumbnailByName($name, $timeout = 10)
    {
        $encodedName = rawurlencode(\JFile::stripExt($name));
        $extension   = \JFile::getExt($name);
        $thumbnail   = $this->dropboxPicWrapper->findElement(
            \WebDriverBy::name($encodedName . "." . $extension)
        );
        $timeRun = 0;
        while (!$thumbnail->isDisplayed()) {
            sleep(1);
            $timeRun++;
            if ($timeRun > $timeout) {
                throw new \TimeOutException(
                    "thumbnail not displayed within timeout"
                );
            }
        }
        return $thumbnail;
    }

    public function magnifyPicture()
    {
        throw new \Exception("not implemented");
    }
}
