<?php

use Test\PageObject as PageObject;

// @codingStandardsIgnoreLine because of no namespace set
class SearchTest extends UITest
{
    protected $dropboxPage;
    protected $searches = [
            "jpg" => [
                    "query"              => 'jpg',
                    "returnedQuery"      => 'jpg',
                    "expectedToBeListed" => [
                            [
                                    "name"       => "Colorado.jpg",
                                    "subfolders" => [
                                            "",
                                            "/files",
                                            "/special char äöüß and more नेपाल",
                                            "/special char äöüß and more नेपाल/files",
                                    ],
                            ],
                            [
                                    "name"       => "kaefer_hinten.JPG",
                                    "subfolders" => [
                                            "",
                                            "/files",
                                            "/special char äöüß and more नेपाल" ,
                                            "/special char äöüß and more नेपाल/files",
                                    ],
                            ],
                            [
                                    "name"       => "जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg",
                                    "subfolders" => [
                                            "",
                                            "/files",
                                            "/special char äöüß and more नेपाल" ,
                                            "/special char äöüß and more नेपाल/files",
                                    ],
                            ],
                    ],
            ],
            "UTF" => [
                    "query"              => 'äöüß',
                    "returnedQuery"      => 'äöüß',
                    "expectedToBeListed" => [
                            [
                                    "name"       => "special char äöüß &%.txt",
                                    "subfolders" => [
                                            "",
                                            "/special char äöüß and more नेपाल",
                                    ],
                            ],
                            [
                                    "name"       => "special char äöüß and more नेपाल",
                                    "subfolders" => [
                                            "",
                                            "/files",
                                            "/sub folder with spaces",
                                            "/special char äöüß and more नेपाल/files",
                                    ],
                            ],
                    ],
            ],
            "space" => [
                    "query"              => 'with spaces',
                    "returnedQuery"      => 'with spaces',
                    "expectedToBeListed" => [
                            [
                                    "name"       => "file with spaces.txt",
                                    "subfolders" => [
                                            "",
                                            "/files",
                                            "/special char äöüß and more नेपाल",
                                    ],
                            ],
                            [
                                    "name"       => "sub folder with spaces",
                                    "subfolders" => [
                                            "",
                                            "/files",
                                            "/sub folder with spaces",
                                            "/special char äöüß and more नेपाल/files",
                                    ],
                            ],
                    ],
            ],
    ];

    public function setUp()
    {
        parent::setUp();

        $this->dropboxPage = new PageObject\DropboxFrontendPage(
            $this->webDriver,
            $this->baseUrl
        );
        $this->dropboxPage->open($this->dropboxId);
    }

    /**
     * @dataProvider dataProviderSearches
     * @param string $query              search query
     * @param string $returnedQuery      query that should be shown in the search box after the search returns
     * @param array  $expectedToBeListed files that are expected to be listed
     */
    public function testSimpleSearch($query, $returnedQuery, $expectedToBeListed)
    {
        $this->dropboxPage = $this->dropboxPage->search($query);

        //check if every item in the result contains the query at least somewhere
        //this is a very vague test, but better than nothing
        $searchResults = $this->dropboxPage->getAllFilesAndFolders();
        foreach ($searchResults as $result) {
            $this->assertContains($query, strtolower($result->getText()));
        }

        //check if the search field shows the expected value
        $this->assertEquals(
            $returnedQuery,
            $this->dropboxPage->getSearchInputField()->getAttribute("value")
        );

        //check if all expected files/folders are listed
        foreach ($expectedToBeListed as $expected) {
            $results = $this->dropboxPage->findFilesByName($expected ["name"]);

            $this->assertNotEmpty($results);

            //check if that every expected file is listed correctly
            foreach ($expected ["subfolders"] as $subfolder) {
                $foundLink = false;
                foreach ($results as $result) {
                    //link to a file
                    if (
                        strpos(
                            $result->getAttribute("href"),
                            "sub_folder=" . rawurlencode($subfolder) . "&"
                        )
                    ) {
                        $foundLink = true;
                        break;
                    }
                    //link to a folder
                    if (
                        strpos(
                            $result->getAttribute("onclick"),
                            "sub_folder=" . rawurlencode($subfolder . "/" . $expected ["name"])
                        )
                    ) {
                        $foundLink = true;
                        break;
                    }
                }
                $this->assertTrue(
                    $foundLink,
                    "could not find file '" . $expected ["name"] .
                    "' in folder '" . $subfolder . "'"
                );
            }
        }
    }

    public function testReset()
    {
        $this->dropboxPage = $this->dropboxPage->search("jpg");
        $this->dropboxPage->resetSearch();
        //we expect the second call to reset to fail
        $this->expectException("NoSuchElementException");
        $this->dropboxPage->resetSearch();
    }

    public function testDownloadFoundFile()
    {
        $this->dropboxPage   = $this->dropboxPage->search("simple");
        $fileContent         = $this->dropboxPage->downloadFileByName("simple_file.txt");
        $fileContentExpected = <<<EOT
This is a simple test file
we need some more lines here
and some more

EOT;
        $this->assertEquals($fileContentExpected, $fileContent);
    }

    public function testDownloadFoundFileUTFName()
    {
        $this->dropboxPage   = $this->dropboxPage->search("special char äöüß");
        $fileContent         = $this->dropboxPage->downloadFileByName("special char äöüß &%.txt");
        $fileContentExpected = <<<EOT
This is a simple test file that contains NON-ASCII characters in the name
we need some more lines here
and some more

EOT;
        $this->assertEquals($fileContentExpected, $fileContent);
    }

    public function testChangeIntoFoundFolder()
    {
        $this->dropboxPage = $this->dropboxPage->search("folder");
        $this->dropboxPage->navigateIntoFolder("sub folder with spaces");
        $this->assertNotEmpty($this->dropboxPage->findFileByName("simple_file.txt"));
    }

    public function dataProviderSearches()
    {
        return $this->searches;
    }
}
