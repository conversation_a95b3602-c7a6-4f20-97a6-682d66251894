<?php

require_once __DIR__ . '/../lib/dropboxModelThumbnailHelper.php';

// @codingStandardsIgnoreLine because of no namespace set
class ThumbnailDropboxModelTest extends DropboxExtensionTestCase
{
    //TODO use Dataprovider to make this file shorter and generate more tests
    /**
     *
     * @var DropboxModelDropbox
     */
    protected $model;

    protected $thumbnailHelper;

    public function testDownloadThumbnailIllegalSize()
    {
        $this->expectException("InvalidArgumentException");
        $this->expectExceptionMessage("illegal size");
        $this->model->setup(10001);
        $this->model->getThumbnail("kaefer_hinten.JPG", "monster");
    }

    public function testDownloadThumbnailNotExisting()
    {
        $this->expectException("Exception");
        $this->expectExceptionMessage("not_found");
        $this->model->setup(10001);
        $this->model->getThumbnail("not-existing.jpg", "small");
    }

    public function testDownloadThumbnailDifferentLegalSizeRootFolder()
    {
        foreach ($this->thumbnailHelper->getTbSizes() as $tbSize => $expectedPixelSize) {
            $this->assertArrayHasKey(
                'mime',
                $this->thumbnailHelper->getThumbnail(
                    10001,
                    "kaefer_hinten.gif",
                    $tbSize
                )
            );
        }
        foreach ($this->thumbnailHelper->getTbSizes() as $tbSize => $expectedPixelSize) {
            $this->assertArrayHasKey(
                'mime',
                $this->thumbnailHelper->getThumbnail(
                    10001,
                    "kaefer_hinten.JPG",
                    $tbSize
                )
            );
        }
        foreach ($this->thumbnailHelper->getTbSizes() as $tbSize => $expectedPixelSize) {
            $this->assertArrayHasKey(
                'mime',
                $this->thumbnailHelper->getThumbnail(
                    10001,
                    "kaefer_hinten.png",
                    $tbSize
                )
            );
        }
    }

    public function testDownloadThumbnailDifferentLegalSizeUTFNameRootFolder()
    {
        foreach ($this->thumbnailHelper->getTbSizes() as $tbSize => $expectedPixelSize) {
            $this->assertArrayHasKey(
                'mime',
                $this->thumbnailHelper->getThumbnail(
                    10001,
                    "जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.png",
                    $tbSize
                )
            );
        }
        foreach ($this->thumbnailHelper->getTbSizes() as $tbSize => $expectedPixelSize) {
            $this->assertArrayHasKey(
                'mime',
                $this->thumbnailHelper->getThumbnail(
                    10001,
                    "जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg",
                    $tbSize
                )
            );
        }
    }
    public function testDownloadThumbnailDifferentLegalSizeUTFNameSubFolder()
    {
        foreach ($this->thumbnailHelper->getTbSizes() as $tbSize => $expectedPixelSize) {
            $this->assertArrayHasKey(
                'mime',
                $this->thumbnailHelper->getThumbnail(
                    10001,
                    "special char äöüß and more नेपाल/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg",
                    $tbSize
                )
            );
        }
    }

    public function testDownloadThumbnailDifferentLegalSizeUTFNameDeepRootFolder()
    {
        foreach ($this->thumbnailHelper->getTbSizes() as $tbSize => $expectedPixelSize) {
            $this->assertArrayHasKey(
                'mime',
                $this->thumbnailHelper->getThumbnail(
                    10002,
                    "जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg",
                    $tbSize
                )
            );
        }
    }

    protected function setUp()
    {
        $this->model           = new DropboxModelDropbox();
        $this->thumbnailHelper = new DropboxModelThumbnailHelper($this->model);
        parent::setUp();
    }
}
