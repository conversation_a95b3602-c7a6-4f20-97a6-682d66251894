<?php

// @codingStandardsIgnoreLine because of no namespace set
class SearchDropboxModelTest extends DropboxExtensionTestCase
{
    /**
     *
     * @var DropboxModelDropbox
     */
    protected $model;

    protected $invalidSearchQueries = [
        "empty string" => [""],
        "null"         => [null],
        "false"        => [false],
        "false"        => [true],
        "int"          => [9404953],
        "zero"         => [0],
        "float"        => [2323.04],
        "array"        => [["aaa","bbbb"]],
    ];

    protected $validSearched = [
            "simple file" => [
                    "root"            => 10001,
                    "query"           => "jpg",
                    "path"            => "",
                    'expectedResults' => [
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'Colorado.jpg',
                                    'path_display' => '/unit_tests/Colorado.jpg',
                                    'cleanPath'    => '',
                            ],
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'path_display' => '/unit_tests/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'cleanPath'    => '',
                            ],
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'path_display' => '/unit_tests/files/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'cleanPath'    => '/files',
                            ],
                    ],
            ],
            "simple file deep root" => [
                    "root"            => 10002,
                    "query"           => "jpg",
                    "path"            => "",
                    'expectedResults' => [
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'Colorado.jpg',
                                    'path_display' => '/unit_tests/files/Colorado.jpg',
                                    'cleanPath'    => '',
                            ],
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'path_display' => '/unit_tests/files/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'cleanPath'    => '',
                            ],
                    ],
            ],
            "simple file path trailing /" => [
                    "root"            => 10001,
                    "query"           => "jpg",
                    "path"            => "/",
                    'expectedResults' => [
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'Colorado.jpg',
                                    'path_display' => '/unit_tests/Colorado.jpg',
                                    'cleanPath'    => '',
                            ],
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'path_display' => '/unit_tests/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'cleanPath'    => '',
                            ],
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'path_display' => '/unit_tests/files/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'cleanPath'    => '/files',
                            ],
                    ],
            ],
            "simple file path multiple trailing /" => [
                    "root"            => 10001,
                    "query"           => "jpg",
                    "path"            => "////",
                    'expectedResults' => [
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'Colorado.jpg',
                                    'path_display' => '/unit_tests/Colorado.jpg',
                                    'cleanPath'    => '',
                            ],
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'path_display' => '/unit_tests/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'cleanPath'    => '',
                            ],
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'path_display' => '/unit_tests/files/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'cleanPath'    => '/files',
                            ],
                    ],
            ],
            "simple folder" => [
                    "root"            => 10001,
                    "query"           => "files",
                    "path"            => "",
                    'expectedResults' => [
                            [
                                    '.tag'         => 'folder',
                                    'name'         => 'files',
                                    'path_display' => '/unit_tests/files',
                                    'cleanPath'    => '/files',
                            ],
                            [
                                    '.tag'         => 'folder',
                                    'name'         => 'files',
                                    'path_display' => '/unit_tests/files/files',
                                    'cleanPath'    => '/files/files',
                            ],
                            [
                                    '.tag'         => 'folder',
                                    'name'         => 'files',
                                    'path_display' => '/unit_tests/special char äöüß and more नेपाल/files',
                                    'cleanPath'    => '/special char äöüß and more नेपाल/files',
                            ],
                    ],
            ],
            "simple file sub folder" => [
                    "root"            => 10001,
                    "query"           => "jpg",
                    "path"            => "files",
                    'expectedResults' => [
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'Colorado.jpg',
                                    'path_display' => '/unit_tests/files/Colorado.jpg',
                                    'cleanPath'    => '/files',
                            ],
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'path_display' => '/unit_tests/files/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'cleanPath'    => '/files',
                            ],
                    ],
            ],
            "simple file UTF sub folder" => [
                    "root"            => 10001,
                    "query"           => "jpg",
                    "path"            => "special char äöüß and more नेपाल",
                    'expectedResults' => [
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'Colorado.jpg',
                                    'path_display' => '/unit_tests/special char äöüß and more नेपाल/Colorado.jpg',
                                    'cleanPath'    => '/special char äöüß and more नेपाल',
                            ],
                            [
                                    '.tag'         => 'file',
                                    'name'         => 'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'path_display' => '/unit_tests/special char äöüß and more नेपाल/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                                    'cleanPath'    => '/special char äöüß and more नेपाल',
                            ],
                    ],
            ],
    ];

    /**
     * @dataProvider dataProviderValidSearches
     * @param int    $root
     * @param string $query
     * @param string $path
     * @param array  $expectedResults
     */
    public function testSearch($root, $query, $path, $expectedResults)
    {
        $this->model->setup($root);
        $result = $this->model->search($query, $path);
        $this->assertArrayHasKey("entries", $result);
        foreach ($expectedResults as $expectedResult) {
            $this->assertTrue($this->isEntryInSearchResult($result, $expectedResult));
        }
        foreach ($result ['entries'] as $entry) {
            $this->assertContains(strtolower($query), strtolower($entry['name']));
        }
    }

    /**
     * @dataProvider dataProviderInvalidSearchQueries
     * @param mixed $query
     */
    public function testSearchInvalidQuery($query)
    {
        $this->expectException("InvalidArgumentException");
        $this->expectExceptionMessage("invalid search query");
        $this->model->search($query, "");
    }

    public function testSearchMaxResults()
    {
        $this->model->setup(10001);
        ;
        $result = $this->model->search("jpg", "", 8);
        $this->assertEquals(8, \count($result['entries']));
    }

    public function dataProviderValidSearches()
    {
        return $this->validSearched;
    }

    public function dataProviderInvalidSearchQueries()
    {
        return $this->invalidSearchQueries;
    }

    public function isEntryInSearchResult($result, $subset)
    {
        $foundSubset = false;

        foreach ($result ['entries'] as $entry) {
            if (
                $entry ['.tag'] === $subset ['.tag']
                && $entry ['name'] === $subset ['name']
                && $entry ['path_display'] === $subset ['path_display']
                && $entry ['cleanPath'] === $subset ['cleanPath']
            ) {
                $foundSubset = true;
                break;
            }
        }

        if ($foundSubset === true) {
            return true;
        }
        return $subset;
    }

    protected function setUp()
    {
        $this->model = new DropboxModelDropbox();
        parent::setUp();
    }
}
