<?php

require_once __DIR__ . '/../lib/dropboxModelListRootFolderHelper.php';

// @codingStandardsIgnoreLine because of no namespace set
class ListDropboxFolderModelTest extends DropboxExtensionTestCase
{
    /**
     *
     * @var DropboxModelDropbox
     */
    protected $model;

    /**
     *
     * @var DropboxModelListRootFolderHelper
     */
    protected $listFolderHelper;

    public function testListRootFolder()
    {
        $this->listFolderHelper->listRootFolder(10001, '/', 'unit_tests');
    }

    public function testListDeepRootFolder()
    {
        $this->listFolderHelper->listRootFolder(10002, '/', 'unit_tests/files');
    }

    public function testListDeepRootFolderWithTrailingSlash()
    {
        $this->listFolderHelper->listRootFolder(10003, '/', 'unit_tests/files');
    }

    public function testListDeepRootFolderWithLeadingSlash()
    {
        $this->listFolderHelper->listRootFolder(10004, '/', 'unit_tests/files');
    }

    public function testListWithoutFolderGiven()
    {
        $this->listFolderHelper->listRootFolder(10001, '', 'unit_tests');
    }

    public function testListWithoutFolderGivenDeepRootFolder()
    {
        $this->listFolderHelper->listRootFolder(10002, '', 'unit_tests/files');
    }

    public function testListWithoutFolderGivenDeepRootFolderWithTrailingSlash()
    {
        $this->listFolderHelper->listRootFolder(10003, '', 'unit_tests/files');
    }

    public function testListWithoutFolderGivenDeepRootFolderWithLeadingSlash()
    {
        $this->listFolderHelper->listRootFolder(10004, '', 'unit_tests/files');
    }

    public function testListFolderWithSpacesInName()
    {
        $this->listFolderHelper->listRootFolder(
            10001,
            '/sub folder with spaces',
            'unit_tests/sub folder with spaces',
            '/sub folder with spaces'
        );
    }

    public function testListFolderWithSlashAtTheEnd()
    {
        $this->listFolderHelper->listRootFolder(
            10001,
            '/sub folder with spaces/',
            'unit_tests/sub folder with spaces',
            '/sub folder with spaces'
        );
    }

    public function testListDeepRootFolderThatLooksLikeURLEncoded()
    {
        $this->listFolderHelper->listRootFolder(
            10001,
            'folder%20test',
            'unit_tests/folder%20test',
            '/folder%20test'
        );
    }

    protected function setUp()
    {
        $this->model            = new DropboxModelDropbox();
        $this->listFolderHelper = new DropboxModelListFolderHelper($this->model);
        parent::setUp();
    }
}
