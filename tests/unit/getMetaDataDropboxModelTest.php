<?php

// @codingStandardsIgnoreLine because of no namespace set
class GetMetaDataDropboxModelTest extends DropboxExtensionTestCase
{
    /**
     *
     * @var DropboxModelDropbox
     */
    protected $model;

    protected $validFilesAndFolders = [
            "simple folder" => [
                    "root"           => 10001,
                    "path"           => "files",
                    'expectedResult' => [
                            '.tag'         => 'folder',
                            'name'         => 'files',
                            'path_display' => '/unit_tests/files',
                    ],
            ],
            "simple folder trailing /" => [
                    "root"           => 10001,
                    "path"           => "files/",
                    'expectedResult' => [
                            '.tag'         => 'folder',
                            'name'         => 'files',
                            'path_display' => '/unit_tests/files',
                    ],
            ],
            "simple folder double trailing /" => [
                    "root"           => 10001,
                    "path"           => "files//",
                    'expectedResult' => [
                            '.tag'         => 'folder',
                            'name'         => 'files',
                            'path_display' => '/unit_tests/files',
                    ],
            ],
            "simple folder deep root" => [
                    "root"           => 10002,
                    "path"           => "files",
                    'expectedResult' => [
                            '.tag'         => 'folder',
                            'name'         => 'files',
                            'path_display' => '/unit_tests/files/files',
                    ],
            ],
            "UTF folder" => [
                    "root"           => 10001,
                    "path"           => "special%20char%20%C3%A4%C3%B6%C3%BC%C3%9F%20and%20more%20%E0%A4%A8%E0%A5%87%E0%A4%AA%E0%A4%BE%E0%A4%B2",
                    'expectedResult' => [
                            '.tag'         => 'folder',
                            'name'         => 'special char äöüß and more नेपाल',
                            'path_display' => '/unit_tests/special char äöüß and more नेपाल',
                    ],
            ],
            "simple file" => [
                    "root"           => 10001,
                    "path"           => "Colorado.jpg",
                    'expectedResult' => [
                            '.tag'         => 'file',
                            'name'         => 'Colorado.jpg',
                            'path_display' => '/unit_tests/Colorado.jpg',
                    ],
            ],
            "simple file deep root" => [
                    "root"           => 10002,
                    "path"           => "simple_file.txt",
                    'expectedResult' => [
                            '.tag'         => 'file',
                            'name'         => 'simple_file.txt',
                            'path_display' => '/unit_tests/files/simple_file.txt',
                    ],
            ],
            "UTF file" => [
                    "root"           => 10001,
                    "path"           => "%E0%A4%9C%E0%A4%A8%E0%A4%A8%E0%A5%80%20%E0%A4%9C%E0%A4%A8%E0%A5%8D%E0%A4%AE%E0%A4%AD%E0%A5%82%E0%A4%AE%E0%A4%BF%E0%A4%B6%E0%A5%8D%E0%A4%9A%20%E0%A4%B8%E0%A5%8D%E0%A4%B5%E0%A4%B0%E0%A5%8D%E0%A4%97%E0%A4%BE%E0%A4%A6%E0%A4%AA%E0%A5%80%20%E0%A4%97%E0%A4%B0%E0%A5%80%E0%A4%AF%E0%A4%B8%E0%A5%80.jpg",
                    'expectedResult' => [
                            '.tag'         => 'file',
                            'name'         => 'जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                            'path_display' => '/unit_tests/जननी जन्मभूमिश्च स्वर्गादपी गरीयसी.jpg',
                    ],
            ],
    ];

    /**
     * @dataProvider dataProviderValidFilesAndFolders
     * @param int    $root           rootID
     * @param string $path
     * @param array  $expectedResult
     */
    public function testGetMetaData($root, $path, $expectedResult)
    {
        $this->model->setup($root);
        $result = $this->model->getMetaData($path);
        $this->assertMetaDataResultAsExpected($expectedResult, $result);
    }

    public function testGetMetaDataNotExisting()
    {
        $this->model->setup(10001);
        $result = $this->model->getMetaData("does not exist");
        $this->assertArrayHasKey("error", $result);
        $this->assertEquals("not_found", $result['error']);
    }

    public function dataProviderValidFilesAndFolders()
    {
        return $this->validFilesAndFolders;
    }

    public function assertMetaDataResultAsExpected($expected, $result)
    {
        $this->assertEquals(
            $expected ['.tag'],
            $result ['.tag'],
            ".tag does not match the expectation '" . $result ['.tag'] .
            "'!='" . $expected ['.tag'] . "'"
        );

        $this->assertEquals(
            $expected ['name'],
            $result ['name'],
            "name does not match the expectation '" . $result ['name'] .
            "'!='" . $expected ['name'] . "'"
        );

        $this->assertEquals(
            $expected ['path_display'],
            $result ['path_display'],
            "path_display does not match the expectation '" . $result ['path_display'] .
            "'!='" . $expected ['path_display'] . "'"
        );
    }

    protected function setUp()
    {
        $this->model = new DropboxModelDropbox();
        parent::setUp();
    }
}
