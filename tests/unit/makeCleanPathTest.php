<?php

// @codingStandardsIgnoreLine because of no namespace set
class MakeCleanPathTest extends \PHPUnit\Framework\TestCase
{
    protected $pathToTest = [
            "simple folder" => [
                    "root"     => "/unit_tests",
                    "path"     => "/unit_tests/test",
                    "type"     => "folder",
                    "expected" => "/test",
            ],
            "simple file" => [
                    "root"     => "/unit_tests",
                    "path"     => "/unit_tests/test",
                    "type"     => "file",
                    "expected" => "",
            ],
            "file leading //" => [
                    "root"     => "//unit_tests",
                    "path"     => "/unit_tests/test",
                    "type"     => "file",
                    "expected" => "",
            ],
            "folder trailing //" => [
                    "root"     => "unit_tests//",
                    "path"     => "/unit_tests/test",
                    "type"     => "folder",
                    "expected" => "/test",
            ],
            "folder path ending /" => [
                    "root"     => "/tests/",
                    "path"     => "/tests/test/",
                    "type"     => "folder",
                    "expected" => "/test",
            ],
            "file root double /" => [
                    "root"     => "/tests/",
                    "path"     => "/tests/test",
                    "type"     => "file",
                    "expected" => "",
            ],
            "folder root double /" => [
                    "root"     => "/tests/",
                    "path"     => "/tests/test",
                    "type"     => "folder",
                    "expected" => "/test",
            ],
            "file root trailing /" => [
                    "root"     => "tests/",
                    "path"     => "/tests/test",
                    "type"     => "file",
                    "expected" => "",
            ],
            "folder root trailing /" => [
                    "root"     => "tests/",
                    "path"     => "/tests/test",
                    "type"     => "folder",
                    "expected" => "/test",
            ],
            "file deep root" => [
                    "root"     => "/u/n/i/t_tests",
                    "path"     => "/u/n/i/t_tests/test",
                    "type"     => "file",
                    "expected" => "",
            ],
            "file deep root multiple //" => [
                    "root"     => "/u/n////i///t_tests",
                    "path"     => "/u/n/i/t_tests/test",
                    "type"     => "file",
                    "expected" => "",
            ],
            "file deep root&path" => [
                    "root"     => "/u/n/i/t_tests",
                    "path"     => "/u/n/i/t_tests/te/st/2",
                    "type"     => "file",
                    "expected" => "/te/st",
            ],
            "folder deep root" => [
                    "root"     => "/u/n/i/t_tests",
                    "path"     => "/u/n/i/t_tests/test",
                    "type"     => "folder",
                    "expected" => "/test",
            ],
            "folder deep root&path" => [
                    "root"     => "/u/n/i/t_tests",
                    "path"     => "/u/n/i/t_tests/te/st/2",
                    "type"     => "folder",
                    "expected" => "/te/st/2",
            ],
            "file root only '/'" => [
                    "root"     => "/",
                    "path"     => "/unit_tests/test",
                    "type"     => "file",
                    "expected" => "/unit_tests",
            ],
            "folder root only '/'" => [
                    "root"     => "/",
                    "path"     => "/unit_tests/test",
                    "type"     => "folder",
                    "expected" => "/unit_tests/test",
            ],
            "UTF folder" => [
                    "root"     => "/unit_tests",
                    "path"     => "/unit_tests/öäüß",
                    "type"     => "folder",
                    "expected" => "/öäüß",
            ],
            "UTF file" => [
                    "root"     => "/unit_tests",
                    "path"     => "/unit_tests/öäüß.txt",
                    "type"     => "file",
                    "expected" => "",
            ],
            "file UTF root" => [
                    "root"     => "/तयस्त",
                    "path"     => "/तयस्त/öäüß.txt",
                    "type"     => "file",
                    "expected" => "",
            ],
            "folder UTF root" => [
                    "root"     => "/तयस्त",
                    "path"     => "/तयस्त/öäüß",
                    "type"     => "folder",
                    "expected" => "/öäüß",
            ],
    ];

    protected $pathNotContainingRoot = [
            "simple folder" => [
                    "root" => "/tests",
                    "path" => "/someFolder/test",
                    "type" => "folder",
            ],
            "simple file" => [
                    "root" => "/tests",
                    "path" => "/someFolder/test.txt",
                    "type" => "file",
            ],
            "file root in filename" => [
                    "root" => "/tests",
                    "path" => "/someFolder/tests.txt",
                    "type" => "file",
            ],
            "file root in between" => [
                    "root" => "/tests",
                    "path" => "/someFolder/tests/file.txt",
                    "type" => "file",
            ],
            "folder root in between" => [
                    "root" => "/tests",
                    "path" => "/someFolder/tests/folder",
                    "type" => "folder",
            ],
    ];

    protected $invalidTypes = [
            "empty string" => [""],
            "string"       => ["assdf"],
            "null"         => [null],
            "false"        => [false],
            "false"        => [true],
            "int"          => [9404953],
            "zero"         => [0],
            "float"        => [2323.04],
            "array"        => [["file","folder"]],
    ];

    /**
     * @dataProvider dataProviderPathToTest
     * @param string $root
     * @param string $path
     * @param string $type     file|folder
     * @param string $expected
     */
    public function testValidPath($root, $path, $type, $expected)
    {
        $actual = DropboxHelper::makeCleanPath($root, $path, $type);
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider dataProviderInvalidTypes
     * @param string $type
     */
    public function testInvalidType($type)
    {
        $this->expectException("InvalidArgumentException");
        $this->expectExceptionMessage("type can only be 'file' or 'folder'");
        DropboxHelper::makeCleanPath("/test", "test", $type);
    }

    /**
     * @dataProvider dataProviderPathNotContainingRoot
     * @param string $root
     * @param string $path
     * @param string $type
     */
    public function testPathNotContainingRoot($root, $path, $type)
    {
        $this->expectException("Exception");
        $this->expectExceptionMessage("cannot find root in path");
        DropboxHelper::makeCleanPath($root, $path, $type);
    }

    public function testPathHasNoLeadingSlash()
    {
        $this->expectException("InvalidArgumentException");
        $this->expectExceptionMessage("invalid path (no leading /)");
        DropboxHelper::makeCleanPath("test", "test", "file");
    }

    public function testPathMultipleSlash()
    {
        $this->expectException("InvalidArgumentException");
        $this->expectExceptionMessage("invalid path (multiple /)");
        DropboxHelper::makeCleanPath("test", "/test//test", "file");
    }

    public function dataProviderPathNotContainingRoot()
    {
        return $this->pathNotContainingRoot;
    }

    public function dataProviderInvalidTypes()
    {
        return $this->invalidTypes;
    }

    public function dataProviderPathToTest()
    {
        return $this->pathToTest;
    }
}
