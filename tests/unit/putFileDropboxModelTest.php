<?php

use Kunnu\Dropbox\Exceptions\DropboxClientException;

// @codingStandardsIgnoreLine because of no namespace set
class PutFileDropboxModelTest extends DropboxExtensionTestCase
{
    /**
     *
     * @var DropboxModelDropbox
     */
    protected $model;

    protected $tempPath;

    protected $fileCreated = "";

    protected $folderCreated = "";

    /**
     * ids of roots defined in DropboxExtensionTestCase->getDataSet
     */
    protected $validRoots = [
            'simple root' => 10001,
            'deep root'   => 10002,
    ];

    protected $validSubfolders = [
            'no subfolder'                             => ["name" => "", "exists" => true],
            '/ as subfolder'                           => ["name" => "/", "exists" => true],
            'double / as subfolder'                    => ["name" => "//", "exists" => true],
            'space as subfolder'                       => ["name" => " ", "exists" => true],
            'space and / as subfolder'                 => ["name" => " /", "exists" => true],
            '/ and space as subfolder'                 => ["name" => "/ ", "exists" => true],
            'simple existing subfolder'                => ["name" => "files", "exists" => true],
            'simple existing subfolder trailing /'     => ["name" => "files/", "exists" => true],
            'simple existing subfolder leading /'      => ["name" => "/files", "exists" => true],
            'simple existing subfolder double /'       => ["name" => "/files/", "exists" => true],
            'UTF existing subfolder'                   => ["name" => "special char äöüß and more नेपाल", "exists" => true],
            'simple not existing subfolder'            => ["name" => "new-files", "exists" => false],
            'simple not existing subfolder trailing /' => ["name" => "new-files/", "exists" => false],
            'simple not existing subfolder leading /'  => ["name" => "/new-files", "exists" => false],
            'simple not existing subfolder double /'   => ["name" => "/new-files/", "exists" => false],
            'UTF not existing subfolder'               => ["name" => "new folder in नेपाल", "exists" => false],
    ];

    protected $validFileNames = [
            'simple filename' => "simple.txt",
            'UTF filename'    => "äöüß and more नेपाल.txt",
    ];

    protected $notStringsFileNames = [
            'null'             => [null],
            'false'            => [false],
            'true'             => [true],
            'int zero'         => [0],
            'int'              => [12230],
            'negative int'     => [-12230],
            'float'            => [12.230],
            'negative float'   => [-12.230],
            'array of strings' => [["one","two"]],
     ];

    /**
     * list of files and folders that exists in dropbox, so when we upload a file
     * with that name it should get renamed to "expectedName"
     */
    protected $existingFilesAndFolders = [
            ['existingName' => 'simple_file.txt', 'expectedName' => 'simple_file (1).txt'],
            ['existingName' => 'files', 'expectedName' => 'files (1)'],
    ];
    /**
     * @dataProvider validFilesToUpload
     */
    public function testUploadSimpleFile($fileName, $subfolderToCreateIn, $rootID)
    {
        $fileNameReturned = null;
        $this->model->setup($rootID);

        //check if the folder exists when it should
        //and that it's actually a folder,
        //but we don't need all these checks if the folder name is only slashes,
        //only spaces or a combination between slashes and spaces
        if (trim(trim($subfolderToCreateIn["name"]), "/") != "") {
            try {
                $subfolderDB = $this->model->getDropboxAPI()->getMetadata(
                    rawurldecode($this->model->getfolder()) .
                    trim($subfolderToCreateIn["name"], "/")
                );

                if ($subfolderDB->getDataProperty(".tag") !== 'folder') {
                    throw new Exception("folder to upload to exists and is a file");
                } elseif ($subfolderToCreateIn["exists"] === false) { //if the folder should not exist but does exist then we have a problem
                    throw new Exception(
                        "folder '" . $this->model->getfolder() .
                                        $subfolderToCreateIn ["name"] .
                        "' should not exist, but does"
                    );
                }
            } catch (DropboxClientException $e) {
                //only if the folder should exist throw an exception
                //if it should not exist, we are all good
                if ($subfolderToCreateIn["exists"] === true) {
                    throw $e;
                }
            }
        }

        if (
            !JFile::copy(
                __DIR__ . "/../data/randomDataFile10K",
                $this->tempPath . "/" . $fileName
            )
        ) {
            throw new Exception("could not copy data file to temp folder");
        }
        $this->model->changeFolder($subfolderToCreateIn["name"]);
        $this->fileCreated = $this->model->putFile(
            $this->tempPath . "/" . $fileName
        );
        $this->assertNotEmpty($this->fileCreated);

        if (
            $subfolderToCreateIn["exists"] === false
            && !empty($this->fileCreated)
        ) {
            //this folder will be created during upload and has to get deleted in tearDown()
            $this->folderCreated = \dirname($this->fileCreated);
        }

        //this is only for the expectation in the assertion
        if (
            substr($subfolderToCreateIn["name"], -1) !== "/"
            && trim(trim($subfolderToCreateIn["name"]), "/") != ""
        ) {
            $subfolderToCreateIn["name"] = $subfolderToCreateIn["name"] . "/";
        }

        //check the return values
        $this->assertEquals(
            $this->model->getChroot() .
            ltrim(trim($subfolderToCreateIn["name"]), "/") . $fileName,
            $this->fileCreated
        );

        //now lets see if the file actually exists on dropbox
        //and check its size
        $sizeReportedByDropbox = $this->model->getDropboxAPI()
            ->getMetadata($this->fileCreated)
            ->getDataProperty("size");
        $sizeReportedByOS = filesize($this->tempPath . "/" . $fileName);
        $this->assertEquals($sizeReportedByOS, $sizeReportedByDropbox);
    }

    public function testPutFileWithRelativePath()
    {
        $this->model->setup(10001);
        $fileName = __DIR__ . "/../data/randomDataFile10K";

        $this->fileCreated = $this->model->putFile($fileName);
        $this->assertNotEmpty($this->fileCreated);
        //check the return values

        $this->assertEquals(
            $this->model->getChroot() .
            basename($fileName),
            $this->fileCreated
        );

        //now lets see if the file actually exists on dropbox
        //and check its size
        $sizeReportedByDropbox = $this->model->getDropboxAPI()
            ->getMetadata($this->fileCreated)
            ->getDataProperty("size");
        $sizeReportedByOS = filesize($fileName);
        $this->assertEquals($sizeReportedByOS, $sizeReportedByDropbox);
    }

    public function testPutNotExistingFile()
    {
        $this->expectException("Exception");
        $this->expectExceptionMessage("file to upload does not exists");
        $file = "/nowhere/notExisting_" . $this->generateRandomString(4);
        $this->model->putFile($file);
    }

    public function testPutFolder()
    {
        $this->expectException("Exception");
        $this->expectExceptionMessage("file to upload does not exists");
        $file = realpath(__DIR__ . "/../data");
        $this->model->putFile($file);
    }

    public function testPutFileNameIsFolder()
    {
        $this->expectException("Exception");
        $this->expectExceptionMessage("file to upload does not exists");
        $this->model->putFile($this->tempPath);
    }

    public function testPutNotReadableFile()
    {
        $this->expectException("Exception");
        $this->expectExceptionMessage("file to upload is not readable");
        $file = "/etc/sudoers";
        $this->model->putFile($file);
    }

    /**
     * @dataProvider fileNamesThatAreNotStrings
     * @param unknown $fileName
     */
    public function testPutFileFileNameNotString($fileName)
    {
        $this->expectException("Exception");
        $this->expectExceptionMessage("could not get name of uploaded file");
        $this->model->putFile($fileName);
    }

    /**
     * @dataProvider filesAndFoldersThatAlreadyExists
     * @param  string    $existingName
     * @param  string    $expectedName
     * @throws Exception
     */
    public function testPutFileWithNameThatAlreadyExists($existingName, $expectedName)
    {
        $this->model->setup(10001);
        if (
            ! JFile::copy(
                __DIR__ . "/../data/randomDataFile10K",
                $this->tempPath . "/" . $existingName
            )
        ) {
            throw new Exception("could not copy data file to temp folder");
        }
        $this->fileCreated = $this->model->putFile(
            $this->tempPath . "/" . $existingName
        );
        $this->assertNotEmpty($this->fileCreated);
        $this->assertEquals($this->model->getChroot() . $expectedName, $this->fileCreated);
    }

    /**
     * dataprovider that returns $this->existingFilesAndFolders
     */
    public function filesAndFoldersThatAlreadyExists()
    {
        return $this->existingFilesAndFolders;
    }

    /**
     * dataprovider that returns $this->notStringsFileNames
     */
    public function fileNamesThatAreNotStrings()
    {
        return $this->notStringsFileNames;
    }

    /**
     * dataprovider for uploading files
     * this basically creates a table of tests by
     * combining $validRoots, $validSubfolders & $validFileNames
     */
    public function validFilesToUpload()
    {
        $testsToRun = [];
        $prefix     = "putFileTest_" . $this->generateRandomString(4) . "_";

        foreach ($this->validFileNames as $fileDesc => $fileName) {
            foreach ($this->validSubfolders as $subfolderDesc => $subfolder) {
                foreach ($this->validRoots as $rootDesc => $rootId) {
                    $testsToRun[$fileDesc . "-" . $subfolderDesc . "-" . $rootDesc] =
                    [$prefix . $fileName, $subfolder, $rootId];
                }
            }
        }
        return $testsToRun;
    }

    protected function setUp()
    {
        $this->model    = new DropboxModelDropbox();
        $config         = JFactory::getConfig();
        $this->tempPath = $config->get('tmp_path') . DS . "DropboxTestTemp";
        if (!JFolder::create($this->tempPath)) {
            throw new Exception("could not create" . $this->tempPath);
        }
        parent::setUp();
    }

    protected function tearDown()
    {
        if (!empty($this->fileCreated)) {
            $deleteReturn = $this->model->getDropboxAPI()->delete($this->fileCreated);
        }
        if (!empty($this->folderCreated)) {
            $deleteReturn = $this->model->getDropboxAPI()->delete($this->folderCreated);
        }
        $this->fileCreated   = "";
        $this->folderCreated = "";
        if (!JFolder::delete($this->tempPath)) {
            throw new Exception("could not delete" . $this->tempPath);
        }
        parent::tearDown();
    }
}
