<?php

// @codingStandardsIgnoreLine because of no namespace set
class CreateFolderDropboxModelTest extends DropboxExtensionTestCase
{
    /**
     *
     * @var DropboxModelDropbox
     */
    protected $model;

    protected $folderCreated = "";

    protected $validFolderRawlings = [
            'simple folder'      => ['simple','simple'],
            'multi_layer folder' => ['multi/layer/sub','multi/layer/sub'],
            'UTF folder'         => ['äöüß and more नेपाल','äöüß-and-more-नेपाल'],
            'html'               => ['<b>funny html</b><script>alert("\'h")</script>','funny-htmlalert(--h-)'],
    ];

    /**
     * ids of roots defined in DropboxExtensionTestCase->getDataSet
     */
    protected $validRoots = [
            'simple root' => 10001,
            'deep root'   => 10002,
    ];

    protected $validSubfolders = [
            'no subfolder'     => "",
            'simple subfolder' => "files",
            'UTF subfolder'    => "special char äöüß and more नेपाल/",
    ];

    /**
     * this should result in an Exception
     *
     * @var array
     */
    protected $inValidFolders = [
            'double slash'     => ['//'],
            'slash with space' => ['/  /'],
            'empty'            => [''],
            'space'            => [' '],
    ];

    /**
     * @dataProvider validFoldersToCreate
     */
    public function testCreateFolderSuccessfully($folderToCreate, $expectedFolderName, $subfolderToCreateIn, $rootId)
    {
        $this->model->setup($rootId);

        try {
            $this->model->changeFolder($subfolderToCreateIn);
            $return              = $this->model->createFolder($folderToCreate);
            $this->folderCreated = $return['path_display'];
        } catch (Exception $e) {
            throw new Exception(
                "could not create " . $folderToCreate . " in " .
                $this->model->getChroot() . $subfolderToCreateIn .
                " " . $e->getMessage()
            );
        }

        $this->assertNotEmpty($this->folderCreated);

        //this is only for the expectation in the assertion
        if (substr($subfolderToCreateIn, -1) !== "/" && $subfolderToCreateIn !== "") {
            $subfolderToCreateIn = $subfolderToCreateIn . "/";
        }
        $this->assertEquals(
            $this->model->getChroot() .
            $subfolderToCreateIn .
            trim($expectedFolderName, "/"),
            $this->folderCreated
        );
    }

    /**
     * @dataProvider inValidFoldersToCreate
     */
    public function testCreateFolderMalformedPath($folderName)
    {
        $this->expectException("Exception");
        $this->model->setup("10001");
        $this->model->createFolder($folderName);
    }

    public function testCreateFolderConflictPath()
    {
        $this->expectException("Exception");
        $this->expectExceptionMessage("conflict");
        $this->model->setup("10001");
        $folderName          = "createFolderTest_" . $this->generateRandomString(4);
        $return              = $this->model->createFolder($folderName);
        $this->folderCreated = $return['path_display'];
        $this->assertNotEmpty($this->folderCreated);
        $this->assertEquals($this->model->getChroot() . $folderName, $this->folderCreated);

        //create the folder again
        $this->model->createFolder($folderName);
    }

    public function inValidFoldersToCreate()
    {
        return $this->inValidFolders;
    }

    public function validFoldersToCreate()
    {
        //add slashes at the beginning/end to the rawlings
        $foldersToTest = [];
        $testsToRun    = [];
        $prefix        = "createFolderTest_" . $this->generateRandomString(4) . "_";
        foreach ($this->validFolderRawlings as $name => $folder) {
            $folder[0]                                = $prefix . $folder[0];
            $folder[1]                                = $prefix . $folder[1];
            $foldersToTest[$name]                     = [$folder[0],$folder[1]];
            $foldersToTest[$name . " leading slash"]  = ["/" . $folder[0],"/" . $folder[1]];
            $foldersToTest[$name . " trailing slash"] = [$folder[0] . "/", $folder[1] . "/"];
            if ($folder[1] === "") {
                $foldersToTest[$name . " double slash"] = ["/" . $folder[0] . "/", "/"];
            } else {
                $foldersToTest[$name . " double slash"] = ["/" . $folder[0] . "/", "/" . $folder[1] . "/"];
            }
        }

        foreach ($foldersToTest as $folderDesc => $folderName) {
            foreach ($this->validSubfolders as $subfolderDesc => $subfolderName) {
                foreach ($this->validRoots as $rootDesc => $rootId) {
                    $testsToRun[$folderDesc . "-" . $subfolderDesc . "-" . $rootDesc] = [
                            $folderName[0],$folderName[1], $subfolderName, $rootId,
                    ];
                }
            }
        }
        return $testsToRun;
    }

    protected function setUp()
    {
        $this->model = new DropboxModelDropbox();
        parent::setUp();
    }

    protected function tearDown()
    {
        if (!empty($this->folderCreated)) {
            $deleteReturn = $this->model->getDropboxAPI()->delete($this->folderCreated);
        }

        $this->folderCreated = "";
        parent::tearDown();
    }
}
