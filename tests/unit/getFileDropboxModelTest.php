<?php

// @codingStandardsIgnoreLine because of no namespace set
class GetFileDropboxModelTest extends DropboxExtensionTestCase
{
    /**
     *
     * @var DropboxModelDropbox
     */
    protected $model;

    protected $cachePath;

    /**
     *
     * @var JTable
     */
    protected $fileLockTable;

    protected $fileContentSimpleFile;
    /**
     * @dataProvider additionalRootsAndSubfolders
     */
    public function testDownloadSimpleFile($id, $folder)
    {
        $this->model->setup($id);
        $fileMetaData = $this->model->getFile($folder . "simple_file.txt");
        if (isset($fileMetaData['error'])) {
            $this->assertArrayNotHasKey("error", $fileMetaData, $fileMetaData['error']);
        }

        $fileContent = file_get_contents(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
        $this->assertEquals($this->fileContentSimpleFile, $fileContent);
    }

    /**
     * @dataProvider additionalRootsAndSubfolders
     */
    public function testDownloadUTFFile($id, $folder)
    {
        $this->model->setup($id);
        $fileMetaData = $this->model->getFile($folder . "special char äöüß &%.txt");
        $fileContent  = file_get_contents(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
        $fileContentExpected = <<<EOT
This is a simple test file that contains NON-ASCII characters in the name
we need some more lines here
and some more

EOT;
        $this->assertEquals($fileContentExpected, $fileContent);
    }

    /**
     * @dataProvider additionalRootsAndSubfolders
     */
    public function testDownloadNotExisting($id, $folder)
    {
        $this->model->setup($id);
        $fileMetaData = $this->model->getFile($folder . "not_existing.xyz");
        $this->assertArrayHasKey("error", $fileMetaData);
        $this->assertEquals("not_found", $fileMetaData['error']);
    }

    /**
     * @dataProvider additionalRootsAndSubfolders
     */
    public function testDownloadCachedFile($id, $folder)
    {
        $now = time();
        $this->model->setup($id);
        $fileMetaData = $this->model->getFile($folder . "simple_file.txt");
        $mtime        = filemtime(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
        $this->assertNotFalse($mtime);
        $this->assertGreaterThan($now, $mtime);
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );

        //make sure locks are deleted
        $fileLockId = $this->model->getFileLockID();
        $this->fileLockTable->delete($fileLockId);

        //sleep to make sure we are not downloading the file again to early
        sleep(1);

        $fileMetaData = $this->model->getFile($folder . "simple_file.txt");
        $mtimeNew     = filemtime(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
        $this->assertEquals($mtime, $mtimeNew);
    }

    public function testDownloadWriteLockEnabledFile()
    {
        $this->model->setup(10001);
        $fileMetaData = $this->model->getFile("simple_file.txt");
        $lock         = $this->fileLockTable->getLockByHash($fileMetaData['id']);
        $this->assertEquals("write", $lock);

        $fileMetaData = $this->model->getFile("simple_file.txt");
        $fileContent  = file_get_contents(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );

        $this->assertEquals($this->fileContentSimpleFile, $fileContent);
    }

    public function testDownloadReadLockEnabledFile()
    {
        $this->expectException("Exception");
        $this->expectExceptionMessage("COM_DROPBOX_FILE_LOCK_TIMEOUT");
        $this->model->setup(10001);
        $fileMetaData = $this->model->getFile("simple_file.txt");
        $this->fileLockTable->save(
            [
                    "file_name_hash" => $fileMetaData ['id'],
                    "lock_type"      => "r",
                    "time"           => time(),
            ]
        );
        $lock = $this->fileLockTable->getLockByHash($fileMetaData['id']);
        $this->assertEquals("read", $lock);
        try {
            $fileMetaData = $this->model->getFile("simple_file.txt");
        } catch (Exception $e) {
            $this->fileLockTable->delete();
            throw $e;
        }
    }

    public function testDownloadWriteLockEnabledDataFileMissing()
    {
        $this->model->setup(10001);
        $fileMetaData = $this->model->getFile("simple_file.txt");
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
        unlink($this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']);
        $this->assertFileNotExists(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );

        $lock = $this->fileLockTable->getLockByHash($fileMetaData['id']);
        $this->assertEquals("write", $lock);

        $fileMetaData = $this->model->getFile("simple_file.txt");
        $fileContent  = file_get_contents(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );

        $this->assertEquals($this->fileContentSimpleFile, $fileContent);
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
    }

    public function testDownloadWriteLockEnabledHeaderFileMissing()
    {
        $this->model->setup(10001);
        $fileMetaData = $this->model->getFile("simple_file.txt");
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
        unlink($this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']);
        $this->assertFileNotExists(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );

        $lock = $this->fileLockTable->getLockByHash($fileMetaData['id']);
        $this->assertEquals("write", $lock);

        $fileMetaData = $this->model->getFile("simple_file.txt");
        $fileContent  = file_get_contents(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );

        $this->assertEquals($this->fileContentSimpleFile, $fileContent);
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
    }

    public function testReDownloadMissingHeaderFile()
    {
        $this->model->setup(10001);
        $fileMetaData = $this->model->getFile("simple_file.txt");
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
        unlink($this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']);
        $this->assertFileNotExists(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );

        //delete locks
        $this->fileLockTable->delete($this->model->getFileLockID());

        $fileMetaData = $this->model->getFile("simple_file.txt");
        $fileContent  = file_get_contents(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );

        $this->assertEquals($this->fileContentSimpleFile, $fileContent);
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
    }

    public function testReDownloadMissingDataFile()
    {
        $this->model->setup(10001);
        $fileMetaData = $this->model->getFile("simple_file.txt");
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
        unlink($this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']);
        $this->assertFileNotExists(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );

        //delete locks
        $this->fileLockTable->delete($this->model->getFileLockID());

        $fileMetaData = $this->model->getFile("simple_file.txt");
        $fileContent  = file_get_contents(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );

        $this->assertEquals($this->fileContentSimpleFile, $fileContent);
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCacheHeader-" . $fileMetaData['id']
        );
        $this->assertFileIsReadable(
            $this->cachePath . DS . "DropboxCache-" . $fileMetaData['id']
        );
    }

    public function testDownloadNotExistingFile()
    {
        throw new Exception("not implemented");
    }

    public function testDownloadBadFileName()
    {
        throw new Exception("not implemented");
    }

    public function additionalRootsAndSubfolders()
    {
        return [
                'simple root'                  => [10001, ""],
                'simple root simple subfolder' => [10001, "files/"],
                'simple root UTF subfolder'    => [10001, "special char äöüß and more नेपाल/"],
                'simple root extra slash'      => [10001, "/"],
                'deep root'                    => [10002, ""],
                'deep root simple subfolder'   => [10002, "files/"],
                'deep root UTF subfolder'      => [10002, "special char äöüß and more नेपाल/"],
                'deep root extra slash'        => [10002, "/"],
        ];
    }

    protected function setUp()
    {
        $this->model     = new DropboxModelDropbox();
        $config          = JFactory::getConfig();
        $this->cachePath = $config->get('tmp_path') . DS . "DropboxCache";
        $this->rrmdir($this->cachePath);
        if (file_exists($this->cachePath)) {
            throw new Exception("could not delete cache folder, maybe need to run tests with 'sudo'?");
        }
        $this->fileLockTable         = JTable::getInstance('FileLocks', 'DropboxTable');
        $this->fileContentSimpleFile = <<<EOT
This is a simple test file
we need some more lines here
and some more

EOT;
        parent::setUp();
    }

    protected function tearDown()
    {
        try {
            $fileLockId = $this->model->getFileLockID();
            $this->fileLockTable->delete($fileLockId);
        } catch (Exception $e) {
        }
        $this->rrmdir($this->cachePath);
        parent::tearDown();
    }

    protected function rrmdir($dir)
    {
        if (is_dir($dir)) {
            $objects = scandir($dir);
            foreach ($objects as $object) {
                if ($object != "." && $object != "..") {
                    if (is_dir($dir . "/" . $object)) {
                        $this->rrmdir($dir . "/" . $object);
                    } else {
                        if (!unlink($dir . "/" . $object)) {
                            throw new Exception("could not delete " . $dir . "/" . $object);
                        }
                    }
                }
            }
            rmdir($dir);
        }
    }
}
