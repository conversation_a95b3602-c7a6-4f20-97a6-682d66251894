<?php

// @codingStandardsIgnoreLine because of no namespace set
class DropboxHelperTest extends \PHPUnit\Framework\TestCase
{
    protected $folderRawlings = [
            'simple'                           => ['simple','simple'],
            'space'                            => ['sim ple','sim-ple'],
            'spaces'                           => ['sim  ple','sim-ple'],
            'tab'                              => ['sim	ple','sim-ple'],
            'tab and space'                    => ['sim	 ple','sim-ple'],
            'too many dots'                    => ['sim..ple','sim-ple'],
            'dot at the end'                   => ['simple.','simple.'],
            'dot in the middle'                => ['simple.folder','simple.folder'],
            'dot at the beginning'             => ['.simple','.simple'],
            'dot only'                         => ['.','-'],
            'double dot only '                 => ['..','-'],
            'triple dot only'                  => ['...','-'],
            'empty'                            => ['',''],
            'multi layer'                      => ['multi/layer/sub','multi/layer/sub'],
            'multi layer dots only in between' => ['multi/layer/../sub','multi/layer/-/sub'],
            'UTF'                              => ['äöüß and more नेपाल' ,'äöüß-and-more-नेपाल'],
            'UTF multilayer'                   => ['äöüß/more नेपाल' ,'äöüß/more-नेपाल'],
            'forbidden characters only'        => ['[:#*"@+=;!&%<>]\|[]' , "-----------------"],
            'forbidden characters'             => ['good[:#*"@+=;!&%<>]\|[]fine' , "good-----------------fine"],
            'more forbidden characters'        => ["good'?fine" , "good-fine"],
            'int'                              => [1234 , "1234"],
            'float'                            => [12.34 , "12.34"],
    ];

    protected $fileNamesToTest = [
            'simple'                    => ['simple','simple'],
            'space'                     => ['sim ple','sim-ple'],
            'spaces'                    => ['sim  ple','sim-ple'],
            'tab'                       => ['sim	ple','sim-ple'],
            'tab and space'             => ['sim	 ple','sim-ple'],
            'too many dots'             => ['sim..ple','sim-ple'],
            'dot at the end'            => ['simple.','simple.'],
            'dot in the middle'         => ['simple.folder','simple.folder'],
            'dot at the beginning'      => ['.simple','.simple'],
            'dot only'                  => ['.','-'],
            'double dot only '          => ['..','-'],
            'triple dot only'           => ['...','-'],
            'leading slash'             => ['/simple','-simple'],
            'trailing slash'            => ['simple/','simple-'],
            'double slash'              => ['/simple/','-simple-'],
            'empty'                     => ['',''],
            'slashes in between'        => ['multi/layer/sub','multi-layer-sub'],
            'dots only between slashes' => ['multi/layer/../sub','multi-layer---sub'],
            'UTF'                       => ['äöüß and more नेपाल' ,'äöüß-and-more-नेपाल'],
            'UTF slashes in between'    => ['äöüß/more नेपाल' ,'äöüß-more-नेपाल'],
            'forbidden characters only' => ['[:#*"@+=;!&%<>]\|[]' , "-----------------"],
            'forbidden characters'      => ['good[:#*"@+=;!&%<>]\|[]fine' , "good-----------------fine"],
            'more forbidden characters' => ["good'?fine" , "good-fine"],
            'int'                       => [1234 , "1234"],
            'float'                     => [12.34 , "12.34"],
    ];

    protected $searchQueriesToTest = [
            'simple'                    => ['simple','simple'],
            'space'                     => ['sim ple','sim ple'],
            'spaces'                    => ['sim  ple','sim ple'],
            'tab'                       => ['sim	ple','sim ple'],
            'tab and space'             => ['sim	 ple','sim ple'],
            'too many dots'             => ['sim..ple','sim ple'],
            'dot at the end'            => ['simple.','simple.'],
            'dot in the middle'         => ['simple.folder','simple.folder'],
            'dot at the beginning'      => ['.simple','.simple'],
            'dot only'                  => ['.',''],
            'double dot only '          => ['..',''],
            'triple dot only'           => ['...',''],
            'leading slash'             => ['/simple','-simple'],
            'trailing slash'            => ['simple/','simple-'],
            'double slash'              => ['/simple/','-simple-'],
            'empty'                     => ['',''],
            'slashes in between'        => ['multi/layer/sub','multi-layer-sub'],
            'dots only between slashes' => ['multi/layer/../sub','multi-layer- -sub'],
            'UTF'                       => ['äöüß and more नेपाल' ,'äöüß and more नेपाल'],
            'UTF slashes in between'    => ['äöüß/more नेपाल' ,'äöüß-more नेपाल'],
            'forbidden characters only' => ['[:#*"@+=;!&%<>]\|[]' , "-----------------"],
            'forbidden characters'      => ['good[:#*"@+=;!&%<>]\|[]fine' , "good-----------------fine"],
            'more forbidden characters' => ["good'?fine" , "good-fine"],
            'int'                       => [1234 , "1234"],
            'float'                     => [12.34 , "12.34"],
    ];

    /**
     * @dataProvider folderNames
     */
    public function testMakeFoldernameSafe($folderName, $expectedResult)
    {
        $safeFolderName = DropboxHelper::makeFoldernameSafe($folderName);
        $this->assertEquals(
            $expectedResult,
            $safeFolderName
        );
    }

    /**
     * @dataProvider fileNames
     */
    public function testMakeFilenameSafe($fileName, $expectedResult)
    {
        $safeFolderName = DropboxHelper::makeFilenameSafe($fileName);
        $this->assertEquals(
            $expectedResult,
            $safeFolderName
        );
    }

    /**
     * @dataProvider dataProviderSearchQueries
     * @param string $query
     */
    public function testMakeSearchSafe($query, $expectedResult)
    {
        $safeQuery = DropboxHelper::makeSearchSafe($query);
        $this->assertEquals(
            $expectedResult,
            $safeQuery
        );
    }

    public function folderNames()
    {
        //add slashes at the beginning/end to the rawlings
        $foldersToTest = [];
        foreach ($this->folderRawlings as $name => $folder) {
            $foldersToTest[$name]                     = [$folder[0],$folder[1]];
            $foldersToTest[$name . " leading slash"]  = ["/" . $folder[0],"/" . $folder[1]];
            $foldersToTest[$name . " trailing slash"] = [$folder[0] . "/", $folder[1] . "/"];
            if ($folder[1] === "") {
                $foldersToTest[$name . " double slash"] = ["/" . $folder[0] . "/", "/"];
            } else {
                $foldersToTest[$name . " double slash"] = ["/" . $folder[0] . "/", "/" . $folder[1] . "/"];
            }
        }
        return $foldersToTest;
    }

    public function fileNames()
    {
        return $this->fileNamesToTest;
    }

    public function dataProviderSearchQueries()
    {
        return $this->searchQueriesToTest;
    }
}
