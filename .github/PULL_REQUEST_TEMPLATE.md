| Status  |                Type                | Env Vars Change |           Ticket           |
| :---: |:----------------------------------:| :---: |:--------------------------:|
| Ready/Hold | Feature/Bug/Docs/Refactor/CI/Tests | Yes/No | [Link](<ticket link here>) |

> ⚠️ NOTE: Use notes like this to highlight important information about the PR. This could include dependencies on other PRs, newly added or removed environment variables, reasons for the PR being on hold, or any other critical details that need attention.

## Problem

_What problem are you trying to solve?_
* **What is the current behavior?** (You can also link to any related issue(s) here)

## Solution

_How did you solve the problem?_
* **What is the new behavior (if this is a feature change)?**

## Screenshots (if appropriate):

**BEFORE**:
[Screenshot here]

**AFTER**:
[Screenshot here]


## Other changes (e.g. bug fixes, UI tweaks, small refactors)


## Deploy notes

_Notes regarding deployment of the contained body of work. These should note any
new dependencies, new scripts, etc._

**New environment variables**:

- `env var`: env var details

**New scripts**:

- `script`: script details

**New dependencies**:

- `dependency`: dependency details

**New dev dependencies**:

- `dependency`: dependency details

## Testing instructions
<!-- Instructions on how to test the changes made in the pull 
request, helping reviewers validate the code. -->

<details>
<summary><strong>Test Configuration</strong></summary>

```
Joomla Version: 4.x.x
Browser: Firefox and Chrome
PHP Version: 8.x
```
</details>

## Special notes for your reviewer
<!-- Use this section to highlight any specific instructions or considerations for the reviewer. -->

## Checklist
<!---
This checklist is a helpful reminder for small, easily overlooked tasks.
Mark all applicable items with an `x`, add notes for any incomplete items, 
and remove any items that are irrelevant to this PR.
-->
- [ ] UI tests (Add/Update/Remove)
- [ ] Documentation (Add/Update/Remove)
