name: Release & Deploy Joomla Extension

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  build-and-release:
    name: Build, create release & attach binaries
    runs-on: ubuntu-latest
    steps:
      - name: 📄 Checkout
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1

      - name: Create artifacts
        run: VERSION=${{ github.ref_name }} SKIP_RELEASE=true make build

      - name: Create / upload GitHub release
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          gh release create "${{ github.ref_name }}" \
            --title "${{ github.ref_name }}" \
            --generate-notes \
            --verify-tag \
            build/assets/com_dropbox_${{ github.ref_name }}.zip

  deploy:
    name: Deploy to server
    runs-on: ubuntu-latest
    needs: build-and-release
    steps:
      - name: 📄 Checkout
        uses: actions/checkout@v4

      - name: 🧰 Install system deps
        run: |
          sudo apt-get update
          sudo apt-get install -y sshpass

      - name: 📥 Download release asset
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          gh release download "${{ github.ref_name }}" \
            --pattern "*.zip" \
            --dir .

      - name: 📤 Upload & install on server
        run: |
          TARGET_ZIP="com_dropbox_${{ github.ref_name }}.zip"
          sshpass -p "${{ secrets.JOOMLA_SSH_PASSWORD }}" scp \
            -o StrictHostKeyChecking=no -P "${{ secrets.JOOMLA_SSH_PORT }}" \
            "$TARGET_ZIP" \
            "${{ secrets.JOOMLA_SSH_USER }}@${{ secrets.JOOMLA_SSH_SERVER }}:/tmp/$TARGET_ZIP"

          sshpass -p "${{ secrets.JOOMLA_SSH_PASSWORD }}" ssh \
            -p "${{ secrets.JOOMLA_SSH_PORT }}" \
            "${{ secrets.JOOMLA_SSH_USER }}@${{ secrets.JOOMLA_SSH_SERVER }}" <<EOF
              cd "${{ secrets.JOOMLA_SSH_FOLDER }}"
              php cli/joomla.php extension:install --path "/tmp/$TARGET_ZIP"
              rm -f "/tmp/$TARGET_ZIP"
          EOF
