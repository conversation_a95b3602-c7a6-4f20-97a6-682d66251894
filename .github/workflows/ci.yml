name: CI

on:
  push:
    branches: [ master ]
  pull_request:

jobs:
  e2e:
    runs-on: ubuntu-latest
    env:
      CI: true
      DROPBOX_CLIENT_ID: ${{ secrets.DROPBOX_CLIENT_ID }}
      DROPBOX_CLIENT_SECRET: ${{ secrets.DROPBOX_CLIENT_SECRET }}
    strategy:
      matrix:
        joomla: [4.4-php8.1, 4.4-php8.2, 5.1-php8.2 ]
        include:
          - joomla: 4.4-php8.1
            joomla_version: 4
            php_version: 8.1
            docker_script: make docker-run-with-v4-php8.1-ci
            DROPBOX_EMAIL: DROPBOX_EMAIL_1
            DROPBOX_PASSWORD: DROPBOX_PASSWORD_1
            DROPBOX_REFRESH_TOKEN: DROPBOX_REFRESH_TOKEN_1
          - joomla: 4.4-php8.2
            joomla_version: 4
            php_version: 8.2
            docker_script: make docker-run-with-v4-php8.2-ci
            DROPBOX_EMAIL: DROPBOX_EMAIL_2
            DROPBOX_PASSWORD: DROPBOX_PASSWORD_2
            DROPBOX_REFRESH_TOKEN: DROPBOX_REFRESH_TOKEN_2
          - joomla: 5.1-php8.2
            joomla_version: 5
            php_version: 8.2
            docker_script: make docker-run-with-v5-php8.2-ci
            DROPBOX_EMAIL: DROPBOX_EMAIL_3
            DROPBOX_PASSWORD: DROPBOX_PASSWORD_3
            DROPBOX_REFRESH_TOKEN: DROPBOX_REFRESH_TOKEN_3
    steps:
      - name: Cancel previous runs
        uses: styfle/cancel-workflow-action@0.12.1
        with:
          all_but_latest: true
          access_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Checkout
        uses: actions/checkout@v4
      - name: Source environment
        working-directory: ./tests/e2e
        run: |
          cat .env.ci >> $GITHUB_ENV
          echo "JOOMLA_VERSION=${{ matrix.joomla_version }}" >> $GITHUB_ENV
          echo "DROPBOX_EMAIL=${{ secrets[matrix.DROPBOX_EMAIL] }}" >> $GITHUB_ENV
          echo "DROPBOX_PASSWORD=${{ secrets[matrix.DROPBOX_PASSWORD] }}" >> $GITHUB_ENV
          echo "DROPBOX_REFRESH_TOKEN=${{ secrets[matrix.DROPBOX_REFRESH_TOKEN] }}" >> $GITHUB_ENV
          printenv
      - name: Start Joomla
        run: |
          cp docker-compose.ci.override.yml docker-compose.override.yml
          ${{ matrix.docker_script }}
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php_version }}
      - name: Setup NodeJS
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: ./tests/e2e/package-lock.json
      - name: Install PHP dependencies
        run: make install
      - name: Install NodeJS dependencies
        working-directory: ./tests/e2e
        run: npm ci
      - name: Wait for services
        run: |
          sudo apt-get install wait-for-it -y
          wait-for-it -h localhost -p 3306 -t 10 -- echo "MySQL is up and running."
          wait-for-it -h localhost -p 8080 -t 10 -- echo "Joomla is up and running."
      - name: PHP CS Fixer
        run: make cs-checker
      - name: JS code style
        working-directory: ./tests/e2e
        run: npm run lint
      - name: Setup Joomla & Install Dropbox Extension
        id: setup-joomla
        run: |
          docker exec dropboxextensionforjoomla-joomla-1 php installation/joomla.php install -n -vvv --site-name="Joomla Test Site" --admin-user="Test Admin" --admin-username="admin" --admin-password="admin123456789" --admin-email="<EMAIL>" --db-type=mysqli --db-host="mysql" --db-encryption=0 --db-user="joomla" --db-name="joomla" --db-pass="joomla" --db-prefix="jdb_" --no-interaction
          VERSION=CI SKIP_RELEASE=true make build
          docker cp build/assets/com_dropbox_CI.zip dropboxextensionforjoomla-joomla-1:/var/www/html/
          docker exec dropboxextensionforjoomla-joomla-1 php cli/joomla.php extension:install -v --path com_dropbox_CI.zip
        continue-on-error: true
      - name: Upload Joomla setup logs
        if: steps.setup-joomla.outcome == 'failure'
        uses: actions/upload-artifact@v4
        with:
          name: setup-logs
          path: tests/e2e/reports/setup
      - name: Exit for Joomla setup failure
        if: steps.setup-joomla.outcome == 'failure'
        run: echo "The setup for joomla script failed. Exiting..." && exit 1
      - name: E2E Tests
        id: e2e-tests
        working-directory: ./tests/e2e
        run: npm run test:e2e features --retries=1
        continue-on-error: true
        timeout-minutes: 30
      - name: Upload E2E logs
        if: steps.e2e-tests.outcome == 'failure'
        uses: actions/upload-artifact@v4
        with:
          name: e2e-logs
          path: tests/e2e/reports/tests
      - name: Exit for E2E failure
        if: steps.e2e-tests.outcome == 'failure'
        run: echo "The E2E tests failed. Exiting..." && exit 1
