<?xml version="1.0" encoding="utf-8"?>
<extension version="3.0" type="plugin" group="content">
<name>Dropbox Plugin</name>
<creationDate>2023-05-26</creationDate>
	<author><PERSON><PERSON> Neumann / Individual IT Services</author>
	<authorEmail><EMAIL></authorEmail>
	<authorUrl>http://www.individual-it.net</authorUrl>
	<copyright>Copyright Individual-IT-Services</copyright>
	<license>GNU/GPL, see LICENSE.php</license>
	<version>5.0.0</version>
	<description>Plugin for the Dropbox Component. Please download and install the Dropbox Component first: http://www.individual-it.net/Install-Instructions-for-Dropbox-Component.html . Usage: {dropbox}joomlas dropboxID{/dropbox} You find the ID under Components->Dropbox. Don't forget to enable the Plugin (Extension -> Plugin-Manager)'</description>

   	<updateservers>
    	<server type="extension" priority="1" name="Dropbox Plugin for Joomla 4 Updates">http://www.individual-it.net/joomla_components_updates/dropbox_plugin_for_joomla.xml</server>
 	</updateservers>

<files>
   <filename plugin="dropboxplugin">dropboxplugin.php</filename>
   <filename>LICENSE.php</filename>
   <filename>index.html</filename>

</files>
</extension>
