<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Plugins
 * @link http://www.individual-it.net
 * @license GNU/GPL
 */

// No direct access allowed to this file
\defined('_JEXEC') or die('Restricted access');

// Import Joomla! Plugin library file
jimport('joomla.plugin.plugin');
// @codingStandardsIgnoreLine because of no namespace set and not PascalCase
class plgContentDropboxplugin extends JPlugin
{
    public function plgContentDropboxplugin(&$subject, $params)
    {
        jimport('joomla.html.parameter');
        if (! JPlugin::loadLanguage('com_dropbox', JPATH_SITE . "/components/com_dropbox/")) {
            echo 'Problems loading language from path: ' . JPATH_SITE . "/components/com_dropbox/";
        }

        parent::__construct($subject, $params);
        $this->_plugin = JPluginHelper::getPlugin('content', 'dropboxplugin');
    }
    public function onContentPrepare($context, &$row, &$params, $page = 0)
    {
        // expression to search for
        $regex = "#{dropbox}(\d+){/dropbox}#s";

        // check whether plugin has been unpublished
        if (! $this->params->get('enabled', 1)) {
            $row->text = preg_replace($regex, '', $row->text);

            return true;
        }

        // find all instances of plugin and put in $matches
        preg_match_all($regex, $row->text, $matches);

        // Number of plugins
        $count = \count($matches [0]);

        // plugin only processes if there are any instances of the plugin in the text
        if ($count) {
            $this->process($row, $matches, $count, $regex);
        }
        // No return value
    }

    // The proccessing function
    protected function process(&$row, &$matches, $count, $regex)
    {
        jimport('joomla.html.parameter');
        $app                     = JFactory::getApplication('site');
        $mosConfig_absolute_path = JPATH_SITE;
        $mosConfig_live_site     = JURI::base();
        $document                = & JFactory::getDocument();
        $db                      = & JFactory::getDBO();
        $uri                     = & JUri::getInstance();

        // $params = &JComponentHelper::getParams( 'com_dropbox' );
        if (!JFactory::getApplication()->isClient("administrator")) {
            $params = & $app->getParams('com_dropbox');

            JHtml::_('jquery.framework');
            $document->addScript(JURI::root() . 'components/com_dropbox/js/tinysort.js');
            $document->addScript(JURI::root() . 'components/com_dropbox/js/jquery.tinysort.charorder.js');
            $document->addCustomTag('<link href="' . JURI::root() . 'components/com_dropbox/css/dropbox.css" rel="stylesheet" type="text/css" />');
            $document->addScript(JURI::root() . 'components/com_dropbox/js/dropbox.js');
            $document->addScript(JURI::root() . 'components/com_dropbox/enlargeit/enlargeit.js');

            $document->addScript(JURI::root() . 'components/com_dropbox/js/ajaxupload.js');

            for ($i = 0; $i < $count; $i++) {
                $load = str_replace('{dropbox}', '', $matches [0] [$i]);
                $load = \intval(trim(str_replace('{/dropbox}', '', $load)));

                $query = 'SELECT params FROM #__dropbox WHERE id=' . (int) $load;
                $db->setQuery($query);
                $item_params = $db->loadResult();

                $params->merge(new JRegistry(json_decode($item_params)));

                $enl_brdsize = (int) $params->get('enl_brdsize');
                if ($enl_brdsize >= 5 && $enl_brdsize <= 30) {
                    $enl_brdsize = $enl_brdsize;
                } else {
                    $enl_brdsize = 5;
                }

                $document->addScriptDeclaration("var enl_gifpath='" . JURI::root() . "components/com_dropbox/enlargeit/';
			
											 enl_buttonurl[1] = 'prev';
											 enl_buttontxt[1] = '" . JText::_("BUTTON_PREVIOUS_PICTURE") . "';
											 enl_buttonoff[1] = -180;
											 enl_buttonurl[2] = 'next';
											 enl_buttontxt[2] = '" . JText::_("BUTTON_NEXT_PICTURE") . "';
											 enl_buttonoff[2] = -198;
											 enl_buttonurl[3] = 'close';
											 enl_buttontxt[3] = '" . JText::_("BUTTON_CLOSE") . "';
											 enl_buttonoff[3] = -126;
											 enl_buttonurl[0] = 'site:" . htmlspecialchars($mosConfig_live_site . 'index.php?option=com_dropbox&view=dropbox&format=raw&task=download&id=' . $load) . "&amp;file=';
											 enl_buttontxt[0] = '" . JText::_("BUTTON_DOWNLOAD") . "';
											 enl_buttonoff[0] = '-90'; 
											 var enl_brdsize=" . $enl_brdsize . ";    // border thickness (5-30)
											 var enl_brdcolor='" . $params->get('enl_brdcolor', '') . "';
											 var enl_brdbck='" . $params->get('enl_brdbck', '') . "';     // border background pic, '' for no pic
											var enl_brdround='" . $params->get('enl_brdround', 1) . "';    // use rounded borders
											var enl_maxstep='" . $params->get('enl_maxstep', 10) . "';    // ani steps (10-30)
											var enl_speed='" . $params->get('enl_speed', 1) . "';      // time between steps
											var enl_ani='" . $params->get('enl_ani', 5) . "';         // 0=no,1=fade,2=glide,3=bumpglide,4=smoothglide,5=expglide
											var enl_opaglide='" . $params->get('enl_opaglide', 1) . "';    // glide transparency
											var enl_shadow='" . $params->get('enl_shadow', 1) . "';      // shadow under border
											var enl_shadowsize='" . $params->get('enl_shadowsize', 10) . "';  // size of shadow right/bottom (0-20)
											var enl_shadowcolor='" . $params->get('enl_shadowcolor', '') . "';// shadow color (empty: black)
											var enl_shadowintens='" . $params->get('enl_shadowintens', 5) . "';// shadow intensity (5-30)
											var enl_dark='" . $params->get('enl_dark', 1) . "';        // darken screen (0=off/1=on/2=keep dark when nav)
											var enl_darkprct='" . $params->get('enl_darkprct', 50) . "';   // how dark the screen should be (0-100)
											var enl_darksteps='" . $params->get('enl_darksteps', 20) . "';   // how long darkening should take
											var enl_center='" . $params->get('enl_center', 1) . "';      // center enlarged pic on screen
											var enl_drgdrop='" . $params->get('enl_drgdrop', 1) . "';     // enable drag + drop for pics
											var enl_preload='" . $params->get('enl_preload', 1) . "';     // preload next/prev pic
											var enl_titlebar='" . $params->get('enl_titlebar', 1) . "';    // show pic title bar
											var enl_keynav='" . $params->get('enl_keynav', 1) . "';      // key navigation
											var enl_wheelnav='" . $params->get('enl_wheelnav', 1) . "';    // mouse wheel navigation
											var enl_titletxtcol='" . $params->get('enl_titletxtcol', '') . "';// color of title bar text (empty: dark grey)"); // border color (white if empty) ");

                $dropbox_content = "<div class='dropbox_plugin_wrapper dropbox_content' id='dropbox_wrapper_" . (int) $load . "'>
										<img src='" . $mosConfig_live_site . "components/com_dropbox/enlargeit/loader.gif'></div>";

                $document->addScriptDeclaration('
							jQuery(document).ready(function() {					
									jQuery("#dropbox_wrapper_' . (int) $load . '").load("' . $mosConfig_live_site . 'index.php?option=com_dropbox&view=dropbox&format=raw&id=' . $load . '", function(response, status, xhr) {
  if (status == "error") {
    var msg = "Error: ";
    alert(msg + xhr.status + " " + xhr.statusText);
  }
});
					
					
			
							 });');

                $row->text = preg_replace('{' . $matches [0] [$i] . '}', $dropbox_content, $row->text);
            }

            $row->text = preg_replace($regex, '', $row->text);
        }
    }
}
