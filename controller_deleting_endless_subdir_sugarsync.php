<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access

defined('_JEXEC') or die('Restricted access');

jimport('joomla.application.component.controller');
jimport('joomla.filesystem.folder');

// @codingStandardsIgnoreLine because of no namespace set
class DropboxController extends JControllerLegacy
{
    protected $model;
    protected $uri;
    protected $params;
    protected $sub_folder;
    protected $dest_folder;
    protected $box_type;
    protected $access_level;
    protected $_db;
    protected $jinput;
    protected $id;


    public function __construct()
    {

        parent::__construct();
        jimport('joomla.filesystem.file');
        $this->_db = & JFactory::getDBO();

        // Check requirements
        //TODOc add all needed requirements, not just cURL
        if (!extension_loaded('curl')) {
            JError::raiseError('500', JText::_('CURL_EXTENSION_REQUIRED'));
            return;
        }
        if (!function_exists('readfile')) {
            JError::raiseError('500', JText::_('COM_DROPBOX_READFILE_FUNCTION_REQUIRED'));
            return;
        }

        $menuitemid = (int)JInput::get('Itemid');
        $this->id = (int)JInput::get('id');


        //what kind of box (provider) do we use?
        $query = ' SELECT box_type, access FROM #__dropbox ' .
                    '  WHERE id = ' . $this->id;
        $this->_db->setQuery($query);
        $result = $this->_db->loadObject();
        $this->box_type = $result->box_type;
        $this->access_level = $result->access;

        $user        = JFactory::getUser();

        // check the access level
        $groups    = $user->getAuthorisedViewLevels();
        if (!in_array($this->access_level, $groups)) {
            JError::raiseError('403', JText::_('JERROR_ALERTNOAUTHOR'));
            return;
        }


        if ($this->box_type == 'sugarsync') {
            $this->model = &$this->getModel('sugarsync');
        } elseif ($this->box_type == 'dropbox') {
            $this->model = &$this->getModel('dropbox');
        } else {
            JError::raiseError('404', JText::_('COULD_NOT_FIND_BOX'));
        }

        $this->jinput = JFactory::getApplication()->input;

        $this->uri         = & JFactory::getURI();

        $return = $this->model->setup($this->id);

        if (!empty($return)) {
            JError::raiseWarning('0', $return);
        } else {
            $this->sub_folder = $this->jinput->get('sub_folder', null, 'STRING');

            $this->params = $this->model->getMergedParams();

            if ($this->params->get('private_user_directories', 0) > 0) {
                $user =& JFactory::getUser();

                if ($user->guest == 1) {
                    $user_subdir = "guest";
                } else {
                    if ($this->params->get('private_user_directories', 0) == 2) {
                        $user_subdir = $user->username;
                    } else {
                        $user_subdir = (int)$user->id;
                    }
                }

                $this->model->addToChroot($user_subdir);

                //change folder into the changed chroot to check if it exists
                $this->model->changeFolder();
            }


            $chroot_info = $this->model->getMetaData();
            $folder_array = array();
            $count = 0;
            $fp = fopen('/tmp/data.txt', 'a');


            while ($chroot_info->contents[0]->is_dir) {
                array_push($folder_array, $chroot_info->contents[0]->folder_id);
                $this->model->sugarsync->folder_id = $chroot_info->contents[0]->folder_id;
                fwrite($fp, $chroot_info->contents[0]->folder_id . "\n");

                $chroot_info = $this->model->getMetaData();
                $count++;
            }
            fclose($fp);

            die();

            $folder_array = file('/tmp/data.txt');
            $fp = fopen('/tmp/log.txt', 'a');
            $count = count($folder_array) - 1;
            while ($count != 0) {
                $id = $folder_array[$count];
                $return = $this->model->deleteFolder($id);
                fwrite($fp, $count . " ---- " . $this->model->getLastHTTPCode() . "\n");
                $count--;
            }
            fclose($fp);
            die();




            //if there is a file that has the same name like the user directory, we cannot do anything
            if (
                (isset($chroot_info->is_deleted) && $chroot_info->is_deleted != true)
                && (isset($chroot_info->is_dir) && $chroot_info->is_dir != true)
            ) {
                JError::raiseError('409', JText::sprintf('COM_DROPBOX_USER_DIR_IS_FILE', $chroot_info->path));
            } elseif (
                //if there is no such file or directory create it
                (isset($chroot_info->is_deleted) && $chroot_info->is_deleted == "true")
                || isset($chroot_info->error)
            ) {
                $return = $this->model->createFolder();
            }

            //change into the folder that was requested
            $this->model->changeFolder($this->sub_folder);
        }
    }

    /**
     * Method to upload a file
     *
     * @access public
     */

    public function upload()
    {
        //TODOc loading bar
        //It's cool but it would be genious if we can have a "loading bar' to see how much time
        //I have to wait to finish my current upload :)
        $error_message = '';
        $dest_folder = '';

        $app =& JFactory::getApplication();


        if ($this->params->get('function_upload', 1) == 1) {
            if ($this->params->get('allow_subfolder_upload', 1) == 1) {
                $this->dest_folder = &JInput::get('dest_folder', 'POST', 'PATH');
                $this->dest_folder = &JFolder::makeSafe($this->dest_folder);

                //TODOc it works even if we upload in a existing folder,
                //but could we save some API calls?
                if (trim($this->dest_folder) != "") {
                    if (!$this->model->createFolder($this->dest_folder)) {
                        $error_message .= JText::_('COULD_NOT_CREATE_SUBFOLDER') . "\\n";
                    }
                }
            } else {
                $this->dest_folder = '';
            }

            $file = &JInput::get('file', '', 'files', 'array');


            //$view->changeFolder($this->sub_folder. "/" . $this->dest_folder);
            //    echo  "\ncontroller:".    $this->sub_folder . "/" . $this->dest_folder . "------";
            $this->model->changeFolder($this->dest_folder);
            if ($this->sub_folder != "" && $this->sub_folder != "/") {
                $this->uri->setVar('sub_folder', rawurlencode($this->sub_folder . "/" . $this->dest_folder));
            } else {
                $this->uri->setVar('sub_folder', rawurlencode($this->dest_folder));
            }



            $file['name'] = &JFile::makeSafe($file['name']);

            switch ((int)$this->params->get('add_timestamp_to_upload', 0)) {
                case 1:
                    $file['name'] = date("Ymd-His_") . $file['name'];
                    break;
                case 2:
                    $file['name'] .= date("_Ymd-His");
            }

            // Rename uploaded file to reflect original name
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $error_message .= JText::_('FILE_WAS_NOT_SUCCESSFULLY_UPLOADED') . "\\n";
            }

            $tmpDir = uniqid(JFactory::getConfig()->get('tmp_path') . DS . 'DropboxUploader-');
            //$tmpDir2 = uniqid('/tmp/DropboxUploader-');
            //if (!mkdir($tmpDir))
            //      $error_message.= 'Cannot create temporary directory! Do you have write access to ' . JFactory::getConfig()->get('tmp_path') . ' ?'. "\\n";

            if ($file['name'] === "") {
                $error_message .= 'File name not supplied by the browser.' . "\\n";
            }

            $tmpFile = $tmpDir . DS . str_replace("/\0", '_', $file['name']);



            if (!JFile::upload($file['tmp_name'], $tmpFile)) {
                $error_message .= 'Cannot rename uploaded file!  Do you have write access to ' . JFactory::getConfig()->get('tmp_path') . ' ? Or is the file to big - check your PHP settings' . "\\n";
            }


            if ($error_message == "") {
                $upload_result = $this->model->putFile($tmpFile);



                if ($upload_result != '{"result": "winner!"}') {
                    //

                    $upload_result = json_decode($upload_result);

                    $error_message .= 'Could not send file to dropbox\\n' . $upload_result->error;
                }

                //        print_r($upload_result);
                //clean up again

                if ($this->params->get('change_folder_after_upload', 1) != 1) {
                    $this->model->changeFolder($this->sub_folder . "/");
                    $dest_folder = '';
                } else {
                    $dest_folder = $this->dest_folder;
                }
            }

            JFile::delete($tmpFile);
            JFolder::delete($tmpDir);


            $document =& JFactory::getDocument();
            $document->setMimeEncoding('text/html');
            $view = &$this->getView('dropbox', 'raw');


            if ($error_message != '') {
                $view->setData('{"dest_folder": "' . $dest_folder . '", "message": "' . $error_message . '"}');
            } else {
                $this->log('up', substr($this->model->getChroot(), 0, -1) . $this->sub_folder . DS . $this->dest_folder . DS . trim(JFile::getName($tmpFile)));

                $view->setData('{"dest_folder": "' . $dest_folder . '", "message":"' . JText::_('COM_DROPBOX_FILE_SUCCESSFULLY_UPLOADED_TO_YOUR_DROPBOX') . '"}');
            }
        }
        parent::display();
    }



    /**
     * Method to download a file
     *
     * @access public
     */

    public function download()
    {
        //        $file_to_get = $this->uri->getVar( 'file');
        $file_to_get = $this->jinput->get('file', null, 'STRING');

        //TODO brauchen wir hier dern Dateinamen? können wir ihn nicht direkt hier
        //berechnen? bzw. wie funktioniert das mit sugarsync?
        $cache_file = $this->model->getFile($file_to_get);

        if ($this->model->getLastHTTPCode() != '404' && $cache_file != false) {
            $this->log('down', $this->model->getfolder() . $file_to_get);

            header('Content-type: ' . $this->uri->getVar('mime_type'));
            //TODO build an option to directly open the files or to save them
            //             echo $file_to_get;
            header('Content-Disposition: attachment; filename="' . JFile::makeSafe($file_to_get) . '"');
            ob_end_flush();
            readfile($cache_file);


            //delete the file_lock
            JTable::addIncludePath(JPATH_COMPONENT . '/tables');
            $file_lock_table = JTable::getInstance('FileLocks', 'DropboxTable');
            $file_lock_table->delete($this->model->getFileLockID());
            $cache_dir_size = $this->getDirectorySize(dirname($cache_file));

            //If the file is too big to be kept in cache or the cache max size is reached
            //AND there are no other locks for this file, delete it
            if (
                (filesize($cache_file) > $this->params->get('max_filesize_in_cache', 99999999) * 1024
                || $cache_dir_size['size'] > $this->params->get('max_cache_size', 99999999) * 1024)
                && !$file_lock_table->getLockByHash(sha1(ltrim($this->model->getfolder(), "/") . $file_to_get))
            ) {
                JFile::delete($cache_file);
                //TODO the header file should also be deleted, but I don't like to calculate the
                //file name here again, we have to change the system.
            }

            die();
        } else {
            JError::raiseError($this->model->getLastHTTPCode(), JText::_('FILE_NOT_FOUND'));
        }
    }


    /**
     * Method to download a Thumbnail
     *
     * @access public
     */

    public function downloadThubnail()
    {

        // get the document
        $document =& JFactory::getDocument();

        $infos = $this->model->getMetaData();

        //read the file
        $file_to_get = $this->jinput->get('file', null, 'STRING');

        //        $file_to_get2 = $this->uri->getVar( 'file');
        $data = $this->model->getThumbnail($file_to_get, $this->params->get('th_size_pic', 'medium'));

        if ($this->model->getLastHTTPCode() != '404' && $this->model->getLastHTTPCode() != '403') {
            // set the MIME type
            $document->setMimeEncoding($this->uri->getVar('mime_type'));
        } else {
            $document->setMimeEncoding('text/plain');
        }

        $view = &$this->getView('dropbox', 'raw');
        $view->setData($data);
        parent::display();
    }


    /**
     * Method to display the view
     *
     * @access public
     */
    public function display()
    {

        $view = &$this->getView('dropbox', 'html');


        $files_list = $this->model->getMetaData();


        $files_pic = array();


        if (isset($files_list) && !isset($files_list->is_deleted) && !isset($files_list->error)) {
            if ($files_list->is_dir != 1) {
                $tmp_files_list = new stdClass();

                $tmp_files_list->contents = array($files_list);
                $files_list = $tmp_files_list;
            }


            $uppercase_box_type = ucfirst($this->box_type);
            $cache_folder = JFactory::getConfig()->get('tmp_path') . DS . $uppercase_box_type . "Cache" . DS;


            if (!JFolder::exists($cache_folder)) {
                JFolder::create($cache_folder);
            }

            $htaccess_file = $cache_folder . '.htaccess';


            if (!JFile::exists($htaccess_file)) {
                $htaccess_content = "<Files *>\norder deny,allow\ndeny from all\n</Files>";

                if (!JFile::write($htaccess_file, $htaccess_content)) {
                    JError::raiseWarning('0', JText::sprintf('COULD_NOT_WRITE_HTACCESS', $cache_folder));
                }
            }


            $this->model->cleanCache($cache_folder, $files_list);


            if ($this->params->get('function_pic', 1) == 1) {
                foreach ($files_list->contents as $file) {
                    if (!isset($file->mime_type)) {
                        continue;
                    }


                    if ($file->mime_type == 'image/png' || $file->mime_type == 'image/jpeg' || $file->mime_type == 'image/gif') {
                        array_push($files_pic, $file);
                    }
                }
            }




            $view->setup($this->model, $this->uri, $this->params, $files_list, $files_pic);

            if ($this->params->get('change_folder_after_upload', 1) == 1) {
                $destination = $this->sub_folder . "/" . $this->dest_folder;
            } else {
                $destination = $this->sub_folder;
            }


            $view->changeFolder($destination);
            parent::display();
        } else {
            JError::raiseWarning('0', JText::sprintf('COULD_NOT_CONNECT_TO_DROPBOX', $this->box_type, rawurldecode($this->model->getfolder())));
        }
    }

    public function log($direction, $file_to_get)
    {


        if ($direction != 'up') {
            $direction = 'down';
        }

        if ($this->params->get('log_' . $direction . 'loads', 0) == 1) {
            if ($this->model->getLastHTTPCode() == '304' && $direction == 'down') {
                $cached = 1;
            } else {
                $cached = 0;
            }

            $user =& JFactory::getUser();

            //            $file_to_get =rawurldecode($file_to_get);
            $file = trim(JFile::getName(rawurldecode($file_to_get)));
            $path = trim(substr($file_to_get, 0, strlen($file_to_get) - strlen($file)));


            $sql = "INSERT INTO `#__dropbox_logs` (
					`userId` ,
					`folder` ,
					`filename` ,
					`direction` ,
					`time` ,
					`dropboxId` ,
					`cached`
					)
					VALUES (";
            $sql .= (!$user->guest) ? (int) $user->id : 'NULL';
            $sql .= ",
					'" . $this->_db->getEscaped($path) . "', 
					'" . $this->_db->getEscaped($file) . "', 
					'" . $direction . "', 
					NOW( ), 
					'" . (int)$this->model->getID() . "', 
					'" . $cached . "'
					);";


            $this->_db->setQuery($sql);

            $this->_db->execute();
        }

        //die();
    }
    /**
     *
     * @param  string $path
     * @return array[string] containing: array['size'], array['count'] and array['dircount']
     */
    public function getDirectorySize($path)
    {
        $totalsize = 0;
        $totalcount = 0;
        $dircount = 0;
        if ($handle = @opendir($path)) {
            while (false !== ($file = readdir($handle))) {
                $nextpath = $path . '/' . $file;
                if ($file != '.' && $file != '..' && !is_link($nextpath)) {
                    if (is_dir($nextpath)) {
                        $dircount++;
                        $result = $this->getDirectorySize($nextpath);
                        $totalsize += $result['size'];
                        $totalcount += $result['count'];
                        $dircount += $result['dircount'];
                    } elseif (is_file($nextpath)) {
                        $totalsize += filesize($nextpath);
                        $totalcount++;
                    }
                }
            }

            closedir($handle);
            $total['size'] = $totalsize;
            $total['count'] = $totalcount;
            $total['dircount'] = $dircount;
            return $total;
        } else {
            return 0;
        }
    }
}
