# Dropbox Integration For Joomla

This is a Joomla plugin that allows you to integrate your Joomla site with Dropbox.
It allows you to upload files to Dropbox from your Joomla site and also allows you to download files from Dropbox to your Joomla site.

# 💻 Development

## Develop Using Docker Compose
**1. Requirements:**
- Docker (>=v19.xx.xx) (https://docs.docker.com/get-docker/ 'Get Docker')
- Docker Compose (>=v2.x.x) (https://docs.docker.com/compose/install/ 'Install Docker Compose')

**2. Prepare for the extension:**
```shell
git clone https://github.com/JankariTech/DropboxExtensionForJoomla.git
cd DropboxExtensionForJoomla && make install
```

**3. Docker Compose:**

Services
- **Joomla**:
  - will be available at [http://localhost:8080](http://localhost:8080 'Joomla Server Address') by default
  - Environment:
    - `JOOMLA_VERSION`: with this environment variable, we can provide different tags to the <PERSON><PERSON><PERSON> docker image.
- **MySQL**:
   - will be available at [http://localhost:3006](http://localhost:3006 'MySQL Server Address') by default
   - Environments:

     The database server is spun up with the following credentials: 
     - `DB_NAME`: **joomla**
     - `DB_USER`: **joomla**
     - `DB_PASSWORD`: **joomla**
     - `DB_HOST`: **mysql** 

First, copy the `docker-compose.override.example.yml` file and update it with the desired ports settings.

```shell
cp docker-compose.override.example.yml docker-compose.override.yml
```

Now, run the following command to start the services:

```bash
make docker-run-with-v4 # starts Joomla v4
```

> Note: Regularly update the docker containers used in the project.
> 
> ```bash
> make docker-update
> ``` 

**4. Setup Joomla & Install the Extension:**

- Go to [http://localhost:8080](http://localhost:8080 'Joomla Server Address')
- Setup admin user & database
- Go to admin panel at [http://localhost:8080/administrator/](http://localhost:8080/administrator/ 'Administration Panel')
- Go to `System` -> `Install` -> `Extensions`
- Select `Install from Folder` tab
- Fill the input `Install from Folder ` with `/var/www/html/ext/dropbox` (loaded inside the docker container using docker volume)
- Click `Check & Install`

If you are not a fan of UI installation and want to do automation for this, we have scripts for this.

```shell
# setup joomla
php /<path-to-joomla>/installation/joomla.php install --site-name=<site-name> --admin-user=<admin-name> --admin-username=<admin-username> --admin-password=<admin-password> --admin-email=<admin-email> --db-type=<db-type> --db-host=localhost --db-encryption=0 --db-user=<db-username> --db-name=<db-name> --db-pass=<db-password> --db-prefix=<db-prefix> --public-folder=<public-folder-path>
# build the extension
VERSION=<version> SKIP_RELEASE=true make build
# install the extension
php /var/your/joomla/cli/joomla.php sudo php cli/joomla.php extension:install --path build/assets/com_dropbox_<version>.zip
```

> Note: Choose the Joomla version you want to install the extension on.

With these steps, Joomla is now ready to use with the plugin installed.

**5. Get refresh token for Dropbox API**

The Dropbox API is needed to do the cleanups in the test infrastructure. To get the refresh token, follow the steps below:
- First, create a new folder using the extension administration
- Click the `Connect` button, allow the application to access your Dropbox account and copy the `code` provided
- Now using curl request we can get the refresh token
  ```bash
  curl https://api.dropbox.com/oauth2/token -d code=<code> -d grant_type=authorization_code -uqea2qg672mampkw:09nb5jag9pki1sl
  ```
- First, copy the `env.example` file and paste the refresh token at 'DROPBOX_REFRESH_TOKEN'.

```shell
cd tests/e2e
cp .env.example .env
```

**6. Run e2e tests:**

E2E tests are written using Playwright. To run the tests, follow the steps below:

```bash
cd tests/e2e
# runs all the e2e tests
npm run test:e2e
# or
# runs the tests in the feature file
npm run test:e2e features/feature1.feature
```


# Limitations

**1. Search results:**

Filtering resources with names containing `-`(hyphen) or `_`(underscore) or similar characters shows irrational behavior. For details, visit [Dropbox Extension Issue](https://github.com/JankariTech/DropboxExtensionForJoomla/issues/39#issuecomment-**********)
### *Workaround for test*
A. Do not use `-`(hyphen) or `_`(underscore) or similar characters in file/sub-folder name
- *Prefer file name `file1` instead of `file-1` or `file_1`*
- *Prefer sub-folder name `folderOne` instead of `folder-one` or `folder_one` or `one-folder`*

B. Use complete file/sub-folder name while searching for an exact file/sub-folder
 - *Use `file1.png` to search for the file instead of term such as `file1` only*
 - *Use `folderOne` to search for the sub-folder instead of term such as `One` only*
