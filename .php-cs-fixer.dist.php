<?php
/**
 * This is the configuration file of <PERSON><PERSON><PERSON>
 *
 * @see https://github.com/joomla/joomla-cms/blob/5.2-dev/.php-cs-fixer.dist.php
 *
 */

$finder = PhpCsFixer\Finder::create()
    ->ignoreVCSIgnored(true)
    // Ignore template files as PHP CS fixer can't handle them properly
    ->notPath('language')
    ->notPath('tests/e2e')
    ->notPath('tests/data')
    ->notPath('site/vendor')
    ->in(
        [
            __DIR__ . '/admin',
            __DIR__ . '/site',
            __DIR__ . '/plugin',
            __DIR__ . '/tests',
        ]
    )
    // Ignore psr12 scripts because they contain invalid syntax
    ->notPath('/psr12/');
$config = new PhpCsFixer\Config();
$config
    ->setParallelConfig(PhpCsFixer\Runner\Parallel\ParallelConfigFactory::detect())
    ->setRiskyAllowed(true)
    ->setHideProgress(false)
    ->setUsingCache(false)
    ->setRules(
        [
            // Basic ruleset is PSR 12
            '@PSR12'                         => true,
            // Short array syntax
            'array_syntax'                   => ['syntax' => 'short'],
            // List of values separated by a comma is contained on a single line should not have a trailing comma like [$foo, $bar,] = ...
            'no_trailing_comma_in_singleline' => true,
            // Arrays on multiline should have a trailing comma
            'trailing_comma_in_multiline'    => ['elements' => ['arrays']],
            // Align elements in multiline array and variable declarations on new lines below each other
            'binary_operator_spaces'         => ['operators' => ['=>' => 'align_single_space_minimal', '=' => 'align']],
            // The "No break" comment in switch statements
            'no_break_comment'               => ['comment_text' => 'No break'],
            // Remove unused imports
            'no_unused_imports'              => true,
            // Classes from the global namespace should not be imported
            'global_namespace_import'        => ['import_classes' => false, 'import_constants' => false, 'import_functions' => false],
            // Alpha order imports
            'ordered_imports'                => ['imports_order' => ['class', 'function', 'const'], 'sort_algorithm' => 'alpha'],
            // There should not be useless else cases
            'no_useless_else'                => true,
            // Native function invocation
            'native_function_invocation'     => ['include' => ['@compiler_optimized']],
            // Handle mixed PHP and HTML content
            'braces' => ['position_after_functions_and_oop_constructs' => 'next'],
            'concat_space' => ['spacing' => 'one'],
            'phpdoc_align' => ['align' => 'vertical'],
            'phpdoc_order' => true,
            'phpdoc_types' => true,
            'no_extra_blank_lines' => ['tokens' => ['attribute', 'break', 'case', 'continue', 'curly_brace_block', 'default', 'extra', 'parenthesis_brace_block', 'return', 'square_brace_block', 'switch', 'throw', 'use']]
        ]
    )
    ->setFinder($finder);

return $config;
