<?xml version="1.0" encoding="UTF-8"?>
<project name="com_dropbox for Joomla 3.0" default="copy_all" basedir=".">
	<property name="src"   value="."/>
	<property name="joomla37"  value="../test_joomla37" />
	<property name="joomla35"  value="../test_joomla35" />

	<!-- Fileset for all plugin files -->
    <fileset dir="plugin" id="pluginfiles">
        <include name="**" />
    </fileset> 	
	
	<!-- Fileset for all site files -->
    <fileset dir="site" id="componentsitefiles">
        <include name="**" />
    </fileset> 
    
	<!-- Fileset for all admin files -->
    <fileset dir="admin" id="componentadminfiles">
        <include name="**" />
    </fileset>    
     
    <!-- Fileset for all files -->
    <fileset dir="." id="allcomponentfiles">
        <include name="**" />
        <include name="**" />
        <exclude name="*.zip" />
        <exclude name=".*" />
        <exclude name="*.dia" /> 
        <exclude name="*.docx" />
        <exclude name=".*/**"/>
        <exclude name="*plugin*"/>
        <exclude name="tests/**"/>
        <exclude name="build.xml" /> 
    </fileset>
     
	<target name="copy_all" description="Copies files to test projects.">
		<echo message="Running build.xml. Copying files from dev to test..." />
		
		<echo message="--- script.php ..." />
 		<copy file="${src}/script.php" todir="${joomla37}/administrator/components/com_dropbox/"/>
 		<copy file="${src}/script.php" todir="${joomla35}/administrator/components/com_dropbox/"/>

		
		<echo message="--- dropbox.xml ..." />
		<copy file="${src}/dropbox.xml" todir="${joomla37}/administrator/components/com_dropbox/"/>
		<copy file="${src}/dropbox.xml" todir="${joomla35}/administrator/components/com_dropbox/"/>

		  
		<echo message="--- sitefiles ..." />
	    <copy todir="${joomla37}/components/com_dropbox/">
            <fileset refid="componentsitefiles" />
        </copy>

	    <copy todir="${joomla35}/components/com_dropbox/">
            <fileset refid="componentsitefiles" />
        </copy>

        
        <echo message="--- adminfiles ..."/>
	    <copy todir="${joomla37}/administrator/components/com_dropbox/">
            <fileset refid="componentadminfiles" />
        </copy>      
	    <copy todir="${joomla35}/administrator/components/com_dropbox/">
            <fileset refid="componentadminfiles" />
        </copy>  

        <echo message="--- pluginfiles ..."/>
	    <copy todir="${joomla37}/plugins/content/dropboxplugin/">
            <fileset refid="pluginfiles" />
        </copy>
	    <copy todir="${joomla35}/plugins/content/dropboxplugin/">
            <fileset refid="pluginfiles" />
        </copy>
        
    	<echo message="Creating archive..." />
    	<tstamp>
  			<format property="DATE" pattern="%Y%m%d_%H%M"/>
		</tstamp>
   		<zip destfile="com_dropbox${DATE}.zip">
 	   		 <fileset refid="allcomponentfiles" />
		</zip>
   		<zip destfile="dropboxplugin${DATE}.zip">
 	   		 <fileset refid="pluginfiles" />
		</zip>
         
	</target>

</project>
