//var jQuery = jQuery.noConflict();

var mouse_x;
var mouse_y;


jQuery(document).bind(
    'mousemove',
    function (e) {
        mouse_x = e.pageX - jQuery(document).scrollLeft();
        mouse_y = e.pageY - jQuery(document).scrollTop();

    }
);


var dropbox_list_aAsc = [];
function com_dropbox_sort_table(wrapper_id,name,direction)
{

    if (typeof direction != "undefined") {
        dropbox_list_aAsc[name] = direction;
    } else {
        dropbox_list_aAsc[name] = dropbox_list_aAsc[name] == 'asc' ? 'desc' : 'asc';
    }

    jQuery('div#dropbox_wrapper_' + wrapper_id).find('div.sorting_icon_asc').hide();
    jQuery('div#dropbox_wrapper_' + wrapper_id).find('div.sorting_icon_desc').hide();
    jQuery('div#dropbox_wrapper_' + wrapper_id).find('div.file_listing_header_cell.' + name).find('div.sorting_icon_' + dropbox_list_aAsc[name]).show();
    jQuery('div#dropbox_wrapper_' + wrapper_id).find('div.db_file_icon').tsort('div.' + name,{order:dropbox_list_aAsc[name]});
}


function search_dropbox_file(source_element,dropbox_search_url)
{
    var value = source_element.val();
    var noWhitespaceValue = value.replace(/\s+/g, '%20');
    if (noWhitespaceValue.length >= 0) {
        jQuery("#dropbox_loading_indicator").show();


        jQuery("#dropbox_loading_indicator").css('left', mouse_x - 15 + 'px');
        jQuery("#dropbox_loading_indicator").css('top', mouse_y - 15 + 'px');

        dropbox_search_url = addQueryString(dropbox_search_url,"search=" + noWhitespaceValue);
        jQuery.get(
            dropbox_search_url,
            function (data) {

                jQuery("#dropbox_loading_indicator").hide();
                source_element.parent().html(data);
            }
        );
    }
}


function change_dropbox_dir(dest_url,id)
{


    jQuery("#dropbox_loading_indicator").show();


    jQuery("#dropbox_loading_indicator").css('left', mouse_x - 15 + 'px');
    jQuery("#dropbox_loading_indicator").css('top', mouse_y - 15 + 'px');

    jQuery(document).ready(
        function () {
            var jqxhr = jQuery.get(
                dest_url,
                function (data) {
                    if (typeof(enl_buttonurl) == 'object' && typeof (enl_buttonurl[0]) == "string" && enl_buttonurl[0]) {
                        enl_buttonurl[0] = 'site:' + dest_url + '&task=download&file=';
                    }
                    $("#dropbox_wrapper_"+id).html(data);
                }
            );
            jqxhr.fail(
                function (data) {
                    alert(data.statusText);
                }
            );
            jqxhr.always(
                function (data) {
                    jQuery("#dropbox_loading_indicator").hide();
                }
            );
        }
    );


}


function hide_show_fields_for_different_box_types()
{
}

/**
 * Function to make the Tabs on the `Edit` page and `New` page responsive
 * Tabs: (Details, Parameters, List Function, Upload Function, Pictures Function)
 */
function makeTabsResponsiveDropbox()
{
    // If any of the tab element is clicked, then set the corresponding tab-panel to be active
    let navElements = document.querySelectorAll(".nav-tabs li");

    for (let el of navElements) {
        el.addEventListener("click", function () {
            const childElement = el.querySelector("a[data-toggle='tab']");
            // remove the currently active tab-panel from the active class
            document
                .querySelector(".tab-pane.active")
                .classList.remove("active");

            // remove the currenly active navigation tab from the active class
            document
                .querySelector(".nav-tabs li.active")
                .classList.remove("active");

            // add the currently clicked navigation tab to the active class
            el.classList.add("active");

            // add the corresponding tab-panel to the active class
            document
                .querySelector(".tab-pane" + childElement.getAttribute("href"))
                .classList.add("active");
        });
    }
}
function removePreviousJoomlaAlert(){
    const alerts = document.querySelectorAll('joomla-alert');

    // Iterate over the NodeList
    alerts.forEach(alert => {
        if (alert.getAttribute('type') === 'danger') {
            alert.remove(); // Remove the alert from the DOM
        }
    });
}

// Function to update button attributes
function updateButtonAttributeDisable(parentElement,action='add') {
    const button = parentElement.querySelector('button');
    switch (action) {
        case "add":
            button.setAttribute('disabled', '');
            break;
        case "remove":
            button.removeAttribute('disabled');
            break;
    }
}

// validate folder on event change
function validate_folder()
{
    const invalidNameLength = 'The folder name must have 1 to 255 characters';
    const invalidCharsMessage = 'The folder name must not contain angle brackets, \\, /, :, ?, *, ", | or \'';
    const regex = /^[^<>:?*"'/|\\]*$/;
    let folderNameInput = document.getElementById('jform_folder');
    if (folderNameInput.value.trim().length < 1 || folderNameInput.value.trim().length  > 255) {
        removePreviousJoomlaAlert();
        updateButtonAttributeDisable(document.getElementById('toolbar-save'), 'add');
        updateButtonAttributeDisable(document.getElementById('toolbar-apply'), 'add');
        Joomla.renderMessages({'error': [invalidNameLength]}, undefined, false);
        folderNameInput.value = '';
        return false;
    } else if (!regex.test(folderNameInput.value)) {
        removePreviousJoomlaAlert()
        updateButtonAttributeDisable(document.getElementById('toolbar-save'),'add');
        updateButtonAttributeDisable(document.getElementById('toolbar-apply'),'add');
        Joomla.renderMessages({'error': [invalidCharsMessage]},undefined,false);
        folderNameInput.value = '';
        return false;
    }
    removePreviousJoomlaAlert()
    updateButtonAttributeDisable(document.getElementById('toolbar-save'),'remove');
    updateButtonAttributeDisable(document.getElementById('toolbar-apply'),'remove');
    return true;
}

/**
 * Function to validate Folder and DropboxCode input box
 */
function hide_show_connect_preview_buttons()
{
    dropboxCode = jQuery("#jform_dropbox_secret").val().trim()
    folderName = jQuery("#jform_folder").val().trim()
    if ((dropboxCode === '' && folderName === '') || (folderName === '')){
        jQuery("#toolbar-arrow-right-4").hide();
        jQuery("#toolbar-out-2").hide();
    } else if (folderName !== '' && dropboxCode !== '') {
        jQuery("#toolbar-arrow-right-4").hide(); //connect button
        jQuery("#toolbar-out-2").show(); // preview button
    } else if (dropboxCode === '' && folderName !== ''){
        jQuery("#toolbar-arrow-right-4").show();
        jQuery("#toolbar-out-2").hide();
    }
}

function validate_form(){
    if (document.getElementById('jform_folder').value.length < 1) {
        updateButtonAttributeDisable(document.getElementById('toolbar-save'), 'add');
        updateButtonAttributeDisable(document.getElementById('toolbar-apply'), 'add');
    }
    hide_show_connect_preview_buttons();
}


function addQueryString(url, queryString)
{
    if (queryString) {
        var isQuestionMarkPresent = url && url.indexOf('?') !== -1,
        separator = isQuestionMarkPresent ? '&' : '?';
        url += separator + queryString;
    }

    return url;
};

