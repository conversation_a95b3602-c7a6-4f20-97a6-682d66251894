<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access

\defined('_JEXEC') or die('Restricted access');

jimport('joomla.application.component.model');
jimport('joomla.filesystem.folder');
require_once JPATH_COMPONENT . DS . 'helpers' . DS . 'dropbox.php';

require_once JPATH_COMPONENT . DS . 'vendor' . DS . 'autoload.php';

use Kunnu\Dropbox\Dropbox;
use Kunnu\Dropbox\DropboxApp;
use Kunnu\Dropbox\Models\AccessToken;
use PHPUnit\DbUnit\Database\Metadata\Metadata;

/**
 * Dropbox API class
 *
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @copyright  Copyright (C) 2010 Rooftop Solutions. All rights reserved.
 * <AUTHOR> Pot (http://www.rooftopsolutions.nl/)
 * @license    GNU/GPL
 */
// @codingStandardsIgnoreLine because of no namespace set
class DropboxModelDropbox extends JModelLegacy
{
    /**
     * The Object with all infos about the actual dropbox
     *
     * @var JParameter Object
     */
    protected $dropbox;

    /**
     * Object to talk with the Dropbox API
     *
     * @var Kunnu\Dropbox\Dropbox
     */
    protected $dropboxAPI;

    /**
     * The destination content Dropbox URL
     *
     * @var string
     */
    protected $baseUri = 'https://api-content.dropbox.com/1/';

    /**
     * Last HTTP Code
     *
     * @var boolean
     */
    protected $write_lock;

    /**
     * all params of the dropbox, these are merged and overriden
     *
     * @var JParameter Object
     */
    protected $merged_params;

    /**
     * the id of the file lock
     *
     * @var int
     */
    protected $file_lock_id;

    protected $app_key = 'qea2qg672mampkw';

    protected $app_secret = '09nb5jag9pki1sl';

    /**
     * Takes the error from Kunnu\Dropbox\Dropbox Exception,
     * finds the error message in it and returns it
     * $result['error'] contains the error message
     *
     * @param  json formated string $error
     * @return array
     */
    protected function decodeError($error)
    {
        $result   = [];
        $response = json_decode($error, true);
        if ($response !== null && $response ["error"] [".tag"] === "path") {
            $result ['error'] = $response ["error"] ["path"] [".tag"];
        } elseif ($response !== null && $response ["error"] [".tag"] === "file") {
            $result ['error'] = $response ["error"] ["file"] [".tag"];
        } else {
            $result ['error'] = $error;
        }
        return $result;
    }
    /**
     * Setup the Class
     */
    public function setup($id = null)
    {
        jimport('joomla.html.parameter');
        $app = JFactory::getApplication('site');

        //get the dropbox params
        $query = ' SELECT * FROM #__dropbox ' .
        '  WHERE id = ' . (int)$id;
        $this->_db->setQuery($query);
        $this->dropbox = $this->_db->loadObject();

        $this->merged_params =  $app->getParams('com_dropbox');
        $this->merged_params->merge(new JRegistry(json_decode($this->dropbox->params)));

        if (
            \strlen(trim($this->dropbox->dropbox_secret)) <= 1 || \strlen(
                trim($this->dropbox->dropbox_token)
            ) <= 1
        ) {
            $this->dropboxAPI = new Dropbox(
                new DropboxApp($this->app_key, $this->app_secret)
            );
            $authHelper = $this->dropboxAPI->getAuthHelper();

            // check if we already have a secret code
            if (\strlen(trim($this->dropbox->dropbox_secret)) <= 1) {
                $this->dropbox->dropbox_token = "";
                $this->_db->updateObject("#__dropbox", $this->dropbox, "id");

                // Configure Dropbox service
                $authUrl = $authHelper->getAuthUrl(null, [], null, 'offline');

                header('Location: ' . $authUrl);
                exit();
            }
            if (\strlen(trim($this->dropbox->dropbox_token)) <= 1) {
                try {
                    $accessToken                          = $authHelper->getAccessToken($this->dropbox->dropbox_secret); // This is where the access token is generated using the Auth code received by clicking on `Allow`
                    $this->dropbox->dropbox_token         = $accessToken->getToken();
                    $this->dropbox->dropbox_refresh_token = $accessToken->getRefreshToken(); // Also receive the refresh token

                    // expiry time (in seconds)
                    $expiry_time = $accessToken->getExpiryTime();
                    $expires_in  = time() + $expiry_time;

                    $this->dropbox->dropbox_expiry_time = $expires_in;
                } catch (Exception $e) {
                    $this->dropbox->dropbox_secret = "";
                    throw new Exception(JText::_('COM_DROPBOX_COULD_NOT_CREATE_TOKEN') . " " . $e->getMessage());
                } finally {
                    $this->_db->updateObject("#__dropbox", $this->dropbox, "id");
                }
            }
        } elseif ($this->dropbox->dropbox_refresh_token && ($this->dropbox->dropbox_expiry_time > 0)) {
            $currDate = time();
            $interval = $this->dropbox->dropbox_expiry_time - $currDate;

            if ($interval < 600) {
                // "You have less than 10 minutes left";
                // generate new access token
                $this->dropboxAPI = new Dropbox(
                    new DropboxApp($this->app_key, $this->app_secret)
                );
                $authHelper = $this->dropboxAPI->getAuthHelper();

                $accessToken                  = new AccessToken(['refresh_token' => $this->dropbox->dropbox_refresh_token]);
                $newAccessToken               = $authHelper->getRefreshedAccessToken($accessToken);
                $this->dropbox->dropbox_token = $newAccessToken->getToken();

                // expiry time (in seconds)
                $expiry_time = $newAccessToken->getExpiryTime();
                $expires_in  = time() + $expiry_time;

                $this->dropbox->dropbox_expiry_time = $expires_in;
                $this->_db->updateObject("#__dropbox", $this->dropbox, "id");
            }
        }

        $this->dropboxAPI = new Dropbox(
            new DropboxApp($this->app_key, $this->app_secret, $this->dropbox->dropbox_token)
        );
        //TODO we change $this->dropbox->folder here and in changeFolder directly. Need to make a setter for that
        //put a slash an the end
        if (substr($this->dropbox->folder, -1) != '/') {
            $this->dropbox->folder .= "/";
        }
        //put a slash an the beginning
        if (substr($this->dropbox->folder, 0, 1) !== '/') {
            $this->dropbox->folder = "/" . $this->dropbox->folder;
        }
        //set the chroot to the given folder, so nobody can go above it
        $this->dropbox->chroot = rawurlencode($this->dropbox->folder);

        //but we need the "/" to be unencoded
        $this->dropbox->chroot = str_replace("%2F", "/", $this->dropbox->chroot);
        $this->dropbox->folder = $this->dropbox->chroot;
    }

    /**
     * adds a subfolder to the chroot folder, so chroot can start further down
     * do not add a / at the end of the folder name
     * just one subfolder can be added. So "subfolder/nextone" WILL NOT WORK
     *
     * @param string $path Path to new chroot
     */
    public function addToChroot($path = "")
    {
        //if the programmer didn't read the comment we have to help and delete slash at the end
        if (substr($path, -1) == '/') {
            $path = substr($path, 0, -1);
        }
        $this->dropbox->chroot .= rawurlencode(JFolder::makeSafe($path)) . "/";
        $this->dropbox->folder = $this->dropbox->chroot;
    }

    public function setChroot($root)
    {
        $this->dropbox->chroot = $root;
    }

    public function getDropboxAPI()
    {
        return $this->dropboxAPI;
    }
    public function getMergedParams()
    {
        return $this->merged_params;
    }

    /**
     * Change the folder
     */
    public function changeFolder($path = "/")
    {
        $path = trim($path ?? "");
        //TODO validate $path
        $folders = explode("/", $path);
        $path    = '';
        if (\sizeof($folders) >= 1) {
            foreach ($folders as $folder) {
                if ($folder != '') {
                    $path .= rawurlencode(trim($folder)) . "/";
                }
            }
        }
        //TODO validate $this->dropbox->folder
        $this->dropbox->folder .= $path;
    }

    /**
     * Returns the box-ID (out of the Database)
     *
     * @return int
     */
    public function getID()
    {
        return $this->dropbox->id;
    }

    /**
     * Returns the file-lock-ID (out of the Database)
     * false if lock id is not set
     *
     * @return int
     */
    public function getFileLockID()
    {
        if (isset($this->file_lock_id) && $this->file_lock_id !== false) {
            if (!\is_int($this->file_lock_id)) {
                throw new Exception("file lock is not integer");
            }
            return $this->file_lock_id;
        }
    }

    /**
     * Returns the actual folder
     *
     * @return string
     */
    public function getfolder()
    {
        return $this->dropbox->folder;
    }
    /**
     * Returns the chroot folder
     *
     * @return string
     */
    public function getChroot()
    {
        return $this->dropbox->chroot;
    }

    /**
     * downloads a file an returns it's Metadata
     *
     * @param string $fileName
     *                         path (NOT rawurlsencoded)
     *
     * @throws Exception in case Cache File could not be written
     * @return array     file Metadata
     */
    public function getFile($fileName = '')
    {
        JTable::addIncludePath(JPATH_COMPONENT . '/tables');
        $file_lock_table = JTable::getInstance('FileLocks', 'DropboxTable');

        $fileName = rawurlencode(ltrim(trim($fileName), '/'));

        $file_to_get  = ltrim($this->dropbox->folder, '/') . $fileName;
        $fileMetaData = $this->getMetaData($fileName);
        if (isset($fileMetaData ['error'])) {
            $this->file_lock_id = false;
            return $fileMetaData;
        }

        $max_time_to_wait = \ini_get('max_execution_time');
        if ($max_time_to_wait == 0) {
            $max_time_to_wait = $this->merged_params->get(
                'max_writelog_sleeping_time',
                10
            );
        } else {
            $max_time_to_wait = $max_time_to_wait / 10 * 9;
        }
        $time_waited = 0;
        $lock        = '';
        $config      = JFactory::getConfig();
        $cachePath   = $config->get('tmp_path') . DS . "DropboxCache" . DS;

        while ($time_waited < $max_time_to_wait) {
            $file_lock_table->deleteOldLocks();
            $lock = $file_lock_table->getLockByHash($fileMetaData['id']);

            //we cannot write the file as its write-locked#
            //but we still can read it and present it to the user
            if ($lock === "write") {
                // create an own lock for this transaction of the file
                $file_lock_table->save(
                    [
                    "file_name_hash" => $fileMetaData ['id'],
                    "lock_type"      => "w",
                    "time"           => time(),
                    ]
                );
                $this->file_lock_id = $file_lock_table->id;
                //TODO what to do with this http code?
                $this->write_lock = true;
                return $fileMetaData;
            } elseif ($lock === "read") {
                sleep(1);
                $time_waited++;
            } else {
                //we are allowed to write the file, so lets see if we need to update the cache
                //and then present the file to the user
                if (file_exists($cachePath) === false) {
                    if (mkdir($cachePath, 0770) === false) {
                        throw new Exception(
                            JText::_('COM_DROPBOX_COULD_CREATE_CACHE_FOLDER')
                        );
                    }
                }
                $dataFile   = $cachePath . 'DropboxCache-' . $fileMetaData ['id'];
                $headerFile = $cachePath . 'DropboxCacheHeader-' . $fileMetaData ['id'];
                $file_lock_table->save(
                    [
                            "file_name_hash" => $fileMetaData ['id'],
                            "lock_type"      => "r",
                            "time"           => time(),
                    ]
                );
                $this->file_lock_id = $file_lock_table->id;
                try {
                    $result = $this->dropboxAPI->download("/" . rawurldecode($file_to_get));
                } catch (Exception $e) {
                    $result = $this->decodeError($e->getMessage());
                    throw new Exception($result ['error']);
                }

                if (file_exists($headerFile) && file_exists($dataFile)) {
                    $headerFileContents = file_get_contents($headerFile);
                    if ($headerFileContents === false) {
                        throw new Exception(
                            JText::sprintf(
                                'COM_DROPBOX_COULD_NOT_OPEN_FILE',
                                $headerFile
                            )
                        );
                    }
                    $cachedMetaData = json_decode($headerFileContents, true);
                    if (
                        isset($cachedMetaData["content_hash"])
                        && $cachedMetaData["content_hash"] === $fileMetaData["content_hash"]
                    ) {
                        //file is downloaded so change the lock to a write lock
                        $file_lock_table->lock_type = "w";
                        $file_lock_table->store();

                        return $fileMetaData;
                    }
                }

                $dataFileHandle = fopen($dataFile, "w");
                if ($dataFileHandle === false) {
                    throw new Exception(
                        JText::sprintf(
                            'COM_DROPBOX_COULD_NOT_OPEN_FILE',
                            $dataFile
                        )
                    );
                }
                fwrite($dataFileHandle, $result->getContents());
                fclose($dataFileHandle);

                $headerFileHandle = fopen($headerFile, "w");
                if ($headerFileHandle === false) {
                    throw new Exception(
                        JText::sprintf(
                            'COM_DROPBOX_COULD_NOT_OPEN_FILE',
                            $headerFile
                        )
                    );
                }
                fwrite($headerFileHandle, json_encode($result->getData()));
                fclose($headerFileHandle);

                //file is downloaded so change the lock to a write lock
                $file_lock_table->lock_type = "w";
                $file_lock_table->store();
                return $fileMetaData;
            }
        }

        throw new Exception(JText::_('COM_DROPBOX_FILE_LOCK_TIMEOUT'));
    }

    /**
     * Returns a thumbnail (binary data) for a file
     * in current path ($this->dropbox->folder)
     *
     * @param  string    $fileName file name
     * @param  string    $size     'thumb','small','medium','large','huge'
     * @throws Exception if something went wrong
     * @return binary    image data (JPG)
     */
    public function getThumbnail($fileName = '', $size = 'large')
    {
        if (!\in_array($size, ['thumb','small','medium','large','huge'])) {
            throw new InvalidArgumentException("illegal size");
        }
        $absulutePathOfFile = $this->dropbox->folder .
        ltrim($fileName, '/');
        try {
            $response = $this->dropboxAPI->getThumbnail(
                rawurldecode($absulutePathOfFile),
                $size
            );
        } catch (Exception $e) {
            $result = $this->decodeError($e->getMessage());
            throw new Exception($result ['error']);
        }
        return $response->getContents();
    }

    /**
     * Returns whether the write operation is locked.
     *
     * @return bool True if write lock is active, false otherwise.
     */
    public function getWriteLock()
    {
        return $this->write_lock;
    }

    /**
     * Uploads a new file
     *
     * @param  string    $file a path to a file
     * @throws Exception in case of an error
     * @return string    path of uploaded file
     */
    public function putFile($file)
    {
        if (!\is_string($file)) {
            throw new Exception("could not get name of uploaded file");
        }
        if (!is_file($file)) {
            throw new Exception("file to upload does not exists");
        }

        if (!is_readable($file)) {
            throw new Exception("file to upload is not readable");
        }

        try {
            $fileUploaded = $this->dropboxAPI->upload(
                realpath($file),
                rawurldecode($this->dropbox->folder) . basename($file),
                [ 'autorename' => true ]
            );
            if (\is_string($fileUploaded->getName())) {
                $recheckTimeout = 10;
                do {
                    sleep(1);
                    $waitReturn = $this->getMetaData($fileUploaded->getName());
                    $recheckTimeout--;
                    if ($recheckTimeout < 0) {
                        throw new Exception(
                            "no error during file upload," .
                            " but when doublechecking it was not there"
                        );
                    }
                } while (\array_key_exists('error', $waitReturn));
            } else {
                throw new Exception("could not get name of file after upload");
            }
        } catch (Exception $e) {
            //dropbox exceptions come as json and need to be decoded
            if (json_decode($e->getMessage()) !== null) {
                $error = $this->decodeError($e->getMessage());
            } else {
                $error = $e->getMessage();
            }
            throw new Exception($error);
        }
        return $fileUploaded->getPathDisplay();
    }

    /**
     * Creates a new folder
     *
     * This method returns the information from the newly created directory
     *
     * @param  string   $path
     * @return stdclass
     */
    public function createFolder($path = "")
    {
        $path = trim($path);
        $path = trim($path, "/");
        $path = DropboxHelper::makeFoldernameSafe($path);
        try {
            $result = $this->dropboxAPI->createFolder(
                rawurldecode(
                    rtrim($this->dropbox->folder . $path, "/")
                )
            );
            return $result->getData();
        } catch (Exception $e) {
            $result = $this->decodeError($e->getMessage());
            throw new Exception($result ['error']);
        }
    }

    /**
     * Returns file and directory information
     *
     * @param  string $path file or folder to get Metadata from. Should be rawurlencoded and should NOT contain the dropbox folder $this->dropbox->folder
     * @return array
     */
    public function getMetaData($path = "")
    {
        try {
            $response = $this->dropboxAPI->getMetadata(
                rawurldecode(rtrim($this->dropbox->folder . $path, "/"))
            );
            $result = $response->getData();
        } catch (Exception $e) {
            $result = $this->decodeError($e->getMessage());
        }
        return $result;
    }

    /**
     * searches for documents
     * data structure is changed so it matches the onle of listFolder()
     *
     * @param  string                   $query
     * @param  string                   $path       path to make the search at, the current path
     *                                              dropbox->folder will be added to that
     * @param  number                   $maxResults
     * @throws InvalidArgumentException
     * @return string|array
     */
    public function search($query, $path = "", $maxResults = 100)
    {
        if (! \is_string($query) || empty($query)) {
            throw new InvalidArgumentException('invalid search query');
        }
        $query = trim($query);

        // TODO do we really want that? maybe we want to have an other
        // sanitation
        $query = DropboxHelper::makeSearchSafe($query);
        if (empty($query)) {
            throw new InvalidArgumentException('invalid search query');
        }

        $searchResult = $this->dropboxAPI->search(
            rawurldecode(rtrim($this->dropbox->folder . '/' . $path, "/")),
            $query,
            ['max_results' => (int) $maxResults]
        )->getData();
        $decodedChroot           = rawurldecode($this->dropbox->chroot);
        $searchResult['entries'] = [];

        //add "cleanPath" to every entry
        for ($mNum = 0; $mNum < \count($searchResult ['matches']); $mNum++) {
            $searchResult ['matches'] [$mNum] ['metadata'] ['cleanPath'] =
            DropboxHelper::makeCleanPath(
                $decodedChroot,
                $searchResult ['matches'] [$mNum] ['metadata'] ['path_lower'],
                $searchResult ['matches'] [$mNum] ['metadata'] ['.tag']
            );
            array_push(
                $searchResult ['entries'],
                $searchResult ['matches'] [$mNum] ['metadata']
            );
        }
        unset($searchResult ['matches']);
        return $searchResult;
    }

    /**
     * list content of folder.
     * for data structure see:
     * https://www.dropbox.com/developers/documentation/http/documentation#files-list_folder
     * additionall value: cleanPath => path without the chroot of the
     * dropbox connection and the file name
     *
     * @param  string    $path
     * @throws Exception
     * @return array
     */
    public function listFolder($path = "")
    {
        try {
            $folderContents = $this->dropboxAPI->listFolder(
                rawurldecode(rtrim($this->dropbox->folder . '/' . $path, "/")),
                ["include_media_info" => false]
            );
        } catch (Exception $e) {
            $result = $this->decodeError($e->getMessage());
            if ($result ['error'] === 'not_found') {
                $result ['error'] = JText::_('COM_DROPBOX_COULD_NOT_OPEN_FOLDER');
            }
            throw new Exception($result ['error']);
        }
        $result        = $folderContents->getData();
        $decodedChroot = rawurldecode($this->dropbox->chroot);
        for ($contentNum = 0; $contentNum < \count($result ['entries']); $contentNum++) {
            $result ['entries'] [$contentNum] ['mime_type'] =
            \GuzzleHttp\Psr7\MimeType::fromFilename(
                $result ['entries'] [$contentNum] ['name']
            ) ?: 'text/plain';

            $result['entries'] [$contentNum] ['cleanPath'] =
            DropboxHelper::makeCleanPath(
                $decodedChroot,
                $result['entries'] [$contentNum] ['path_lower'],
                $result['entries'] [$contentNum] ['.tag']
            );
        }
        return $result;
    }

    /**
     * Checks if the cache is up-to-date and if there are files that are not in the box anymore they are deleted
     */

    //TODO is this a part of the model? Is this not the same in an other model and twice in the software?
    public function maintainCache($cache_folder, $files_list)
    {
        $cache_folder = $cache_folder . DS . $this->dropbox->id . "_" . JFolder::makeSafe($this->dropbox->folder);

        if (JFolder::exists($cache_folder)) {
            $cached_files   = JFolder::files($cache_folder);
            $cached_folders = JFolder::folders($cache_folder);

            if (\is_array($cached_files)) {
                foreach ($cached_files as $cached_file) {
                    $found = false;
                    //echo $cached_file . "\n";
                    foreach ($files_list->contents as $db_file) {
                        if (
                            "DropboxCache-" . DropboxHelper::makeFilenameSafe(rawurlencode(JFile::getName($db_file->path))) == $cached_file
                            || "DropboxCacheHeader-" . DropboxHelper::makeFilenameSafe(rawurlencode(JFile::getName($db_file->path))) == $cached_file
                        ) {
                            $found = true;
                            continue;
                        }
                    }
                    if (!$found) {
                        Jfile::delete($cache_folder . $cached_file);
                    }
                }
            }

            if (\is_array($cached_folders)) {
                foreach ($cached_folders as $cached_folder) {
                    $found = false;

                    foreach ($files_list->contents as $db_file) {
                        if (
                            JFolder::makeSafe(rawurlencode(JFile::getName($db_file->path))) == $cached_folder
                            && $db_file->is_dir == 1
                        ) {
                            $found = true;
                            continue;
                        }
                    }
                    if (!$found) {
                        JFolder::delete($cache_folder . $cached_folder);
                    }
                }
            }
        }
    }
}
