<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access

\defined('_JEXEC') or die('Restricted access');

jimport('joomla.application.component.controller');
jimport('joomla.filesystem.folder');

require_once JPATH_COMPONENT . DS . 'helpers' . DS . 'dropbox.php';

// @codingStandardsIgnoreLine because of no namespace set
class DropboxController extends JControllerLegacy
{
    protected $model;
    protected $uri;
    protected $params;
    protected $sub_folder;
    protected $dest_folder;
    protected $box_type;
    protected $access_level;
    protected $_db;
    protected $id;

    /**
     *
     * @param array $config An optional associative array of configuration settings.
     * @param int   $id     dropbox Connection ID. Normally this is encoded in the URL.
     *                      But here we can pass an ID for easier UNIT testing
     */
    public function __construct($config = [], $id = null)
    {
        parent::__construct($config);
        jimport('joomla.filesystem.file');

        $this->_db = JFactory::getDBO();
        $this->uri = JUri::getInstance();

        // Check requirements
        //TODOc add all needed requirements, not just cURL
        if (!\extension_loaded('curl')) {
            //TODO use PHP exceptions.
            throw new Exception('500 ' . JText::_('CURL_EXTENSION_REQUIRED'));
            return;
        }
        if (!\function_exists('readfile')) {
            throw new Exception('500 ' . JText::_('COM_DROPBOX_READFILE_FUNCTION_REQUIRED'));
            return;
        }

        if ($id === null) {
            $this->id = (int)$this->input->get('id');
        } else {
            $this->id = (int) $id;
        }

        //what kind of box (provider) do we use?
        $query = ' SELECT box_type, access FROM #__dropbox ' .
                    '  WHERE id = ' . $this->id;
        $this->_db->setQuery($query);
        $result             = $this->_db->loadObject();
        $this->box_type     = $result->box_type;
        $this->access_level = $result->access;

        $user = JFactory::getUser();

        // check the access level
        $groups    = $user->getAuthorisedViewLevels();
        if (!\in_array($this->access_level, $groups)) {
            throw new Exception('403 ' . JText::_('JERROR_ALERTNOAUTHOR'));
            return;
        }

        //we can use different models to connect to other services
        if ($this->box_type === 'dropbox') {
            $this->model = $this->getModel('dropbox');
        } else {
            throw new Exception('404 ' . JText::_('COULD_NOT_FIND_BOX'));
        }

        $return = $this->model->setup($this->id);

        if (!empty($return)) {
            JFactory::getApplication()->enqueueMessage('0' . $return, 'warning');
        } else {
            $this->sub_folder = $this->input->get('sub_folder', null, 'STRING');

            $this->params = $this->model->getMergedParams();

            if ($this->params->get('private_user_directories', 0) > 0) {
                $user = JFactory::getUser();
                if ($user->guest == 1) {
                    $user_subdir = "guest";
                } else {
                    if ($this->params->get('private_user_directories', 0) == 2) {
                        $user_subdir = $user->username;
                    } else {
                        $user_subdir = (int)$user->id;
                    }
                }
                $this->model->addToChroot($user_subdir);
                //change folder into the changed chroot to check if it exists
                $this->model->changeFolder();
            }

            $chroot_info = $this->model->getMetaData();
            //if there is a file that has the same name like the user directory, we cannot do anything
            if (isset($chroot_info['.tag']) && $chroot_info['.tag'] === 'file') {
                throw new Exception('409 ' . JText::sprintf('COM_DROPBOX_USER_DIR_IS_FILE', $chroot_info->path));
            } elseif (isset($chroot_info ['error']) && $chroot_info ['error'] === "not_found") {
                // if there is no such file or directory create it
                $return = $this->model->createFolder();
            } elseif (isset($chroot_info ['error'])) {
                throw new Exception($chroot_info ['error']);
            }
            if (\array_key_exists('path_lower', $chroot_info)) {
                $this->model->setChroot($chroot_info['path_lower']);
            }
            //change into the folder that was requested
            $this->model->changeFolder($this->sub_folder);
        }
    }

    /**
     * Method to upload a file
     *
     * @access public
     */

    public function upload()
    {
        //TODOc loading bar
        //It's cool but it would be genious if we can have a "loading bar' to see how many times
        //I have to wait to finish my current upload :)
        $error_message = '';
        $dest_folder   = '';

        $app = JFactory::getApplication();

        if ($this->params->get('function_upload', 1) == 1) {
            if ($this->params->get('allow_subfolder_upload', 1) == 1) {
                $this->dest_folder = $this->input->get(
                    "dest_folder",
                    "",
                    'STRING'
                );
                $this->dest_folder = trim($this->dest_folder);
                $this->dest_folder = DropboxHelper::makeFoldernameSafe($this->dest_folder);

                if ($this->dest_folder != "") {
                    $destFolderInfo = $this->model->getMetaData($this->dest_folder);
                    //if there is a file that has the same name like the user directory, we cannot do anything
                    if (
                        isset($destFolderInfo['.tag'])
                        && $destFolderInfo['.tag'] === 'file'
                    ) {
                        $error_message .= JText::_('COULD_NOT_CREATE_SUBFOLDER') . "\\n";
                    } elseif (
                        isset($destFolderInfo['.tag'])
                        && $destFolderInfo['.tag'] === 'folder'
                    ) {
                        $this->model->changeFolder($this->dest_folder);
                    } else {
                        try {
                            $this->model->createFolder($this->dest_folder);
                        } catch (Exception $e) {
                            $error_message .= JText::_('COULD_NOT_CREATE_SUBFOLDER') .
                             " " . $e->getMessage() . "\\n";
                        }
                        $this->model->changeFolder($this->dest_folder);
                    }
                }
            } else {
                $this->dest_folder = '';
            }

            $file = $this->input->files->get('file', [], 'array');
            if ($this->sub_folder != "" && $this->sub_folder != "/") {
                $this->uri->setVar(
                    'sub_folder',
                    rawurlencode($this->sub_folder . "/" . $this->dest_folder)
                );
            } else {
                $this->uri->setVar(
                    'sub_folder',
                    rawurlencode($this->dest_folder)
                );
            }

            for ($uploaded_file_number = 0; $uploaded_file_number < \count($file); $uploaded_file_number++) {
                $file[$uploaded_file_number]['name'] = DropboxHelper::makeFilenameSafe($file[$uploaded_file_number]['name']);

                switch ((int)$this->params->get('add_timestamp_to_upload', 0)) {
                    case 1:
                        $file[$uploaded_file_number]['name'] = date("Ymd-His_") . $file[$uploaded_file_number]['name'];
                        break;
                    case 2:
                        $p = pathinfo($file[$uploaded_file_number]['name']);
                        if ($p['filename'] !== '') {
                            if (!isset($p['extension'])) {
                                $file[$uploaded_file_number]['name'] = $p['filename'] . date("_Ymd-His");
                            } else {
                                $file[$uploaded_file_number]['name'] = $p['filename'] . date("_Ymd-His") . "." . $p['extension'];
                            }
                        }
                        break;
                }

                // Rename uploaded file to reflect original name
                //do not use !== here some servers may have a string in the error variable
                if ($file[$uploaded_file_number]['error'] != UPLOAD_ERR_OK) {
                    $error_message .= JText::_('FILE_WAS_NOT_SUCCESSFULLY_UPLOADED') . "\\n";
                }

                $tmpDir = uniqid(JFactory::getConfig()->get('tmp_path') . DS . 'DropboxUploader-');

                //if (!mkdir($tmpDir))
                //      $error_message.= 'Cannot create temporary directory! Do you have write access to ' . JFactory::getConfig()->get('tmp_path') . ' ?'. "\\n";

                if (empty($file[$uploaded_file_number]['name'])) {
                    $error_message .= 'File name not supplied by the browser.' . "\\n";     //TODOc put in language file
                }
                $tmpFile = $tmpDir . DS . str_replace("/\0", '_', $file[$uploaded_file_number]['name']);

                if (!JFile::upload($file[$uploaded_file_number]['tmp_name'], $tmpFile)) {
                    //TODO put in language file
                    $error_message .= 'Cannot rename uploaded file!' .
                      ' Do you have write access to ' .
                      JFactory::getConfig()->get('tmp_path') .
                     ' ? Or is the file to big - check your PHP settings' .
                     "\\n";
                }

                if ($error_message === "") {
                    try {
                        $upload_result = $this->model->putFile($tmpFile);
                    } catch (Exception $e) {
                        $error_message .= 'Could not send file to dropbox\\n' . $e->getMessage();
                    }

                    // clean up again
                    if ($this->params->get('change_folder_after_upload', 1) != 1) {
                        $this->model->changeFolder($this->sub_folder . "/");
                        $dest_folder = '';
                    } else {
                        $dest_folder = $this->dest_folder;
                    }
                }
                JFile::delete($tmpFile);
                JFolder::delete($tmpDir);
            }
        } else {
            $error_message .= JText::_('COM_DROPBOX_NO_PERMISSION_TO_UPLOAD');
        }

        $document = JFactory::getDocument();
        $document->setMimeEncoding('text/html');
        $view = $this->getView('dropbox', 'raw');

        $notification_email_sender_address    = $this->params->get('notification_email_sender_address', "");
        $notification_email_recipient_address = $this->params->get('notification_email_recipient_address', "");

        if (!empty($notification_email_sender_address) && !empty($notification_email_recipient_address)) {
            $mailer = JFactory::getMailer();
            $mailer->setSender($notification_email_sender_address);
            $mailer->addRecipient($notification_email_recipient_address);
        }

        if ($error_message != '') {
            $view->setData('{"dest_folder": "' . $dest_folder . '", "message": "' . $error_message . '"}');

            if (!empty($notification_email_sender_address) && !empty($notification_email_recipient_address)) {
                $mailer->setSubject(JText::_('COM_DROPBOX_ERROR'));
                $mailer->setBody($dest_folder . "\n\n" . $error_message);
            }
        } else {
            $this->log('up', $this->model->getfolder() . trim(basename($tmpFile)));

            if (\count($file) > 1) {
                $message = JText::sprintf('COM_DROPBOX_FILES_SUCCESSFULLY_UPLOADED_TO_YOUR_DROPBOX', \count($file));
                $view->setData('{"dest_folder": "' . $dest_folder . '", "message":"' . $message . '"}');
            } else {
                $message = JText::_('COM_DROPBOX_FILE_SUCCESSFULLY_UPLOADED_TO_YOUR_DROPBOX');
                $view->setData('{"dest_folder": "' . $dest_folder . '", "message":"' . $message . '"}');
            }

            if (!empty($notification_email_sender_address) && !empty($notification_email_recipient_address)) {
                $mailer->setSubject($message);
                $mail_body = "";
                foreach ($file as $singleFile) {
                    //TODO dest_folder ist nicht der komplette pfad
                    $filename = $singleFile['name'];
                    $mail_body .= $dest_folder . "/" . DropboxHelper::makeFilenameSafe($filename) . "\n";
                }
                $mailer->setBody($mail_body);
            }
        }

        if (!empty($notification_email_sender_address) && !empty($notification_email_recipient_address)) {
            if (!$mailer->send()) {
                echo "error sending Email";
                die();
            }
        }
        $view->display();
    }

    /**
     * Method to download a file
     *
     * @access public
     */

    public function download()
    {
        $file_to_get  = $this->input->get('file', null, 'STRING');
        $fileMetaData = $this->model->getFile($file_to_get);

        JTable::addIncludePath(JPATH_COMPONENT . '/tables');
        $file_lock_table = JTable::getInstance('FileLocks', 'DropboxTable');

        if (
            isset($fileMetaData ['id'])
            && !isset($fileMetaData ['error'])
        ) {
            $cache_file = JFactory::getConfig()->get('tmp_path') . DS . "DropboxCache" .
            DS . 'DropboxCache-' . $fileMetaData ['id'];
            $this->log('down', $this->model->getfolder() . $file_to_get);

            header('Content-type: ' . $this->uri->getVar('mime_type'));
            if ($this->params->get('view_downloads_in_browser', 0) != 1) {
                header(
                    'Content-Disposition: attachment; filename="' .
                    DropboxHelper::makeFilenameSafe($file_to_get) . '"'
                );
            }
            ob_end_flush();
            readfile($cache_file);

            //delete the file_lock
            $fileLockId = $this->model->getFileLockID();
            if ($fileLockId === false) {
                throw new Exception("cannot find lock Id");
            }
            $file_lock_table->delete($fileLockId);
            $cache_dir_size = $this->getDirectorySize(\dirname($cache_file));

            // If the file is to big to be kept in cache or the cache max
            // size is reached
            // AND there are no other locks for this file, delete it
            if (
                (filesize($cache_file) > ($this->params->get(
                    'max_filesize_in_cache',
                    99999999
                ) * 1024)
                || $cache_dir_size ['size'] > ($this->params->get(
                    'max_cache_size',
                    99999999
                ) * 1024))
                && !$file_lock_table->getLockByHash($fileMetaData ['content_hash'])
            ) {
                JFile::delete($cache_file);
                //TODO the header file should also be deleted, but I don't like to calculate the
                //file name here again, we have to change the system.
            }
            die();
        } elseif (
            isset($fileMetaData ['error'])
            && $fileMetaData ['error'] === "not_found"
        ) {
            throw new Exception(JText::_('FILE_NOT_FOUND'));
        } elseif (isset($fileMetaData ['error'])) {
            throw new Exception($fileMetaData ['error']);
        }
        throw new Exception(JText::_('FILE_NOT_FOUND'));
    }

    /**
     * Method to download a Thumbnail
     *
     * @access public
     */

    public function downloadThubnail()
    {
        // get the document
        $document = JFactory::getDocument();

        //read the file
        $file_to_get = $this->input->get('file', null, 'STRING');

        $data = $this->model->getThumbnail($file_to_get, $this->params->get('th_size_pic', 'medium'));

        $isPreviewValid = getimagesizefromstring($data);
        if ($isPreviewValid !== false) {
            // set the MIME type
            $document->setMimeEncoding($this->uri->getVar('mime_type'));
        } else {
            $document->setMimeEncoding('text/plain');
        }
        $view = $this->getView('dropbox', 'raw');
        $view->setData($data);
        $view->display();
    }

    /**
     * Method to display the view.
     *
     * @param boolean $cachable  If true, the view output will be cached (IGNORED!)
     * @param array   $urlparams An array of safe url parameters and their variable types, for valid values see {@link JFilterInput::clean()}. (IGNORED!)
     *
     * @access public
     */
    public function display($cachable = false, $urlparams = false)
    {
        $view   = $this->getView('dropbox', 'html');
        $search = JFactory::getApplication()->input->get('search', '', 'get', 'STRING');
        $search = DropboxHelper::makeSearchSafe($search);

        if (!empty($search)) {
            $files_list = $this->model->search($search);
        } else {
            $files_list = $this->model->listFolder();
        }

        $files_pic = [];

        if (
            isset($files_list)
            && !isset($files_list["error"])
        ) {
            $uppercase_box_type = ucfirst($this->box_type);
            $cache_folder       = JFactory::getConfig()->get('tmp_path') . DS . $uppercase_box_type . "Cache" . DS;

            if (!JFolder::exists($cache_folder)) {
                JFolder::create($cache_folder);
            }

            $htaccess_file = $cache_folder . '.htaccess';

            if (!JFile::exists($htaccess_file)) {
                $htaccess_content = "<Files *>\norder deny,allow\ndeny from all\n</Files>";
                if (!JFile::write($htaccess_file, $htaccess_content)) {
                    $warningMsg =  '0 ' . JText::sprintf('COULD_NOT_WRITE_HTACCESS', $cache_folder);
                    JFactory::getApplication()->enqueueMessage($warningMsg, 'warning');
                }
            }

            $this->model->maintainCache($cache_folder, $files_list);

            if ($this->params->get('function_pic', 1) == 1) {
                foreach ($files_list["entries"] as $file) {
                    if ($file[".tag"] !== "file") {
                        continue;
                    }
                    if (
                        (\array_key_exists('media_info', $file)
                        && \array_key_exists("metadata", $file['media_info'])
                        && \array_key_exists('.tag', $file['media_info']['metadata'])
                        && $file['media_info']['metadata']['.tag'] === 'photo')
                        || \in_array(strtolower(JFile::getExt($file['name'])), ["jpg", "jpeg", "gif", "png"])
                    ) {
                        $files_pic[] = $file;
                    }
                }
            }

            $view->setup($this->model, $this->uri, $this->params, $files_list, $files_pic, $search);

            if ($this->params->get('change_folder_after_upload', 1) == 1) {
                $destination = "/" . $this->sub_folder . "/" . $this->dest_folder;
            } else {
                $destination = $this->sub_folder;
            }
            $view->changeFolder($destination);
            $view->display();
        } else {
            $warningMsg = '0 ' . JText::sprintf('COULD_NOT_CONNECT_TO_DROPBOX', $this->box_type, rawurldecode($this->model->getfolder()));
            JFactory::getApplication()->enqueueMessage($warningMsg, 'warning');
        }
    }

    public function log($direction, $file_to_get)
    {
        if ($direction != 'up') {
            $direction = 'down';
        }

        if ($this->params->get('log_' . $direction . 'loads', 0) == 1) {
            if ($this->model->getWriteLock() && $direction == 'down') {
                $cached = 1;
            } else {
                $cached = 0;
            }

            $user = JFactory::getUser();
            $file = trim(basename(rawurldecode($file_to_get)));
            $path = trim(substr($file_to_get, 0, \strlen($file_to_get) - \strlen($file)));

            $sql = "INSERT INTO `#__dropbox_logs` (
					`userId` ,
					`folder` ,
					`filename` ,
					`direction` ,
					`time` ,
					`dropboxId` ,
					`cached`
					)
					VALUES (";
            $sql .= (!$user->guest) ? (int) $user->id : 'NULL';
            $sql .= ",
					'" . $this->_db->escape($path) . "', 
					'" . $this->_db->escape($file) . "', 
					'" . $direction . "', 
					NOW( ), 
					'" . (int)$this->model->getID() . "', 
					'" . $cached . "'
					);";

            $this->_db->setQuery($sql);

            $this->_db->execute();
        }
    }
    /**
     *
     * @param string $path
     * @return array[string] containing: array['size'],  array['count'] and array['dircount']
     */
    public function getDirectorySize($path)
    {
        $totalsize  = 0;
        $totalcount = 0;
        $dircount   = 0;
        if ($handle = @opendir($path)) {
            while (false !== ($file = readdir($handle))) {
                $nextpath = $path . '/' . $file;
                if ($file != '.' && $file != '..' && !is_link($nextpath)) {
                    if (is_dir($nextpath)) {
                        $dircount++;
                        $result = $this->getDirectorySize($nextpath);
                        $totalsize += $result['size'];
                        $totalcount += $result['count'];
                        $dircount += $result['dircount'];
                    } elseif (is_file($nextpath)) {
                        $totalsize += filesize($nextpath);
                        $totalcount++;
                    }
                }
            }

            closedir($handle);
            $total['size']     = $totalsize;
            $total['count']    = $totalcount;
            $total['dircount'] = $dircount;
            return $total;
        }
        return 0;
    }
}
