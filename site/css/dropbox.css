/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link http://www.individual-it.net
 * @license    GNU/GPL
*/
div.dropbox_content {
    /* display:table; //keine ahnung warum diese eingenschaft hier steht. engt das ganze ein
    float: left;*/
}

div.dropbox_content div.name {
    display: table-cell;
    border-bottom: 1px solid #CCC;
    word-break: break-word;
}

div.dropbox_content div.modified_date {
    display: table-cell;
    padding-right: 20px;
    border-bottom: 1px solid #CCC;
}

div.dropbox_content div.size {
    display: table-cell;
    border-bottom: 1px solid #CCC;
}

div.dropbox_content div.upload_indicator {
    background-image: url(../enlargeit/loader.gif);
    background-repeat: no-repeat;
    display: none;
    width: 40px;
    height: 40px;
}

#dropbox_loading_indicator {
    background-image: url(../enlargeit/loader.gif);
    background-repeat: no-repeat;
    display: none;
    width: 40px;
    height: 40px;
    position: fixed;
    z-index:100;
}

div.dropbox_pic_wrapper a {
    background-image: url(../enlargeit/loader.gif);
    background-repeat: no-repeat;
    display: inline-block;
    padding-bottom: 5px;
    padding-right: 5px;
    background-position: center center;
    text-align: center;
    vertical-align: middle;
}

div.dropbox_pic_wrapper img {
    cursor: url("../enlargeit/pluscur.cur"), pointer;
}

div.dropbox_pic_clear {
    clear: both;
    float: none;
}
.file_listing_table {
    display: table;
    width: 100%;

}
.file_listing_header_row {
    height: 25px;
    display: table-row;
    width: 100%;
    color: #FFFFFF;
    font-weight: bold;
    background: #00214B;
    margin: 5px 0;
}

.file_listing_header_cell {
    display: table-cell;
    cursor: pointer;
    padding: 6px;
}

.file_listing_header_cell div.sorting_icon_asc {
    background-image: url(../icons/sort_asc.png);
    background-repeat: no-repeat;
    float: left;
    height: 10px;
    width: 14px;
    position: relative;
    top: 4px;
    margin-left: 5px;
}

.file_listing_header_cell div.sorting_icon_desc {
    background-image: url(../icons/sort_desc.png);
    background-repeat: no-repeat;
    float: left;
    height: 10px;
    width: 14px;
    position: relative;
    top: 4px;
    margin-left: 5px;
}

.file_listing_header_cell div.file_listing_header_cell_text {
    float: left;
}

.db_file_icon, .db_directory_up_icon {
    height: 25px;
    display: table-row;
}

.db_directory_up_icon a.directory_up {
    background-image: url(../icons/up.png);
    cursor: pointer;
}

.db_file_icon a, .db_directory_up_icon a  {
    background-image: url(../icons/unknown.png);
    background-repeat: no-repeat;
    background-position: 0px 0px;
    margin: 0px;
    padding-left: 25px;
    padding-top: 2px;
    padding-bottom: 5px;
    padding-right: 20px;
}

.db_file_icon a:hover {
    background-position: 0 0;
    background-repeat: no-repeat;
}

.db_file_icon a.directory {
    background-image: url(../icons/bb_fldr_fav_.png);
    padding-right: 20px;
    cursor: pointer;
    font-weight: bold;
}

.dropbox_content .db_file_icon div,
.dropbox_content .db_directory_up_icon div {
    padding: 6px; }


.db_file_icon a.odt,.db_file_icon a.sxw {
    background-image: url(../icons/002_text_document.png);
}

.db_file_icon a.doc,.db_file_icon  a.docx {
    background-image: url(../icons/doc.png);
}

.db_file_icon a.iso {
    background-image: url(../icons/cdimage.png);
}

.db_file_icon a.pps,.db_file_icon a.odp,.db_file_icon a.sxi,.db_file_icon a.ppsx
    {
    background-image: url(../icons/008_presentation_document.png);
}

.db_file_icon a.csv,.db_file_icon a.ods {
    background-image: url(../icons/004_spreadsheet_document.png);
}

.db_file_icon a.xls,.db_file_icon a.xlsx {
    background-image: url(../icons/spreadsheet.png);
}

.db_file_icon a.txt {
    background-image: url(../icons/txt.png);
}

.db_file_icon a.pdf {
    background-image: url(../icons/pdf.png);
}

.db_file_icon a.log {
    background-image: url(../icons/log.png);
}

.db_file_icon a.exe,.db_file_icon a.com,.db_file_icon a.bat {
    background-image: url(../icons/exec_wine.png);
}

.db_file_icon a.mpg,.db_file_icon a.avi {
    background-image: url(../icons/video.png);
}

.db_file_icon a.wav,.db_file_icon a.mp3 {
    background-image: url(../icons/sound.png);
}

.db_file_icon a.html,.db_file_icon a.htm,.db_file_icon a.xhtml,.db_file_icon a.php,.db_file_icon a.php4,.db_file_icon a.php5
    {
    background-image: url(../icons/gnome-mime-text-html.png);
}

.db_file_icon a.jpg,.db_file_icon a.gif,.db_file_icon a.png {
    background-image: url(../icons/gnome-mime-image.png);
}

.db_file_icon a.zip,.db_file_icon a.tar,.db_file_icon a.gz {
    background-image: url(../icons/package_frederic_moser_01.png);
}

#adminForm div.pane-sliders {
    float: right;
}

#adminForm div.col100 {
    width: 45%
}

#adminForm a.active {
    color: #000000;
    text-decoration: underline;
}

#adminForm th.action {
    text-align: right;
}

#adminForm th.sub_header {
    background-color: #6F6F6F;
    color: #FFFFFF;
    font-size: 110%;
}

input.dropbox_upload_des_folder {
    width: 350px;
}

button.dropbox_file_search {
    margin-bottom: 9px;
    margin-left: 10px;
}
