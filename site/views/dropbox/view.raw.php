<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// no direct access

\defined('_JEXEC') or die('Restricted access');

jimport('joomla.application.component.view');

// @codingStandardsIgnoreLine because of no namespace set
class DropboxViewDropbox extends JViewLegacy
{
    protected $data;
    public function display($tpl = null)
    {
        echo $this->data;
    }

    public function setData($data)
    {
        $this->data = $data;
    }
}
