<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// no direct access

\defined('_JEXEC') or die('Restricted access');

jimport('joomla.application.component.view');

// @codingStandardsIgnoreLine because of no namespace set
class DropboxViewDropbox extends JViewLegacy
{
    protected $model;
    protected $uri;
    protected $params;
    protected $sub_folder;
    protected $files_list;
    protected $files_pic;
    protected $search_query;

    public function setup($model, $uri, $params, $files_list = '', $files_pic = '', $search_query = '')
    {
        $this->model        = $model;
        $this->uri          = $uri;
        $this->params       = $params;
        $this->files_list   = $files_list;
        $this->files_pic    = $files_pic;
        $this->search_query = $search_query;
    }

    public function changeFolder($sub_folder)
    {
        if (substr($sub_folder, -1) == '/') {
            $this->sub_folder = substr($sub_folder, 0, -1);
        } else {
            $this->sub_folder = $sub_folder;
        }

        if (substr($this->sub_folder, 0, 2) == '//') {
            $this->sub_folder = substr($this->sub_folder, 1);
        }
    }

    public function display($tpl = null)
    {
        if ($this->sub_folder == '/') {
            $this->sub_folder = '';
        } elseif ($this->sub_folder != '') {
            if (substr($this->sub_folder, -1) == '/') {
                $this->sub_folder = substr($this->sub_folder, 0, -1);
            }

            $folders = explode("/", $this->sub_folder);

            $recent_folder = '';

            $folders_i = 0;
            while ($folders_i < \sizeof($folders) - 1) {
                if ($folders[$folders_i] != '/') {
                    $recent_folder .= $folders[$folders_i] . '/';
                }
                $folders_i++;
            }
            if ($recent_folder == "/") {
                $recent_folder = "";
            }
            $this->recent_folder = $recent_folder;
        }

        $document = JFactory::getDocument();
        //JHtml::_('bootstrap.framework');
        JHtml::_('jquery.framework');
        $document->addScript(JURI::root() . 'components/com_dropbox/js/tinysort.js');
        $document->addScript(JURI::root() . 'components/com_dropbox/js/jquery.tinysort.charorder.js');

        if ($document->getType() != "raw") {
            $document->addCustomTag('<link href="' . JURI::root() . 'components/com_dropbox/css/dropbox.css" rel="stylesheet" type="text/css" />');
            $raw = false;
        } else {
            $raw = true;
        }

        $this->raw                        = &$raw;
        $this->description_for_upload     = $this->params->get('description_for_upload', '');
        $this->th_per_row                 = $this->params->get('th_per_row', 4);
        $this->th_size_pic                = $this->params->get('th_size_pic', 'medium');
        $this->function_upload            = $this->params->get('function_upload', 1);
        $this->allow_subfolder_upload     = $this->params->get('allow_subfolder_upload', 1);
        $this->change_folder_after_upload = $this->params->get('change_folder_after_upload', 1);
        $this->function_list              = $this->params->get('function_list', 1);
        $this->function_pic               = $this->params->get('function_pic', 1);
        $this->show_breadcrumbs           = $this->params->get('show_breadcrumbs', 1);
        $this->show_search                = $this->params->get('show_search', 1);
        $this->list_sorting_field         = $this->params->get('list_sorting_field', 'name');
        $this->list_sorting_dir           = $this->params->get('list_sorting_dir', 'asc');
        $this->Id                         = $this->model->getID();
        $this->page_heading               = $this->params->get('page_heading', '1');
        $this->show_page_heading          = $this->params->get('show_page_heading', '1');
        $this->pageclass_sfx              = htmlspecialchars((string) ($this->params->get('pageclass_sfx')));

        $this->search_query = $this->search_query;

        $this->NewUri = &$this->uri;

        $this->sub_folder = &$this->sub_folder;

        $document->addScript(JURI::root() . 'components/com_dropbox/js/dropbox.js');

        if ($this->params->get('function_upload', 1) == 1) {
            $document->addScript(JURI::root() . 'components/com_dropbox/js/ajaxupload.js');
        }

        if ($this->params->get('function_list', 1) == 1) {
            $this->files_list = &$this->files_list;

            if ($this->params->get('list_show_modified_date', 1) == 1) {
                $this->date_format             = $this->params->get('date_format', 'd-m-Y H:i:s');
                $this->list_show_modified_date = "1";
                date_default_timezone_set($this->params->get('time_zone', 'Europe/Berlin'));
            }

            if ($this->params->get('list_show_size', 1) == 1) {
                $this->list_show_size = 1;
            }
        }

        if ($this->params->get('function_pic', 1) == 1) {
            $this->files_pic = &$this->files_pic;
            $document->addScript(JURI::root() . 'components/com_dropbox/enlargeit/enlargeit.js');

            $this->uri->setVar('sub_folder', rawurlencode($this->sub_folder));
            $this->uri->setVar('format', 'raw');
            $this->uri->setVar('task', 'download');

            $enl_brdsize = (int)$this->params->get('enl_brdsize', 5);
            if ($enl_brdsize >= 5 && $enl_brdsize <= 30) {
                $enl_brdsize = $enl_brdsize;
            } else {
                $enl_brdsize = 5;
            }

            $document->addScriptDeclaration(
                "var enl_gifpath='" . JURI::root() . "components/com_dropbox/enlargeit/';
											 enl_buttonurl[1] = 'prev';
											 enl_buttontxt[1] = '" . JText::_("BUTTON_PREVIOUS_PICTURE") . "';
											 enl_buttonoff[1] = -180;
											 enl_buttonurl[2] = 'next';
											 enl_buttontxt[2] = '" . JText::_("BUTTON_NEXT_PICTURE") . "';
											 enl_buttonoff[2] = -198;
											 enl_buttonurl[3] = 'close';
											 enl_buttontxt[3] = '" . JText::_("BUTTON_CLOSE") . "';
											 enl_buttonoff[3] = -126;
											 enl_buttonurl[0] = 'site:" . htmlspecialchars($this->uri->toString()) . "&amp;file=';
											 enl_buttontxt[0] = '" . JText::_("BUTTON_DOWNLOAD") . "';
											 enl_buttonoff[0] = '-90'; 
											 var enl_brdsize=" . $enl_brdsize . ";    // border thickness (5-30)
											 var enl_brdcolor='" . $this->params->get('enl_brdcolor', '') . "';
											 var enl_brdbck='" . $this->params->get('enl_brdbck', '') . "';     // border background pic, '' for no pic
											 var enl_brdround='" . $this->params->get('enl_brdround', 1) . "';    // use rounded borders
									 		 var enl_maxstep='" . $this->params->get('enl_maxstep', 10) . "';    // ani steps (10-30)
											 var enl_speed='" . $this->params->get('enl_speed', 1) . "';      // time between steps
											 var enl_ani='" . $this->params->get('enl_ani', 5) . "';         // 0=no,1=fade,2=glide,3=bumpglide,4=smoothglide,5=expglide
											 var enl_opaglide='" . $this->params->get('enl_opaglide', 1) . "';    // glide transparency
											 var enl_shadow='" . $this->params->get('enl_shadow', 1) . "';      // shadow under border
											 var enl_shadowsize='" . $this->params->get('enl_shadowsize', 10) . "';  // size of shadow right/bottom (0-20)
											 var enl_shadowcolor='" . $this->params->get('enl_shadowcolor', '') . "';// shadow color (empty: black)
											 var enl_shadowintens='" . $this->params->get('enl_shadowintens', 5) . "';// shadow intensity (5-30)
											 var enl_dark='" . $this->params->get('enl_dark', 1) . "';        // darken screen (0=off/1=on/2=keep dark when nav)
											 var enl_darkprct='" . $this->params->get('enl_darkprct', 50) . "';   // how dark the screen should be (0-100)
											 var enl_darksteps='" . $this->params->get('enl_darksteps', 20) . "';   // how long darkening should take
											 var enl_center='" . $this->params->get('enl_center', 1) . "';      // center enlarged pic on screen
											 var enl_drgdrop='" . $this->params->get('enl_drgdrop', 1) . "';     // enable drag + drop for pics
											 var enl_preload='" . $this->params->get('enl_preload', 1) . "';     // preload next/prev pic
											 var enl_titlebar='" . $this->params->get('enl_titlebar', 1) . "';    // show pic title bar
											 var enl_keynav='" . $this->params->get('enl_keynav', 1) . "';      // key navigation
											 var enl_wheelnav='" . $this->params->get('enl_wheelnav', 1) . "';    // mouse wheel navigation
											 var enl_titletxtcol='" . $this->params->get('enl_titletxtcol', '') . "';// color of title bar text (empty: dark grey)"
            );   // border color (white if empty) ");
        }

        parent::display($tpl);
    }
}
