<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access

\defined('_JEXEC') or die('Restricted access');

if (!$this->raw) {
    ?>
    <div class="item-page<?php echo $this->pageclass_sfx ?>">
    <?php if ($this->show_page_heading) : ?>
        <h1>
        <?php echo $this->escape($this->page_heading); ?>
        </h1>
    <?php endif; ?>
    <div class="dropbox_content" id="dropbox_wrapper_<?php echo $this->Id;?>">
    <?php
}

echo '<div id="dropbox_loading_indicator">';
echo '</div>';

if ($this->function_upload == 1) {
    $this->NewUri->setVar('task', 'upload');
    $this->NewUri->setVar('format', 'raw');

    echo "<script>
	jQuery(document).ready(function() {
			
		new AjaxUpload('dropbox_upload_field_" . $this->Id . "', 
								{action: '" . $this->NewUri->toString() . "',
								 name: 'file',	
								 onSubmit: function(file, extension) 
								 	{ 
								 		this.setData({'dest_folder': jQuery('#dropbox_dest_folder_" . $this->Id . "').val()});
								 		jQuery('#dropbox_upload_indicator_" . $this->Id . "').show();
								 		
								 	},										
								 onComplete: function(file, response) 
								 	{
								 		";

    $this->NewUri->delVar('sub_folder');

    $this->NewUri->delVar('mime_type');
    $this->NewUri->delVar('file');
    $this->NewUri->delVar('task');

    echo "							try
										{	
											var response_obj = jQuery.parseJSON(response);
											change_dropbox_dir('" . $this->NewUri->toString() . "&sub_folder=";

    if ($this->sub_folder != "") {
        echo $this->sub_folder . "/";
    }
    echo "								'+response_obj.dest_folder,$this->Id)
								 			jQuery('#dropbox_upload_indicator_" . $this->Id . "').hide();
								 		
								 			//jQuery('#dropbox_upload_field_" . $this->Id . "').hide();
								 			alert(response_obj.message);

										}
										catch (e)
										{ 
											alert('JAVASCRIPT exeption:\\n\\nERROR:\\n'+e);		

								 		}
								 	}
								});
	 });
	 
	</script>";

    echo'<form method="post" action="" enctype="multipart/form-data">
        <dl>';

    if ($this->allow_subfolder_upload == 1) {
        echo '<dt>' . JText::_('DESTINATION_DIRECTORY') . '</dt>
					<dd>
						<input type="text" id="dropbox_dest_folder_' . $this->Id . '" class="dropbox_upload_des_folder" name="dest_folder" placeholder="' . JText::_('DESTINATION_DIRECTORY_DESCRIPTION') . '"/> 
					</dd>';
    }

    echo '<dt>' . JText::_('FILE') . '</dt><dd><input type="file" id="dropbox_upload_field_' . $this->Id . '" name="file" /></dd>
            <dd><div class="upload_indicator" id="dropbox_upload_indicator_' . $this->Id . '"></div></dd>
          <dt>' . $this->description_for_upload . '</dt>
        </dl>
        </form>';
}

//TODO make the search function disableble
$this->NewUri->setVar('format', 'raw');
$this->NewUri->setVar('sub_folder', $this->sub_folder);
$this->NewUri->delVar('task');
$this->NewUri->delVar('search');

echo "<script>
		
		jQuery(document).ready(function() {
			jQuery('#dropbox_file_search_" . $this->Id . "' ).change(function() {
				var dropbox_search_url='" . $this->NewUri->toString() . "';
				search_dropbox_file(jQuery(this),dropbox_search_url); 
			});
		});
	</script>";

if ($this->show_search == 1) {
    echo '<input type="text" name="dropbox_file_search_' . $this->Id . '" id="dropbox_file_search_' . $this->Id . '" 
			value="' . $this->search_query . '"
			class="dropbox_file_search" placeholder="' . JText::_("COM_DROPBOX_SEARCH") . '"/>
			<button type="submit" class="btn btn-primary dropbox_file_search">' . JText::_("COM_DROPBOX_SEARCH") . '</button>';

    if ($this->search_query != "") {
        echo '<button type="reset" class="btn btn-primary dropbox_file_search" onclick="search_dropbox_file(jQuery(this),\'' . $this->NewUri->toString() . '\');">' . JText::_("COM_DROPBOX_SEARCH_RESET") . '</button>';
    }
}

if ($this->function_pic == 1) {
    $this->NewUri->setVar('format', 'raw');

    $dropbox_picture_num = 0;

    echo "<div class='dropbox_pic_wrapper'>";

    foreach ($this->files_pic as $file) {
        $this->NewUri->setVar('mime_type', $file["mime_type"]);

        $this->NewUri->setVar('sub_folder', rawurlencode($file["cleanPath"]));

        $this->NewUri->setVar('file', rawurlencode($file["name"]));

        echo '<a href="images/fullsize_file.jpg" target="_blank" style="';

        switch ($this->th_size_pic) {
            case 'thumb':
                echo "width: 32px; height:32px;";
                break;
            case 'small':
                echo "width: 64px; height:64px;";
                break;
            case 'medium':
                echo "width: 128px; height:128px;";
                break;
            case 'large':
                echo "width: 640px; height:480px;";
                break;
            case 'huge':
                echo "width: 1024px; height:768px;";
                break;
        }

        $this->NewUri->setVar('task', 'downloadThubnail');
        echo '" onclick="return false;">
			  <img class="dropbox_pictures_' . $this->Id . '" id="dropbox_pictures_' . $this->Id . '_' . $dropbox_picture_num . '"' .
        ' name="' . rawurlencode($file["name"]) . '"' .
        ' src="' . htmlspecialchars($this->NewUri->toString()) . '"';

        echo ' onclick="enlarge(this);"
				';

        $this->NewUri->setVar('task', 'download');

        echo    ' longdesc="' . htmlspecialchars($this->NewUri->toString()) . '" /></a>';

        $this->NewUri->setVar('task', 'downloadThubnail');

        echo '<script>
					jQuery("#dropbox_pictures_' . $this->Id . '_' . $dropbox_picture_num . '").on("load",function() {
                        if(' . $this->params->get('enl_titlebar', 1) . ' === 1){
                            jQuery("#dropbox_pictures_' . $this->Id . '_' . $dropbox_picture_num . '").attr("alt","' . Jfile::stripExt($file["name"]) . '");
                        }
				  		jQuery("#dropbox_pictures_' . $this->Id . '_' . $dropbox_picture_num . '").parent().css("background-image","none");

					});

				</script>';

        $dropbox_picture_num++;

        if ((int)$this->th_per_row > 0) {
            if ($dropbox_picture_num % (int)$this->th_per_row == 0) {
                echo "<div class='dropbox_pic_clear'></div>";
            }
        }
    }

    echo "</div>";
}

if ($this->function_list == 1 || $this->function_list == 2) { //we have to list everything (1) or just the folders (2)
    echo "<script>
				  			
				jQuery(document).ready(function() {
						jQuery.tinysort.defaults.attr='sorting';
						jQuery.tinysort.defaults.charOrder='" . JText::_("COM_DROPBOX_CHAR_ORDER") . "';
						com_dropbox_sort_table(\"" . $this->Id . "\",'" . $this->list_sorting_field . $this->Id . "','" . $this->list_sorting_dir . "');
				});
		  </script>";

    //

    $folders = explode('/', $this->sub_folder);
    if ($this->show_breadcrumbs == 1) {
        echo "<h1>";
        echo JText::_("YOU_ARE_HERE") . " " . JText::_("DROPBOX_ROOT");
        $urlvalue='';
        foreach ($folders as $folder) {
            if($folder !== '') {
                $urlvalue .= '%2F' . $folder;
                echo "/<a class='directory_up' onclick='change_dropbox_dir(\"" . htmlspecialchars(explode('sub_folder=', $this->NewUri->toString())[0] . "sub_folder=$urlvalue%2F") . "\",$this->Id)'>" . JText::_($folder) . "</a>";
            }
        }
        echo "</h1>";
    }

    echo "<div  class='file_listing_table'>
		   <div  class='file_listing_header_row'>
			<div class='file_listing_header_cell name" . $this->Id . "' onclick='com_dropbox_sort_table(\"" . $this->Id . "\",\"name" . $this->Id . "\");'>
				<div class='file_listing_header_cell_text'>" . JText::_("COM_DROPBOX_FILE_NAME") . "</div>
				<div class='sorting_icon_asc'></div>
				<div class='sorting_icon_desc'></div>
			</div>";
    if (isset($this->list_show_modified_date)) {
        echo "<div class='file_listing_header_cell modified_date" . $this->Id . "' onclick='com_dropbox_sort_table(\"" . $this->Id . "\",\"modified_date" . $this->Id . "\");'>
					<div class='file_listing_header_cell_text'>" . JText::_("COM_DROPBOX_FILE_MODIFIED_DATE") . "</div>
					<div class='sorting_icon_asc'></div>
					<div class='sorting_icon_desc'></div>
				</div>";
    }

    if (isset($this->list_show_size)) {
        echo "<div class='file_listing_header_cell size" . $this->Id . "' onclick='com_dropbox_sort_table(\"" . $this->Id . "\",\"size" . $this->Id . "\");'>
					<div class='file_listing_header_cell_text'>" . JText::_("COM_DROPBOX_FILE_SIZE") . "</div>
					<div class='sorting_icon_asc'></div>
					<div class='sorting_icon_desc'></div>
				</div>";
    }

    echo "</div>";

    if ($this->sub_folder != '' && $this->sub_folder != '/') {
        $this->NewUri->setVar('sub_folder', rawurlencode($this->recent_folder));

        $this->NewUri->delVar('mime_type');
        $this->NewUri->delVar('file');
        $this->NewUri->delVar('task');

        echo "<div class='db_directory_up_icon'>
					<div class='name name" . $this->Id . "'>
						<a class='directory_up' onclick='change_dropbox_dir(\"" . htmlspecialchars($this->NewUri->toString()) . "\",$this->Id)'>" . JText::_("UP") . "</a>
  					</div>";

        if (isset($this->list_show_modified_date)) {
            echo "<div class='modified_date modified_date" . $this->Id . "' ></div>";
        }

        if (isset($this->list_show_size)) {
            echo "<div class='size size" . $this->Id . "'>" . JText::_("DIR") . "</div>";
        }

        echo "</div>";
    }

    if (\sizeof($this->files_list["entries"]) <= 0) {
        if ($this->search_query != "") {
            echo "<div class='error'>" . JText::_("COM_DROPBOX_EMPTY_SEARCH_RESULT") . "</div>";
        } else {
            echo "<div class='error'>" . JText::_("FOLDER_EMPTY") . "</div>";
        }
    } else {
        //         echo "<pre>";
        //         print_r($this->files_list->contents);
        //         echo "</pre>";
        foreach ($this->files_list["entries"] as $content) {
            $this->NewUri->delVar('mime_type');
            $this->NewUri->delVar('sub_folder');
            //$this->NewUri->delVar( 'format');
            $this->NewUri->delVar('file');
            $this->NewUri->delVar('task');
            $this->NewUri->delVar('search');
            $this->NewUri->setVar('format', 'raw');
            //TODO we need to strip away the chroot here
            $this->NewUri->setVar('sub_folder', rawurlencode($content["cleanPath"]));
            if ($content['.tag'] === 'file') {
                if ($this->function_list == 2) {
                    continue;
                }

                $this->NewUri->setVar('task', 'download');

                //TODO guess mime_type, we do not get the mime_type for free from dropbox, so we need to get it somehow
                $this->NewUri->setVar('mime_type', $content["mime_type"]);

                $this->NewUri->setVar('file', rawurlencode($content["name"]));

                echo "<div class='db_file_icon'>
						<div
							class='name name" . $this->Id . "'
							sorting='" . htmlspecialchars($content["name"]) . "'>
							<a 
								class='" . strtolower(Jfile::getExt($content["name"])) . "' 
								href='" . htmlspecialchars($this->NewUri->toString()) . "'>" .
                 htmlspecialchars($content["name"]) . "
							 </a>
						</div>";

                if (isset($this->list_show_modified_date)) {
                    $modified = strtotime($content['server_modified']);
                    echo "<div class='modified_date modified_date" . $this->Id .
                    "' sorting='" . $modified . "'>" .
                    date($this->date_format, $modified) . "</div>";
                }

                if (isset($this->list_show_size)) {
                    echo "<div class='size size" . $this->Id .
                    "' sorting='" . $content['size'] . "'>" .
                    DropboxHelper::humanFileSize($content['size']) .
                    "</div>";
                }

                echo "</div>";
            } else {
                echo "<div class='db_file_icon'><div class='name name" .
                $this->Id . "' sorting='" .
                htmlspecialchars($content ['name']) .
                "'>
					<a class='directory' onclick='change_dropbox_dir(\"" .
                htmlspecialchars($this->NewUri->toString()) .
                "\",$this->Id)'>" .
                htmlspecialchars($content ['name']) .
                "</a></div>";

                if (isset($this->list_show_modified_date)) {
                    echo "<div class='modified_date modified_date" .
                      $this->Id . "' sorting='0000-00-00'>&nbsp;</div>";
                }

                if (isset($this->list_show_size)) {
                    echo "<div class='size size" . $this->Id . "' sorting='0'>" .
                    JText::_("DIR") . "</div>";
                }

                echo "</div>";
            }
        }
    }
}

// we need this for the joomla login module, otherwise the module will add
// the existing query parameters at the end of the default redirection page
$this->NewUri->delVar('format');
$this->NewUri->delVar('task');
$this->NewUri->delVar('sub_folder');
$this->NewUri->delVar('file');
$this->NewUri->delVar('mime_type');
$this->NewUri->delVar('search');

if (!$this->raw) {
    ?>
        </div>
    </div>
    <?php
}
?>
