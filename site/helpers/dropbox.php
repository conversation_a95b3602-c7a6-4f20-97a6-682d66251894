<?php

enum PathType: string
{
    case FILE   = 'file';
    case FOLDER = 'folder';
    case SEARCH = 'search';

    // Method to get forbidden characters pattern based on type
    public function getForbiddenPattern(): string
    {
        return match ($this) {
            self::FOLDER => '#[:\#\*"@+=;!&%<>\]\'\\\\|\[]#',
            self::FILE, self::SEARCH => '#[:\#\*"@+=;!&%<>\]\/\'\\\\|\[]#'
        };
    }

    // Method to check if type should use dashes
    public function shouldUseDashes(): bool
    {
        return $this !== self::SEARCH;
    }

    // Convert string to enum safely
    public static function fromString(string $value): self
    {
        return self::tryFrom($value) ?? throw new InvalidArgumentException("Invalid path type: $value");
    }
}

// TRAIT for sanitizing and normalizing file and folder name strings
trait FileFolderNameProcessingTrait
{
    private function normalizeWhitespace(string $str): string
    {
        // Replace double byte whitespaces to single byte
        $str = preg_replace('/\xE3\x80\x80/', ' ', $str);
        // Replace any multiple whitespace with a single white space
        return preg_replace('/\s+/', ' ', $str);
    }

    private function sanitizeDotsInPath(string $str): string
    {
        return preg_replace([
            '/\/\.+\//',    // dots between slashes
            '/^\.+\//',     // dot at start with slash
            '/\/\.+$/',     // dots at end with slash
            '/(\.)\1+/',    // duplicate dots
            '/^\.$/',        // single dot
        ], [
            "/\x20/",
            "\x20/",
            "/\x20",
            "\x20",
            "\x20",
        ], $str);
    }

    private function removeDuplicateSlashes(string $str): string
    {
        return preg_replace('/(\/)\1+/', '/', $str);
    }
}

// @codingStandardsIgnoreLine because of no namespace set
class DropboxHelper
{
    use FileFolderNameProcessingTrait;

    /**
     * helper function to use from makeFilenameSafe and makeFoldernameSafe because both are so similar
     *
     * @param  string   $string folder or file name
     * @param  PathType $type   file|folder|search
     * @return string
     */
    protected static function makeFileFoldernameSafe(string $string, PathType $type = PathType::FILE): string
    {
        // Use trait method for whitespace normalization
        $str = (new self())->normalizeWhitespace($string);

        if ($type->shouldUseDashes()) {
            // remove any '-' from the string as they will be used as concatenator.
            $str = str_replace('-', ' ', $str);
        }

        //delete anything that looks like HTML or PHP
        $str = strip_tags($str);

        //replace forbidden characters by whitespaces
        $str = preg_replace($type->getForbiddenPattern(), "-", $str);

        //delete all '?'
        $str = str_replace('?', '', $str);

        //trim white spaces at beginning and end of alias
        $str = trim($str);

        // Process dots
        $str = (new self())->sanitizeDotsInPath($str);

        // Remove any duplicate slashes
        $str = (new self())->removeDuplicateSlashes($str);

        // Normalize whitespace: preserve spaces for SEARCH, replace spaces with hyphens for FILE/FOLDER
        return match ($type) {
            PathType::SEARCH => trim(preg_replace('#\x20+#', ' ', $str)),
            PathType::FILE, PathType::FOLDER => preg_replace('#\x20+#', '-', $str)
        };
    }

    /**
     * alternative for JFile::makeSafe that supports non ASCII filenames
     * taken from http://forum.joomla.org/viewtopic.php?p=2075458
     *
     * @param  string $filename
     * @return string
     */

    public static function makeFilenameSafe(string $filename): string
    {
        return self::makeFileFoldernameSafe($filename, PathType::FILE);
    }

    /**
     * alternative for JFolder::makeSafe that supports non ASCII filenames
     * taken from http://forum.joomla.org/viewtopic.php?p=2075458
     *
     * @param  string $foldername
     * @return string
     */

    public static function makeFoldernameSafe(string $foldername): string
    {
        return self::makeFileFoldernameSafe($foldername, PathType::FOLDER);
    }

    /**
     * makes the search query safe
     *
     * @param  string $query
     * @return string
     */
    public static function makeSearchSafe(string $query): string
    {
        return self::makeFileFoldernameSafe($query, PathType::SEARCH);
    }

    /**
     * returns "cleanPath" path without the root of the dropbox connection and the file name
     *
     * @param  string          $root (unencoded)
     * @param  string          $path from dropboxAPI
     * @param  PathType|string $type file|folder
     * @throws Exception
     * @return string
     */
    public static function makeCleanPath(string $root, string $path, PathType|string $type): string
    {
        // Convert string to enum if needed
        if (\is_string($type)) {
            $type = PathType::fromString($type);
        }

        if (substr($path, 0, 1) !== '/') {
            throw new InvalidArgumentException(
                "invalid path (no leading /)"
            );
        }

        if (preg_match("/\/{2,}/", $path) > 0) {
            throw new InvalidArgumentException(
                "invalid path (multiple /)"
            );
        }

        //we need to make root always start with a "/"
        if (substr($root, 0, 1) !== '/') {
            $root = "/" . $root;
        }
        //get rid of multiple /
        $root = preg_replace("/\/+/", "/", $root);

        $pos = strpos($path, $root);
        if ($pos !== 0) {
            throw new Exception("cannot find root in path");
        }
        // get rid of the chroot part of the path
        $cleanPath = substr_replace($path, "", $pos, \strlen($root));

        // only get the path, not the file name
        $cleanPath = match ($type) {
            PathType::FILE   => \dirname($cleanPath),
            PathType::FOLDER => $cleanPath
        };

        if ($cleanPath === ".") {
            $cleanPath = "";
        }

        if (substr($cleanPath, 0, 1) !== '/' && $cleanPath !== "") {
            $cleanPath = "/" . $cleanPath;
        }

        if (substr($cleanPath, - 1) === '/' && $cleanPath !== "") {
            $cleanPath = substr($cleanPath, 0, (\strlen($cleanPath) - 1));
        }

        return $cleanPath;
    }

    public static function humanFileSize(int|float $size, string $unit = ""): string
    {
        static $units = [
            'GB' => 1 << 30,
            'MB' => 1 << 20,
            'KB' => 1 << 10,
        ];

        return match (true) {
            ((!$unit && $size >= $units['GB']) || $unit === "GB") => number_format($size / $units['GB'], 2) . "GB",
            ((!$unit && $size >= $units['MB']) || $unit === "MB") => number_format($size / $units['MB'], 2) . "MB",
            ((!$unit && $size >= $units['KB']) || $unit === "KB") => number_format($size / $units['KB'], 2) . "KB",
            default                                               => number_format($size) . " bytes"
        };
    }
}
