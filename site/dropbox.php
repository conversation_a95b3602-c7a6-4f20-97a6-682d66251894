<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access
\defined('_JEXEC') or die('Restricted access');

// import joomla controller library
jimport('joomla.application.component.controller');

if (!\defined('DS')) {
    \define('DS', DIRECTORY_SEPARATOR);
}

// Get an instance of the controller prefixed by Dropbox
$controller = JControllerLegacy::getInstance('Dropbox');

$jinput   = JFactory::getApplication()->input;

// Perform the Request task
$controller->execute($jinput->getCmd('task'));

// Redirect if set by the controller
$controller->redirect();
