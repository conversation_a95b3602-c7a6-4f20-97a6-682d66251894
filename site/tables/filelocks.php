<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access
\defined('_JEXEC') or die('Restricted access');

// import Joom<PERSON> table library
jimport('joomla.database.table');

/**
 * Dropbox Table class
 *
 * @package    Joomla.Dropbox
 * @subpackage Components
 */
// @codingStandardsIgnoreLine because of no namespace set
class DropboxTableFileLocks extends JTable
{
    /**
     * Primary Key
     *
     * @var int
     */
    public $id = null;

    /**
     * @var string
     */
    public $file_name_hash = null;

    /**
     * @var string
     */
    public $lock_type = null;

    /**
     * @var int
     */
    public $time = null;

    /**
     * Constructor
     *
     * @param object Database connector object
     */
    public function __construct(&$db)
    {
        parent::__construct('#__dropbox_file_locks', 'id', $db);
    }

    public function deleteOldLocks()
    {
        $config    = JFactory::getConfig();
        $cachePath = $config->get('tmp_path') . DS . "DropboxCache" . DS;

        $max_lock_age = \ini_get('max_execution_time');
        if ($max_lock_age == 0) {
            //if its realy 0, delete all locks that are older than one day
            $max_lock_age = 86400;
        } else {
            $max_lock_age = $max_lock_age * 2;
        }

        $q = "DELETE FROM #__dropbox_file_locks WHERE time < " . (time() - $max_lock_age);
        $this->_db->setQuery($q);
        $this->_db->execute();

        //check if there are locks for non-existing files
        //if yes delete the lock
        $q = "SELECT id, file_name_hash FROM #__dropbox_file_locks";
        $this->_db->setQuery($q);
        $result = $this->_db->loadAssocList();
        foreach ($result as $file) {
            $dataFile   = $cachePath . "DropboxCache-" . $file['file_name_hash'];
            $headerFile = $cachePath . "DropboxCacheHeader-" . $file['file_name_hash'];

            if (
                !file_exists($dataFile)
                || !file_exists($headerFile)
            ) {
                $this->delete($file['id']);
            }
        }
    }

    /**
     * Checks if any locks exists for the given file
     *
     * @param  string         $hash the sha1 hash
     * @return string|boolean "read" or "write" if a lock exists, false if no lock exists
     */
    public function getLockByHash($hash)
    {
        $q = "SELECT count(*) as count FROM #__dropbox_file_locks WHERE `lock_type`='r' AND `file_name_hash`='" . $hash . "'";
        $this->_db->setQuery($q);
        $result = $this->_db->loadRow();
        if ($result[0] > 0) {
            return "read";
        }
        $q = "SELECT count(*) as count FROM #__dropbox_file_locks WHERE `lock_type`='w' AND `file_name_hash`='" . $hash . "'";
        $this->_db->setQuery($q);
        $result = $this->_db->loadRow();
        if ($result[0] > 0) {
            return "write";
        }

        return false;
    }
}
