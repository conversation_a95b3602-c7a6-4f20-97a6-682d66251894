# Created by https://www.gitignore.io/api/eclipse

### Eclipse ###

.metadata
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders


# Eclipse Core
.project

# External tool builders
.externalToolBuilders/

# Locally stored "Eclipse launch configurations"
*.launch

# PyDev specific (Python IDE for Eclipse)
*.pydevproject

# CDT-specific (C/C++ Development Tooling)
.cproject

# JDT-specific (Eclipse Java Development Tools)
.classpath

# Java annotation processor (APT)
.factorypath

# PDT-specific (PHP Development Tools)
.buildpath

# sbteclipse plugin
.target

# Tern plugin
.tern-project

# TeXlipse plugin
.texlipse

# STS (Spring Tool Suite)
.springBeans

# Code Recommenders
.recommenders/

tests/chromedriver

*.jar

tests/sc-*/

# Node modules (tests)
tests/e2e/node_modules
tests/e2e/.env

# Composer Packages
tests/vendor
site/vendor

# IDE caches
.idea
.vscode
.package-generator

# docker
docker-compose.override.yml

# test artifacts
tests/e2e/reports

# build assets
build/assets
