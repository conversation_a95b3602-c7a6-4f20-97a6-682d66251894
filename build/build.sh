#!/usr/bin/env bash

# The script makes use of the three environment variables:
# 1. VERSION: The version string of the release. e.g. 1.0.0
# 2. PRE_RELEASE: If set to 'true', the release will be marked as a pre-release.
# 3. LATEST: If set to 'true', the release will be marked as the latest release.

# gh, zip and make are required
if ! command -v gh &> /dev/null
then
	echo "'gh' could not be found. Please install it." && exit
fi

if ! command -v zip &> /dev/null
then
	echo "'zip' could not be found. Please install it." && exit
fi

if ! command -v make &> /dev/null
then
	echo "'make' could not be found. Please install it." && exit
fi


if [ -z "$VERSION" ]; then
	echo "Version string 'x.x.x' is required."
	exit 1
fi

build_zip="build/assets/com_dropbox_$VERSION.zip"
release_notes_file="build/release_notes.txt"
version_string="v$VERSION"

# move to the root directory
cd "$(dirname "$0")/.." || exit

# make sure the assets directory exists
mkdir -p build/assets
# remove the old zip file if it exists
rm -rf "$build_zip"

# prepare vendors for the production build
make clean
make install-prod

# build the zip file: com_dropbox.x.x.x.zip & dropbox_unzip_first.zip
zip -r "$build_zip" site language plugin admin script.php dropbox.xml

# skip creating a release if the build is for test or development
if [ "$SKIP_RELEASE" = "true" ];then
	exit 0
fi

# create a new github release
if [ "$PRE_RELEASE" = "true" ]; then
	gh release create "$version_string" "$build_zip" -t "$version_string" --prerelease -F "$release_notes_file"
elif [ "$LATEST" = "true" ]; then
	gh release create "$version_string" "$build_zip" -t "$version_string" -F "$release_notes_file" --latest
else
	gh release create "$version_string" "$build_zip" -t "$version_string" -F "$release_notes_file"
fi
