3.0 -> 3.1.ALPHA.1
- fixing code towards the strict standards
- file size and modification date can be hidden

3.1.ALPHA.1 -> 3.1.ALPHA.2
- style changes

3.1.ALPHA.2 -> 3.1.BETA.1
- bugfixes

3.1 -> 3.2.BETA.1
- search function
- complete PHP7 compatibility
- Email notification for uploads

5.1.1 -> 6.0.0
- support for php8.1
- removing support for Joomla < 4.4
- fix css styling in admin site
- revert support for renewing access token after token expires

6.0.0 -> 6.1.0
- Shift submenu from admin page to joomla menu bar
- fixed file name while adding timestamp to suffix while uploading
- Use mouse wheel to navigate picture on sites
- Add validation rule for folder
- fixed not showing rounded border when use rounded borders (Mozilla/Safari only) was set to Yes by 
- adding feature to navigate using breadcrums
- fix issue where changing 'center enlarged pic on screen' to 'No' would do nothing
- Set default value on picture functions

6.1.0 -> 6.1.1
- Fix: Resolved PHP deprecated warning
