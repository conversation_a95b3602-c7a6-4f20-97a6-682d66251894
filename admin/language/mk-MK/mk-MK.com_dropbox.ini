;@package    Joomla.Dropbox

;@subpackage Components

;@link http://www.individual-it.net

;@license    GNU/GPL

;Backend file

; Note : All ini files need to be saved as UTF-8- No BOM

;translated by <PERSON><PERSON><PERSON><PERSON> <PERSON> b<PERSON><PERSON>@yahoo.com 

FOLDER="Папка"
STATUS="Статус"
ACTION="Акција"
CONNECTED="Поврзано"
NOT_CONNECTED="Не е поврзано"
DELETE_CONNECTION="Избриете поврзување"
CONNECT="Поврзи"
PARAMETERS_FOR_PIC_FUNCTION="Процедура за слики"
PARAMETERS_FOR_LIST_FUNCTION="Процедура за листи"
PARAMETERS_FOR_UPLOAD_FUNCTION="Процедура за додавање"
DROPBOX_DISCONNECTED="Dropbox е откажан"
DROPBOX_SAVED="Dropbox е запомнет"
ERROR_SAVING_DROPBOX="Грешка при запомнување во Dropbox"
DROPBOXES_DELETED="Dropbox - избришано"
ERROR_DELETING_DROPBOXES="Грешка: Еден или повеќе Dropboxes не се избришани"
CANCELLED="Откажано"
PARAMETERS="Параметри"
OVERRIDES_DESCRIPTION="Зададени вредности во однос на мени и другите параметри на компонентата"
FILENAME="Име на документот"
DIRECTION="Насока"
CACHED="Кеширано"
TOTAL="Вкупно"
COUNT="Преброено"
GROUP_BY="Group"
ORDER_BY="Редослед"
GUEST="гост"
LOG_DOWNLOADS="Log на превземања"
LOG_DOWNLOADS_DESCR="Log на секое превземање во базата на податоци"
LOG_UPLOADS="Log на додавања"
LOG_UPLOADS_DESCR="Log на секое додавање во базата на податоци"
VIEW_PICTURES="Видете ги сликите"
USE_HTTPS="Користи HTTPS?"
USE_HTTPS_DESCR="HTTPS е посигурен на ако имате проблеми, обидете се без него?"
BOX_TYPE="Box тип"
GENERAL_SETTINGS_LABEL="Општи местењА"
USERNAME="Sugarsync корисничко име"
PASSWORD="Sugarsync лозинка"
USERNAME_PASSWORD_DESCRIPTION="Ова ви е потребно само во случај да го користите www.sugarsync.com. АКО ГО КОРИСТИТЕ www.dropbox.com НИКАКО НЕ ГИ СТАВАЈТЕ ВАШЕТО КОРИСНИЧКО ИМЕ НИТУ ЛОЗИНКАТА!"
COM_DROPBOX_DROPBOX_DETAILS="Детално"
COM_DROPBOX_ID="Id"
COM_DROPBOX_CONFIGURATION="Configuration for all Boxes"
COM_DROPBOX_YES="Yes"
COM_DROPBOX_NO="No"
COM_DROPBOX_LIST_FILES="List files"
COM_DROPBOX_LIST_FILES_DESC="list the files?"
COM_DROPBOX_LIST_JUST_FOLDERS="List just folders"
COM_DROPBOX_UPLOAD="Upload"
COM_DROPBOX_UPLOAD_DESC="allow to upload files?"
COM_DROPBOX_VIEW_PICTURES_DESC="view pictures with EnlargeIt?"
COM_DROPBOX_SHOW_BREADCRUMBS="Show breadcrumbs"
COM_DROPBOX_SHOW_BREADCRUMBS_DESC="Show breadcrumbs e.g. 'You are here:  DROPBOX/files'"
COM_DROPBOX_CHANGE_FOLDER_AFTER_UPLOAD="Change folder after upload"
COM_DROPBOX_ALLOW_UPLOAD_INTO_SUBFOLDERS="Allow upload into subfolders"
COM_DROPBOX_ADD_TIMESTAMP_TO_FILE="Add timestamp to the file name"
COM_DROPBOX_PREFIX="Prefix"
COM_DROPBOX_SUFFIX="Suffix"
COM_DROPBOX_DESCRIPTION_FOR_UPLOAD="Description for Upload."
COM_DROPBOX_DESCRIPTION_FOR_UPLOAD_DESC="HTML allowed"
COM_DROPBOX_PIC_THUMBNAIL_SIZE="Thumbnail size"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_DESC="Adjust the maximum thumbnail size of the thubnails."
COM_DROPBOX_PIC_THUMBNAIL_SIZE_THUMB="max. 32px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_SMALL="max. 64px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_MEDIUM="max. 128px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_LARGE="max. 640px/480px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_HUGE="max. 1024px/768px"
COM_DROPBOX_PIC_THUMBNAILS_PER_ROW="Thumbnails per row"
COM_DROPBOX_PIC_BORDER_THICKNESS="border thickness (5-30)"
COM_DROPBOX_PIC_BORDER_COLOR="border color (white if empty)"
COM_DROPBOX_PIC_BORDER_BACKGROUND_PIC="border background pic, leave empty for no pic"
COM_DROPBOX_PIC_USE_ROUNDED_BORDERS="use rounded borders"
COM_DROPBOX_PIC_ANI_STEPS="ani steps (10-30)"
COM_DROPBOX_PIC_TIME_BETWEEN_STEPS="time between steps"
COM_DROPBOX_PIC_ANIMATION="Animation"
COM_DROPBOX_PIC_ANIMATION_FADE="Fade"
COM_DROPBOX_PIC_ANIMATION_GLIDE="Glide"
COM_DROPBOX_PIC_ANIMATION_BUMPGLIDE="Bumpglide"
COM_DROPBOX_PIC_ANIMATION_SMOTHGLIDE="Smoothglide"
COM_DROPBOX_PIC_ANIMATION_EXPGLIDE="Expglide"
COM_DROPBOX_PIC_GLIDE_TRANSPARENCY="glide transparency"
COM_DROPBOX_PIC_SHADOW_UNDER_BORDER="shadow under border"
COM_DROPBOX_PIC_SIZE_OF_SHADOW="size of shadow right/bottom (0-20)"
COM_DROPBOX_PIC_SHADOW_COLOR="shadow color (empty: black)"
COM_DROPBOX_PIC_KEEP_DARK_WHEN_NAV="keep dark when nav"
COM_DROPBOX_PIC_HOW_DARK_WHEN_NAV="how dark the screen should be (0-100)"
COM_DROPBOX_PIC_TIME_OF_DARKENING="how long darkening should take"
COM_DROPBOX_PIC_CENTER_PIC="center enlarged pic on screen"
COM_DROPBOX_PIC_DRAG_N_DROP="enable drag&amp;drop for pics"
COM_DROPBOX_PIC_PRELOAD="preload next/prev pic"
COM_DROPBOX_PIC_SHOW_TITLE_BAR="show pic title bar"
COM_DROPBOX_PIC_KEY_NAVIGATION="key navigation"
COM_DROPBOX_PIC_MOUSE_WHEEL_NAVIGATION="mouse wheel navigation"
COM_DROPBOX_PIC_COLOR_OF_TITLE_BAR_TEXT="color of title bar text (empty: dark grey)"
LOGS="Logs"
BOXES="Boxes"
TIME="Time"
USERID="User Id"
UP="up"
DOWN="down"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES="Create private user directories"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES_DESC="For every user a privater folder will be created unter the main folder. Every user can just access his own folder and cannot reach data outside this folder. Not loged-in users will be redirected to the folder 'guest'"
COM_DROPBOX_CONFIGURATION="Global Dropbox Configuration"
COM_DROPBOX_PIC_SHADOW_INTESITY="shadow intensity (5-30)"
COM_DROPBOX_PIC_DARKEN_SCREEN="darken screen"
COM_DROPBOX_N_ITEMS_DELETED="%s box(es) successfully deleted"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES_NAMED_BY_USERNAME="Yes, name the directories by the username"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES_NAMED_BY_ID="Yes, name the directories by the userID"
FOLDER_DESCRIPTION="The folder you like to publish. For dropbox.com it is always a subfolder of joomla/. And for sugarsync.com it cannot be the root folder nor the folder on the first level. Nevertheless the first level folder must be given for sugarsync.com. e.g. Sugarsync/myfiles"

COM_DROPBOX_DO_VERIFY_PEER_SSL_CERTIFICATE="Do verifying the peers SSL certificate."
COM_DROPBOX_DO_VERIFY_PEER_SSL_CERTIFICATE_DESCR="You can use this option to No if you have SSL problems. This option makes the connection secure and could make you vulnerable for Man-In-The-Midle attacks, its much better to contact your administrator and make him to install the correct certificate"
COM_DROPBOX_MAX_FILESIZE_IN_CACHE="Maximum filesize in cache"
COM_DROPBOX_MAX_FILESIZE_IN_CACHE_DESCR="A file bigger than this size will be cached to be send to the user, but deleted afterwards."
COM_DROPBOX_MAX_CACHE_SIZE="Maximum size of the cache (per box/connection)"
COM_DROPBOX_MAX_CACHE_SIZE_DESCR="If this size is reached a new file will be cached to be send to the user, but deleted afterwards."
COM_DROPBOX_MAX_WRITELOCK_SLEEPING_TIME="Time that should be waited for a releasing of a write lock"
COM_DROPBOX_MAX_WRITELOCK_SLEEPING_TIME_DESCR="When a file has a write lock set an other process can not read it and has to wait. This time is the time it will wait (not longer than max_execution_time). If there is a file lock after this time a error will be shown."
COM_DROPBOX_DATE_FORMAT="Date format for file modification date. <a href='http://www.php.net/manual/en/function.date.php'>Format</a>"
COM_DROPBOX_TIME_ZONE="Timezone for file modification date. <a href='http://www.php.net/manual/en/timezones.php'>List of Supported Timezones</a>"
COM_DROPBOX_LIST_SORTING_FIELD="Default sorting field"
COM_DROPBOX_LIST_SORTING_FIELD_DESC="By what field should the file list be sorted"
COM_DROPBOX_FILE_NAME="Name"
COM_DROPBOX_FILE_MODIFIED_DATE="Modified Date"
COM_DROPBOX_FILE_SIZE="Size"
COM_DROPBOX_LIST_SORTING_DIR="Default sorting direction"
COM_DROPBOX_ASC="Ascending"
COM_DROPBOX_DESC="Descending"
COM_DROPBOX_VIEW_DOWNLOADS_IN_BROWSER="View downloads in browser"
COM_DROPBOX_VIEW_DOWNLOADS_IN_BROWSER_DESCR="If this setting is enabled the download will be viewed if possible in the browser and not presented with a 'Save As' dialog."

COM_DROPBOX_LIST_SHOW_SIZE="Show filesize"
COM_DROPBOX_LIST_SHOW_MODIFIED_DATE="Show file modification-date"

COM_DROPBOX_DROPBOX_SECRET="Dropbox Code"
COM_DROPBOX_DROPBOX_SECRET_DESCRIPTION="You will receive this code after connecting to Dropbox"
