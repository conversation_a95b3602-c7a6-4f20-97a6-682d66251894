;@package    Joomla.Dropbox

;@subpackage Components

;@link http://www.individual-it.net

;@license    GNU/GPL

;Backend file

; Note : All ini files need to be saved as UTF-8- No BOM

;

FOLDER="Папка"
STATUS="Статус"
ACTION="Действия"
CONNECTED="Подключено"
NOT_CONNECTED="Нет подключения"
DELETE_CONNECTION="Удалить подключение"
CONNECT="Подключить"
PARAMETERS_FOR_PIC_FUNCTION="Параметры изображений"
PARAMETERS_FOR_LIST_FUNCTION="Параметры списка"
PARAMETERS_FOR_UPLOAD_FUNCTION="Параметры загрузки"
DROPBOX_DISCONNECTED="Dropbox отключен"
DROPBOX_SAVED="Dropbox сохранен"
ERROR_SAVING_DROPBOX="Ошибка сохранения Dropbox"
DROPBOXES_DELETED="Dropbox(es) удален"
ERROR_DELETING_DROPBOXES="Ошибка: один или несколько Dropboxes невозможно удалить"
CANCELLED="Отменено"
PARAMETERS="Параметры"
OVERRIDES_DESCRIPTION="Специфические пункты меню изменяют настройки по-умолчанию"
FILENAME="Имя файла"
DIRECTION="Направление"
CACHED="Закэшировано"
TOTAL="Всего"
COUNT="Count"
GROUP_BY="Группировка"
ORDER_BY="Сортировка"
GUEST="гость"
LOG_DOWNLOADS="Лог скачиваний"
LOG_DOWNLOADS_DESCR="Записывать лог всех скачиваний в базу данных"
LOG_UPLOADS="Лог загрузок"
LOG_UPLOADS_DESCR="Записывать лог всех загрузок в базу данных"
VIEW_PICTURES="Показывать изображения"
USE_HTTPS="Использовать HTTPS?"
USE_HTTPS_DESCR="HTTPS - более безопасное соединение, но если у вас появляются сбои в работе, то отключите его."
BOX_TYPE="Тип ящика"
GENERAL_SETTINGS_LABEL="Общие настройки"
USERNAME="Sugarsync логин"
PASSWORD="Sugarsync пароль"
USERNAME_PASSWORD_DESCRIPTION="Это логин и пароль только от www.sugarsync.com. Внимание!! Оставьте эти поля пустыми, если вы используете www.dropbox.com"
COM_DROPBOX_DROPBOX_DETAILS="Подробнее"
COM_DROPBOX_ID="Id"
COM_DROPBOX_CONFIGURATION="Настройки всех ящиков"
COM_DROPBOX_YES="Да"
COM_DROPBOX_NO="Нет"
COM_DROPBOX_LIST_FILES="Список файлов"
COM_DROPBOX_LIST_FILES_DESC="список файлов?"
COM_DROPBOX_LIST_JUST_FOLDERS="Список только папок"
COM_DROPBOX_UPLOAD="Разрешить закачку"
COM_DROPBOX_UPLOAD_DESC="разрешить закачку файлов на сервер?"
COM_DROPBOX_VIEW_PICTURES_DESC="показывать изображения с EnlargeIt?"
COM_DROPBOX_SHOW_BREADCRUMBS="Показать навигацию"
COM_DROPBOX_SHOW_BREADCRUMBS_DESC="Показать навигацию т.е. 'Вы находитесь тут:  DROPBOX/files'"
COM_DROPBOX_CHANGE_FOLDER_AFTER_UPLOAD="Изменить папку после закачки"
COM_DROPBOX_ALLOW_UPLOAD_INTO_SUBFOLDERS="Разрешить закачку во вложенные папки"
COM_DROPBOX_ADD_TIMESTAMP_TO_FILE="Добавить время к имени файла"
COM_DROPBOX_PREFIX="Префикс"
COM_DROPBOX_SUFFIX="Суффикс"
COM_DROPBOX_DESCRIPTION_FOR_UPLOAD="Описание для закачки. Используйте HTML BR (http://www.w3schools.com/TAGS/tag_br.asp) для создания новой строки"
COM_DROPBOX_DESCRIPTION_FOR_UPLOAD_DESC="HTML разрешено"
COM_DROPBOX_PIC_THUMBNAIL_SIZE="Размер эскизов"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_DESC="Установить максимальный размер эскиза."
COM_DROPBOX_PIC_THUMBNAIL_SIZE_THUMB="max. 32px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_SMALL="max. 64px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_MEDIUM="max. 128px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_LARGE="max. 640px/480px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_HUGE="max. 1024px/768px"
COM_DROPBOX_PIC_THUMBNAILS_PER_ROW="Эскизов в строке"
COM_DROPBOX_PIC_BORDER_THICKNESS="Толщина рамки (5-30)"
COM_DROPBOX_PIC_BORDER_COLOR="Цвет рамки (по умолчанию белый)"
COM_DROPBOX_PIC_BORDER_BACKGROUND_PIC="Фоновый рисунок рамки, по умолчанию - нет"
COM_DROPBOX_PIC_USE_ROUNDED_BORDERS="Скруглить углы (Mozilla/Safari только)"
COM_DROPBOX_PIC_ANI_STEPS="Любые шаги (10-30)"
COM_DROPBOX_PIC_TIME_BETWEEN_STEPS="Время между шагами"
COM_DROPBOX_PIC_ANIMATION="Анимация"
COM_DROPBOX_PIC_ANIMATION_FADE="Затухание"
COM_DROPBOX_PIC_ANIMATION_GLIDE="Скольжение"
COM_DROPBOX_PIC_ANIMATION_BUMPGLIDE="Резкое скольжение"
COM_DROPBOX_PIC_ANIMATION_SMOTHGLIDE="Плавное скольжение"
COM_DROPBOX_PIC_ANIMATION_EXPGLIDE="Выскальзывание"
COM_DROPBOX_PIC_GLIDE_TRANSPARENCY="Скольжение прозрачности"
COM_DROPBOX_PIC_SHADOW_UNDER_BORDER="Тень от рамки"
COM_DROPBOX_PIC_SIZE_OF_SHADOW="Размер тени справа/вниз (0-20)"
COM_DROPBOX_PIC_SHADOW_COLOR="Цвет тени (по умолчанию черный)"
COM_DROPBOX_PIC_KEEP_DARK_WHEN_NAV="сохранить затемнение при перемещении курсора"
COM_DROPBOX_PIC_HOW_DARK_WHEN_NAV="Степень затемнения экрана (0-100)"
COM_DROPBOX_PIC_TIME_OF_DARKENING="Продолжительность затемнения"
COM_DROPBOX_PIC_CENTER_PIC="Центрировать полноразмерное изображение на экране"
COM_DROPBOX_PIC_DRAG_N_DROP="Включить drag & drop для изображений"
COM_DROPBOX_PIC_PRELOAD="Предзагрузка для следующих/предыдущих изображений"
COM_DROPBOX_PIC_SHOW_TITLE_BAR="Показать заголовок изображения"
COM_DROPBOX_PIC_KEY_NAVIGATION="Навигация клавишами"
COM_DROPBOX_PIC_MOUSE_WHEEL_NAVIGATION="Прокрутка колесом мыши"
COM_DROPBOX_PIC_COLOR_OF_TITLE_BAR_TEXT="Цвет заголовка (по умолчанию темно серый)"
LOGS="Логи"
BOXES="Ящики"
TIME="Время"
USERID="Id пользователя"
UP="вверх"
DOWN="вниз"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES="Создание личных каталогов пользователей"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES_DESC="Для каждого пользователя будет создана своя папка в корневой папке. У каждого пользователя будет доступ только к своей папке. Неавторизованные пользователи будут находиться в гостевой папке 'guest'"
COM_DROPBOX_CONFIGURATION="Основные настройки Dropbox"
COM_DROPBOX_PIC_SHADOW_INTESITY="Интенсивность тени (5-30)"
COM_DROPBOX_PIC_DARKEN_SCREEN="затемнение экрана"
COM_DROPBOX_N_ITEMS_DELETED="%s box(es) successfully deleted"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES_NAMED_BY_USERNAME="Yes, name the directories by the username"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES_NAMED_BY_ID="Yes, name the directories by the userID"
FOLDER_DESCRIPTION="The folder you like to publish. For dropbox.com it is always a subfolder of joomla/. And for sugarsync.com it cannot be the root folder nor the folder on the first level. Nevertheless the first level folder must be given for sugarsync.com. e.g. Sugarsync/myfiles"

COM_DROPBOX_DO_VERIFY_PEER_SSL_CERTIFICATE="Do verifying the peers SSL certificate."
COM_DROPBOX_DO_VERIFY_PEER_SSL_CERTIFICATE_DESCR="You can use this option to No if you have SSL problems. This option makes the connection secure and could make you vulnerable for Man-In-The-Midle attacks, its much better to contact your administrator and make him to install the correct certificate"
COM_DROPBOX_MAX_FILESIZE_IN_CACHE="Maximum filesize in cache"
COM_DROPBOX_MAX_FILESIZE_IN_CACHE_DESCR="A file bigger than this size will be cached to be send to the user, but deleted afterwards."
COM_DROPBOX_MAX_CACHE_SIZE="Maximum size of the cache (per box/connection)"
COM_DROPBOX_MAX_CACHE_SIZE_DESCR="If this size is reached a new file will be cached to be send to the user, but deleted afterwards."
COM_DROPBOX_MAX_WRITELOCK_SLEEPING_TIME="Time that should be waited for a releasing of a write lock"
COM_DROPBOX_MAX_WRITELOCK_SLEEPING_TIME_DESCR="When a file has a write lock set an other process can not read it and has to wait. This time is the time it will wait (not longer than max_execution_time). If there is a file lock after this time a error will be shown."
COM_DROPBOX_DATE_FORMAT="Date format for file modification date. <a href='http://www.php.net/manual/en/function.date.php'>Format</a>"
COM_DROPBOX_TIME_ZONE="Timezone for file modification date. <a href='http://www.php.net/manual/en/timezones.php'>List of Supported Timezones</a>"
COM_DROPBOX_LIST_SORTING_FIELD="Default sorting field"
COM_DROPBOX_LIST_SORTING_FIELD_DESC="By what field should the file list be sorted"
COM_DROPBOX_FILE_NAME="Name"
COM_DROPBOX_FILE_MODIFIED_DATE="Modified Date"
COM_DROPBOX_FILE_SIZE="Size"
COM_DROPBOX_LIST_SORTING_DIR="Default sorting direction"
COM_DROPBOX_ASC="Ascending"
COM_DROPBOX_DESC="Descending"
COM_DROPBOX_VIEW_DOWNLOADS_IN_BROWSER="View downloads in browser"
COM_DROPBOX_VIEW_DOWNLOADS_IN_BROWSER_DESCR="If this setting is enabled the download will be viewed if possible in the browser and not presented with a 'Save As' dialog."

COM_DROPBOX_LIST_SHOW_SIZE="Show filesize"
COM_DROPBOX_LIST_SHOW_MODIFIED_DATE="Show file modification-date"

COM_DROPBOX_DROPBOX_SECRET="Dropbox Code"
COM_DROPBOX_DROPBOX_SECRET_DESCRIPTION="You will receive this code after connecting to Dropbox"
