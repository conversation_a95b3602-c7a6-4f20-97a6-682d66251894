;@package    Joomla.Dropbox

;@subpackage Components

;@link http://www.individual-it.net

;@license    GNU/GPL

;Backend file

; Note : All ini files need to be saved as UTF-8- No BOM

;
FOLDER="Folder"
STATUS="Status"
ACTION="Action"
CONNECTED="Connected"
NOT_CONNECTED="Not Connected"
DELETE_CONNECTION="Delete Connection"
CONNECT="Connect"
PARAMETERS_FOR_PIC_FUNCTION="Pictures function"
PARAMETERS_FOR_LIST_FUNCTION="List function"
PARAMETERS_FOR_UPLOAD_FUNCTION="Upload function"
DROPBOX_DISCONNECTED="Dropbox disconnected"
DROPBOX_SAVED="Dropbox Saved"
ERROR_SAVING_DROPBOX="Error Saving Dropbox"
DROPBOXES_DELETED="Dropbox(es) Deleted"
ERROR_DELETING_DROPBOXES="Error: One or More Dropboxes Could not be Deleted"
CANCELLED="Cancelled"
PARAMETERS="Parameters"
OVERRIDES_DESCRIPTION="Item-specific overrides Menu-specific overrides Component-default"
FILENAME="Filename"
DIRECTION="Direction"
CACHED="Cached"
TOTAL="Total"
COUNT="Count"
GROUP_BY="Group"
ORDER_BY="Order"
GUEST="guest"
LOG_DOWNLOADS="Log Downloads"
LOG_DOWNLOADS_DESCR="Log every download in the database"
LOG_UPLOADS="Log Uploads"
LOG_UPLOADS_DESCR="Log every upload in the database"
VIEW_PICTURES="View Pictures"
USE_HTTPS="Use HTTPS?"
USE_HTTPS_DESCR="HTTPS is more secure but if you have problems try to switch it off?"
BOX_TYPE="Box Type"
GENERAL_SETTINGS_LABEL="General Settings"
USERNAME="Sugarsync Username"
PASSWORD="Sugarsync Password"
USERNAME_PASSWORD_DESCRIPTION="DO NOT PUT IN NAME AND PASSWORD IF YOU USE www.dropbox.com"
COM_DROPBOX_DROPBOX_DETAILS="Details"
COM_DROPBOX_ID="Id"
COM_DROPBOX_CONFIGURATION="Configuration for all Boxes"
COM_DROPBOX_YES="Yes"
COM_DROPBOX_NO="No"
COM_DROPBOX_LIST_FILES="List files"
COM_DROPBOX_LIST_FILES_DESC="list the files?"
COM_DROPBOX_LIST_JUST_FOLDERS="List just folders"
COM_DROPBOX_UPLOAD="Upload"
COM_DROPBOX_UPLOAD_DESC="allow to upload files?"
COM_DROPBOX_VIEW_PICTURES_DESC="view pictures with EnlargeIt?"
COM_DROPBOX_SHOW_BREADCRUMBS="Show breadcrumbs"
COM_DROPBOX_SHOW_BREADCRUMBS_DESC="Show breadcrumbs e.g. 'You are here:  DROPBOX/files'"
COM_DROPBOX_CHANGE_FOLDER_AFTER_UPLOAD="Change folder after upload"
COM_DROPBOX_ALLOW_UPLOAD_INTO_SUBFOLDERS="Allow upload into subfolders"
COM_DROPBOX_ADD_TIMESTAMP_TO_FILE="Add timestamp to the file name"
COM_DROPBOX_PREFIX="Prefix"
COM_DROPBOX_SUFFIX="Suffix"
COM_DROPBOX_DESCRIPTION_FOR_UPLOAD="Description for Upload"
COM_DROPBOX_DESCRIPTION_FOR_UPLOAD_DESC="HTML allowed"
COM_DROPBOX_PIC_THUMBNAIL_SIZE="Thumbnail size"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_DESC="Adjust the maximum thumbnail size of the thubnails."
COM_DROPBOX_PIC_THUMBNAIL_SIZE_THUMB="max. 32px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_SMALL="max. 64px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_MEDIUM="max. 128px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_LARGE="max. 640px/480px"
COM_DROPBOX_PIC_THUMBNAIL_SIZE_HUGE="max. 1024px/768px"
COM_DROPBOX_PIC_THUMBNAILS_PER_ROW="Thumbnails per row"
COM_DROPBOX_PIC_BORDER_THICKNESS="border thickness (5-30)"
COM_DROPBOX_PIC_BORDER_COLOR="border color (white if empty)"
COM_DROPBOX_PIC_BORDER_BACKGROUND_PIC="border background pic, leave empty for no pic"
COM_DROPBOX_PIC_USE_ROUNDED_BORDERS="use rounded borders"
COM_DROPBOX_PIC_ANI_STEPS="ani steps (10-30)"
COM_DROPBOX_PIC_TIME_BETWEEN_STEPS="time between steps"
COM_DROPBOX_PIC_ANIMATION="Animation"
COM_DROPBOX_PIC_ANIMATION_FADE="Fade"
COM_DROPBOX_PIC_ANIMATION_GLIDE="Glide"
COM_DROPBOX_PIC_ANIMATION_BUMPGLIDE="Bumpglide"
COM_DROPBOX_PIC_ANIMATION_SMOTHGLIDE="Smoothglide"
COM_DROPBOX_PIC_ANIMATION_EXPGLIDE="Expglide"
COM_DROPBOX_PIC_GLIDE_TRANSPARENCY="glide transparency"
COM_DROPBOX_PIC_SHADOW_UNDER_BORDER="shadow under border"
COM_DROPBOX_PIC_SIZE_OF_SHADOW="size of shadow right/bottom (0-20)"
COM_DROPBOX_PIC_SHADOW_COLOR="shadow color (empty: black)"
COM_DROPBOX_PIC_KEEP_DARK_WHEN_NAV="keep dark when nav"
COM_DROPBOX_PIC_HOW_DARK_WHEN_NAV="how dark the screen should be (0-100)"
COM_DROPBOX_PIC_TIME_OF_DARKENING="how long darkening should take"
COM_DROPBOX_PIC_CENTER_PIC="center enlarged pic on screen"
COM_DROPBOX_PIC_DRAG_N_DROP="enable drag&amp;drop for pics"
COM_DROPBOX_PIC_PRELOAD="preload next/prev pic"
COM_DROPBOX_PIC_SHOW_TITLE_BAR="show pic title bar"
COM_DROPBOX_PIC_KEY_NAVIGATION="key navigation"
COM_DROPBOX_PIC_MOUSE_WHEEL_NAVIGATION="mouse wheel navigation"
COM_DROPBOX_PIC_COLOR_OF_TITLE_BAR_TEXT="color of title bar text (empty: dark grey)"
LOGS="Logs"
BOXES="Boxes"
TIME="Time"
USERID="User Id"
UP="up"
DOWN="down"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES="Create private user directories"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES_DESC="For every user a privater folder will be created unter the main folder. Every user can just access his own folder and cannot reach data outside this folder. Not loged-in users will be redirected to the folder 'guest'"
COM_DROPBOX_CONFIGURATION="Global Dropbox Configuration"
COM_DROPBOX_PIC_SHADOW_INTESITY="shadow intensity (5-30)"
COM_DROPBOX_PIC_DARKEN_SCREEN="darken screen"
COM_DROPBOX_N_ITEMS_DELETED="%s box(es) successfully deleted"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES_NAMED_BY_USERNAME="Yes, name the directories by the username"
COM_DROPBOX_PRIVATE_USER_DIRECTORIES_NAMED_BY_ID="Yes, name the directories by the userID"
FOLDER_DESCRIPTION="The folder you like to publish"

COM_DROPBOX_ERROR_UNACCEPTABLE="Some values are unacceptable"
COM_DROPBOX_DO_VERIFY_PEER_SSL_CERTIFICATE="Do verifying the peers SSL certificate."
COM_DROPBOX_DO_VERIFY_PEER_SSL_CERTIFICATE_DESCR="You can use this option to No if you have SSL problems. This option makes the connection secure and could make you vulnerable for Man-In-The-Midle attacks, its much better to contact your administrator and make him to install the correct certificate"
COM_DROPBOX_MAX_FILESIZE_IN_CACHE="Maximum filesize in cache"
COM_DROPBOX_MAX_FILESIZE_IN_CACHE_DESCR="A file bigger than this size will be cached to be send to the user, but deleted afterwards."
COM_DROPBOX_MAX_CACHE_SIZE="Maximum size of the cache (per box/connection)"
COM_DROPBOX_MAX_CACHE_SIZE_DESCR="If this size is reached a new file will be cached to be send to the user, but deleted afterwards."
COM_DROPBOX_MAX_WRITELOCK_SLEEPING_TIME="Time that should be waited for a releasing of a write lock"
COM_DROPBOX_MAX_WRITELOCK_SLEEPING_TIME_DESCR="When a file has a write lock set an other process can not read it and has to wait. This time is the time it will wait (not longer than max_execution_time). If there is a file lock after this time a error will be shown."
COM_DROPBOX_DATE_FORMAT="Date format for file modification date. <a href='http://www.php.net/manual/en/function.date.php'>Format</a>"
COM_DROPBOX_TIME_ZONE="Timezone for file modification date. <a href='http://www.php.net/manual/en/timezones.php'>List of Supported Timezones</a>"
COM_DROPBOX_LIST_SORTING_FIELD="Default sorting field"
COM_DROPBOX_LIST_SORTING_FIELD_DESC="By what field should the file list be sorted"
COM_DROPBOX_FILE_NAME="Name"
COM_DROPBOX_FILE_MODIFIED_DATE="Modified Date"
COM_DROPBOX_FILE_SIZE="Size"
COM_DROPBOX_LIST_SORTING_DIR="Default sorting direction"
COM_DROPBOX_ASC="Ascending"
COM_DROPBOX_DESC="Descending"
COM_DROPBOX_VIEW_DOWNLOADS_IN_BROWSER="View Downloads in Browser"
COM_DROPBOX_VIEW_DOWNLOADS_IN_BROWSER_DESCR="If this setting is enabled the download will be viewed if possible in the browser and not presented with a 'Save As' dialog."
COM_DROPBOX_LIST_SHOW_SIZE="Show filesize"
COM_DROPBOX_LIST_SHOW_MODIFIED_DATE="Show file modification-date"
COM_DROPBOX_LIST_SHOW_SIZE="Show filesize"
COM_DROPBOX_LIST_SHOW_MODIFIED_DATE="Show file modification-date"
COM_DROPBOX_NOTIFICATION_EMAIL_SENDER_ADDRESS="Email notification sender address"
COM_DROPBOX_NOTIFICATION_EMAIL_SENDER_ADDRESS_DESCR="sender email address for upload notifications"
COM_DROPBOX_NOTIFICATION_EMAIL_RECIPIENT_ADDRESS="Email notification recipient address"
COM_DROPBOX_NOTIFICATION_EMAIL_RECIPIENT_ADDRESS_DESCR="recipient email address for upload notifications"
COM_DROPBOX_SHOW_SEARCH="Show search field"
COM_DROPBOX_DROPBOX_SECRET="Dropbox Code"
COM_DROPBOX_DROPBOX_SECRET_DESCRIPTION="You will receive this code after connecting to Dropbox"
COM_DROPBOX_TOKEN_MUST_BE_EMPTY="To establish a connection the 'Dropbox Code' field must be empty. Please delete the string in the 'Dropbox Code' field and try again"
