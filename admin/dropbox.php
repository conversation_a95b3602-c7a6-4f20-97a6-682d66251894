<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access to this file
\defined('_JEXEC') or die('Restricted access');

if (!\defined('DS')) {
    \define('DS', DIRECTORY_SEPARATOR);
}
// require helper file
//JLoader::register('DropboxHelper', dirname(__FILE__) . DS . 'helpers' . DS . 'dropbox.php');

// import joomla controller library
jimport('joomla.application.component.controller');

// Get an instance of the controller prefixed by Dropbox
$controller = JControllerLegacy::getInstance('Dropbox');

//get the task
$jinput   = JFactory::getApplication()->input;
$task     = $jinput->getCmd('task');

// Perform the Request task
try {
    $controller->execute($task);
} catch (Exception $e) {
    throw new Exception($e->getMessage() . " Task:" . $task);
}

// Redirect if set by the controller
$controller->redirect();
