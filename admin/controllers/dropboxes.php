<?php

// No direct access to this file
\defined('_JEXEC') or die('Restricted access');

// import Joom<PERSON> controlleradmin library
jimport('joomla.application.component.controlleradmin');

/**
 * Dropboxes Controller
 */
// @codingStandardsIgnoreLine because of no namespace set
class DropboxControllerDropboxes extends JControllerAdmin
{
    /**
     * Proxy for getModel.
     *
     * @since 1.6
     */
    public function getModel(
        $name = 'Dropbox',
        $prefix = 'DropboxModel',
        $config = ['ignore_request' => true]
    ) {
        return parent::getModel($name, $prefix, $config);
    }
}
