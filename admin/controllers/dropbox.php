<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access
\defined('_JEXEC') or die('Restricted access');

/**
 * Dropbox Dropbox Controller
 *
 * @package    Joomla.Dropbox
 * @subpackage Components
 */

// import Jo<PERSON><PERSON> controllerform library
jimport('joomla.application.component.controllerform');

// @codingStandardsIgnoreLine because of no namespace set
class DropboxControllerDropbox extends JControllerForm
{
    /**
     * clear a record (dropbox_secret     dropbox_state) and redirect to main page
     *
     * @return void
     */
    public function clear(): void
    {
        $model = $this->getModel('dropbox');

        if ($model->clear()) {
            $msg = JText::_('DROPBOX_DISCONNECTED');
        } else {
            $msg = JText::_('ERROR DISCONNECTING DROPBOX');
        }

        // Check the table in so it can be edited.... we are done with it anyway
        $link = 'index.php?option=com_dropbox';
        $this->setRedirect($link, $msg);
    }

    /**
     * Method to connect to dropbox.com
     * It automatically also applies the form
     *
     * @access public
     */
    public function connect(): void
    {
        $jform         = filter_input(INPUT_POST, 'jform', FILTER_DEFAULT, FILTER_REQUIRE_ARRAY);
        $dropboxSecret = $jform['dropbox_secret'] ?? '';

        if ($dropboxSecret !== '') {
            $application = JFactory::getApplication();
            $application->enqueueMessage(JText::_('COM_DROPBOX_TOKEN_MUST_BE_EMPTY'), 'error');
        }

        $this->applyAndSave();
    }

    /**
     * Method to save the connection before the preview
     *
     * @access public
     */
    public function preview(): void
    {
        $this->applyAndSave();
    }

    /**
     * Register apply task and execute it
     *
     * @access private
     */
    private function applyAndSave(): void
    {
        $this->registerTask('apply', 'save');
        $this->execute('apply');
    }
}
