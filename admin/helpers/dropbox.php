<?php

// No direct access to this file
\defined('_JEXEC') or die;

/**
 * Dropbox component helper.
 */
// @codingStandardsIgnoreLine because of no namespace set
abstract class DropboxHelper
{
    private const array ACTIONS = [
        'core.admin',
        'core.manage',
        'core.create',
        'core.edit',
        'core.delete',
    ];

    /**
     * Get the actions
     */
    public static function getActions($messageId = 0): \stdClass
    {
        $user      = JFactory::getUser();
        $result    = new \stdClass();

        if (empty($messageId)) {
            $assetName = 'com_dropbox';
        } else {
            $assetName = 'com_dropbox.message.' . (int) $messageId;
        }

        foreach (self::ACTIONS as $action) {
            $result->$action = $user->authorise($action, $assetName);
        }

        return $result;
    }
}
