<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access
\defined('_JEXEC') or die('Restricted access');

jimport('joomla.application.component.view');

/**
 * Dropbox View
 *
 * @package    Joomla.Dropbox
 * @subpackage Components
 */
// @codingStandardsIgnoreLine because of no namespace set
class DropboxViewDropbox extends JViewLegacy
{
    /**
     * display method of Dropbox view
     *
     * @return void
     **/
    public function display($tpl = null)
    {
        //get the dropbox
        $dropbox = $this->get('Item');
        $isNew   = ($dropbox->id < 1);
        $form    = $this->get('Form');
        $item    = $this->get('Item');
        $script  = $this->get('Script');

        // Check for errors.
        if (\count($errors = $this->get('Errors'))) {
            throw new Exception('500 ' . implode('<br />', $errors));
            return false;
        }
        // Assign the Data
        $this->form   = $form;
        $this->item   = $item;
        $this->script = $script;

        $text = $isNew ? JText::_('New') : JText::_('Edit');
        JToolBarHelper::title(JText::_('Dropbox') . ': <small><small>[ ' . $text . ' ]</small></small>');

        // JRequest::setVar('hidemainmenu', true);
        JFactory::getApplication()->get('hidemainmenu', 1);

        JToolBarHelper::save('dropbox.save');

        JToolBarHelper::apply('dropbox.apply');

        if (!$isNew) {
            JToolBarHelper::custom('dropbox.connect', 'arrow-right-4', 'arrow-right-4', 'CONNECT', false);
            JToolBarHelper::custom('dropbox.preview', 'out-2', 'out-2', 'JGLOBAL_PREVIEW', false);
        }

        JToolBarHelper::cancel('dropbox.cancel', $isNew ? 'JTOOLBAR_CANCEL' : 'JTOOLBAR_CLOSE');

        parent::display($tpl);

        // Set the document
        $this->setDocument();
    }

    /**
     * Method to set up the document properties
     *
     * @return void
     */
    public function setDocument(?Joomla\CMS\Document\Document $document = null): void
    {
        $document = JFactory::getDocument();

        $document->addCustomTag('<link href="' . JURI::root() . '/administrator/components/com_dropbox/css/dropbox.css" rel="stylesheet" type="text/css" />');
        //         $document->addScript(JURI::root().'components/com_dropbox/js/jquery.js');
        $document->addScript(JURI::root() . 'components/com_dropbox/js/dropbox.js');
        $document->addScriptDeclaration(
            'jQuery(document).ready(function() {
  												hide_show_fields_for_different_box_types();
                                                makeTabsResponsiveDropbox();
											});'
        );
        //         $document->addScript(JURI::root() . "/administrator/components/com_dropbox"
        //                 . "/models/forms/dropbox.js");
        //         $document->addScript(JURI::root() . "/administrator/components/com_dropbox"
        //                 . "/js/submitbutton.js");
        JText::script('COM_DROPBOX_ERROR_UNACCEPTABLE');
    }
}
