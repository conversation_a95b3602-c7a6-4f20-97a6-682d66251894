<?php

/**
 * @package    Joomla.Administrator
 * @subpackage com_redirect
 *
 * @copyright Copyright (C) 2005 - 2013 Open Source Matters, Inc. All rights reserved.
 * @license   GNU General Public License version 2 or later; see LICENSE.txt
 */

\defined('_JEXEC') or die;

// Include the HTML helpers.
JHtml::addIncludePath(JPATH_COMPONENT . '/helpers/html');
JHtml::_('behavior.formvalidator');
JHtml::_('behavior.keepalive');
JHtml::_('formbehavior.chosen', 'select');
$params = $this->form->getFieldsets('params');

?>
<script type="text/javascript">
    jQuery( document ).ready(function() {
        validate_form();
        jQuery( "#jform_folder" ).on("change", function(event){
            if (validate_folder()){
                Joomla.submitform("dropbox.apply", document.getElementById('link-form'));
            }
            hide_show_connect_preview_buttons();
        });
        jQuery( "#jform_dropbox_secret" ).on("change", function(event){
            if (jQuery("#jform_dropbox_secret").val().trim() !== '')
            {
                Joomla.submitform("dropbox.apply", document.getElementById('link-form'));
            }
            hide_show_connect_preview_buttons();
        });
    });
    
    Joomla.submitbutton = function(task)
    {
        if (task == 'dropbox.cancel' || document.formvalidator.isValid(document.getElementById('link-form')))
        {
            Joomla.submitform(task, document.getElementById('link-form'));
        }
        if (task === "dropbox.connect" || task === "dropbox.preview" )
        {
            window.open('<?php echo JRoute::_('../index.php?option=com_dropbox&id=' . (int) $this->item->id); ?>','_blank');
        }        
    };    

    
</script>

<form action="<?php echo JRoute::_('index.php?option=com_dropbox&id=' . (int) $this->item->id); ?>" method="post" name="adminForm" id="link-form" class="form-validate form-horizontal">
    <fieldset>
        <ul class="nav nav-tabs">
            <li class="active"><a class="nav-link" href="#details" data-toggle="tab"><?php echo empty($this->item->id) ? JText::_('COM_DROPBOX_DROPBOX_DETAILS') : JText::sprintf('COM_DROPBOX_DROPBOX_DETAILS', $this->item->id); ?></a></li>
            <?php foreach ($params as $name => $fieldset) : ?>
            <li><a class="nav-link" href="#<?php echo $name;?>" data-toggle="tab"><?php echo JText::_($fieldset->label);?></a></li>
            <?php endforeach; ?>
        </ul>
        <div class="w-60 tab-content">
            <div class="tab-pane active" id="details">
                <?php foreach ($this->form->getFieldset('details') as $field) : ?>
                    <div class="control-group">
                        <div class="control-label"><?php echo $field->label; ?></div>
                        <div class="controls"><?php echo $field->input; ?></div>
                    </div>        
                <?php endforeach; ?>
            </div>
            <?php foreach ($params as $name => $fieldset) : ?>
            <div class="tab-pane" id="<?php echo $name;?>">
                <?php foreach ($this->form->getFieldset($name) as $field) : ?>
                    <div class="control-group">
                        <div class="control-label"><?php echo $field->label; ?></div>
                        <div class="controls">
                    <?php
                    if (isset($this->item->params["data"][$field->fieldname])) {
                        $field->setValue($this->item->params["data"][$field->fieldname]);
                    }
                    echo $field->input;
                    ?></div>
                        
                    </div>
                <?php endforeach; ?>
                
            </div>
            <?php endforeach; ?>
            
            
            
        </div>
        <input type="hidden" name="task" value="" />
        <?php echo JHtml::_('form.token'); ?>
    </fieldset>
</form>
