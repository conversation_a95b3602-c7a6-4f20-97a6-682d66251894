<?php

// No direct access to this file
\defined('_JEXEC') or die('Restricted access');

// import Joomla view library
jimport('joomla.application.component.view');

/**
 * HelloWorlds View
 */
// @codingStandardsIgnoreLine because of no namespace set
class DropboxViewLogs extends JViewLegacy
{
    protected $items;
    protected $pagination;
    protected $state;

    /**
     * Logs view display method
     *
     * @return void
     */
    public function display($cachable = false, $urlparams = false)
    {
        // Initialise variables.
        $this->items         = $this->get('Items');
        $this->pagination    = $this->get('Pagination');
        $this->state         = $this->get('State');

        // Check for errors.
        if (\count($errors = $this->get('Errors'))) {
            throw new Exception('500 ' . implode('<br />', $errors));
            return false;
        }
        $document = JFactory::getDocument();

        $document->addCustomTag('<link href="' . JURI::root() . '/administrator/components/com_dropbox/css/dropbox.css" rel="stylesheet" type="text/css" />');

        JToolBarHelper::title(JText::_('Dropbox Manager'), 'dropbox.png');

        include_once JPATH_COMPONENT . '/helpers/dropbox.php';

        $this->sidebar = JHtmlSidebar::render();

        // Display the template
        parent::display();
    }
}
