<?php

/**
 * No direct acess to this file
 **/

\defined('_JEXEC') or die('Restricted Access');

JHtml::addIncludePath(JPATH_COMPONENT . '/helpers/html');

$listOrder  = $this->escape($this->state->get('list.ordering'));
$listDirn   = $this->escape($this->state->get('list.direction'));
//TODO Ordering does not work
?>

<form
  action="<?php echo JRoute::_('index.php?option=com_dropbox&view=logs'); ?>"
  method="post" id="adminForm" name="adminForm" class="d-flex bd-highlight">

  <div id="j-main-container" class="span10 w-85 flex-grow-1 bd-highlight">
    <div id="editcell">
    <table class="table table-striped">
      <thead>
    
        <tr>
          <th colspan="10" class="title"><?php echo JText::_("ORDER_BY"); ?></th>
        </tr>
    
        <tr>
          <th  class="nowrap" width="5"><?php echo JHtml::_('grid.sort', 'ID', 'id', $listDirn, $listOrder); ?>
    
          </th>
          <th class="nowrap"><?php echo JHtml::_('grid.sort', 'UserId', 'userId', $listDirn, $listOrder); ?>
          </th>
          <th class="nowrap"><?php echo JText::_('Username'); ?></th>
          <th class="nowrap"><?php echo JHtml::_('grid.sort', 'FOLDER', 'folder', $listDirn, $listOrder); ?>
    
    
          </th>
          <th class="nowrap"><?php echo JHtml::_('grid.sort', 'FILENAME', 'filename', $listDirn, $listOrder); ?>
          </th>
          <th class="nowrap"><?php echo JHtml::_('grid.sort', 'DIRECTION', 'direction', $listDirn, $listOrder); ?>
    
          </th>
          <th class="nowrap"><?php echo JHtml::_('grid.sort', 'TIME', 'time', $listDirn, $listOrder); ?>
    
          </th>
          <th class="nowrap"><?php echo JHtml::_('grid.sort', 'Dropbox Id', 'dropboxId', $listDirn, $listOrder); ?>
          </th>
          <th class="nowrap"><?php echo JHtml::_('grid.sort', 'CACHED', 'cached', $listDirn, $listOrder); ?>
    
        </tr>
      </thead>
    
      <?php
        $k = 0;
for ($i = 0, $n = \count($this->items); $i < $n; $i++) {
    $row =& $this->items[$i];

    $user = JFactory::getUser($row->userId);

    ?>
      <tr class="<?php echo "row$k"; ?>">
        <td class="row-id"><?php echo (int)$row->id; ?></td>
        <td class="user-id"><?php echo (int)$row->userId; ?></td>
        <td class="username"><?php if ($row->userId == '') {
            echo JText::_("GUEST");
        } else {
            echo $user->username;
        }  ?></td>
        <td class="folder-name"><?php echo htmlspecialchars($row->folder); ?></td>
        <td class="file-name"><?php echo htmlspecialchars($row->filename); ?></td>
        <td class="direction"><?php echo $row->direction; ?></td>
        <td class="time"><?php echo $row->time; ?></td>
        <td class="dropbox-id"><?php echo $row->dropboxId; ?></td>
        <td class="cached"><?php
        if ($row->cached == 1) {
            echo JText::_("COM_DROPBOX_YES");
        } else {
            echo JText::_("COM_DROPBOX_NO");
        }
    ?></td>
    
      </tr>
            <?php
        $k = 1 - $k;
}
?>
    
      <tfoot>
        <tr>
          <td colspan="2"><?php echo JText::_("TOTAL") . ": " . $this->pagination->total;
?></td>
          <td colspan="8"><?php

echo $this->pagination->getListFooter();

?></td>
        </tr>
      </tfoot>
    
    </table>
    </div>
  </div>

<div><input type="hidden" name="filter_order"
  value="<?php echo $listOrder; ?>" /> <input type="hidden"
  name="filter_order_Dir" value="<?php echo $listDirn; ?>" /> <?php echo JHtml::_('form.token'); ?>
</div>
</form>
