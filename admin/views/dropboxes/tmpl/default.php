<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

\defined('_JEXEC') or die('Restricted access');
// load tooltip behavior

JHtml::addIncludePath(JPATH_COMPONENT . '/helpers/html');
JHtml::_('formbehavior.chosen', 'select');
?>

<form action="<?php echo JRoute::_('index.php?option=com_dropbox'); ?>"
    method="post" id="adminForm" name="adminForm" class="form-validate d-flex bd-highlight">

    <div id="j-main-container" class="span10 w-85 flex-grow-1 bd-highlight">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th width="20"><input type="checkbox" name="checkall-toggle"
                        value="" title="<?php echo JText::_('JGLOBAL_CHECK_ALL'); ?>"
                        onclick="Joomla.checkAll(this)" />
                    </th>
                    <th class="title"><?php echo JText::_('COM_DROPBOX_ID'); ?>
                    </th>
                    <th width="30%" class="nowrap"><?php echo JText::_('FOLDER'); ?>
                    </th>
                    <th width="30%" class="nowrap"><?php echo JText::_('STATUS'); ?>
                    </th>
                    <th width="10%" class="nowrap"><?php echo JText::_('JGRID_HEADING_ACCESS'); ?>
                    </th>
                    <th width="1%" class="nowrap center"><?php echo JText::_('ACTION'); ?>
                    </th>
                    <th width="1%" class="nowrap center"><?php echo JText::_('JGLOBAL_PREVIEW'); ?>
                    </th>
                </tr>
            </thead>
            <tbody>

                <?php

                for ($i = 0, $n = \count($this->items); $i < $n; $i++) {
                    $row           =& $this->items[$i];
                    $checked       = JHTML::_('grid.id', $i, $row->id);
                    $link          = JRoute::_('index.php?option=com_dropbox&task=dropbox.edit&id=' . $row->id);
                    $link_activate = '';

                    if (\strlen($row->dropbox_secret) > 1) {
                        $state         = 'CONNECTED';
                        $action        = 'DELETE_CONNECTION';
                        $link_activate = JRoute::_('index.php?option=com_dropbox&task=dropbox.clear&id=' . $row->id);
                    } else {
                        $state         = 'NOT_CONNECTED';
                        $link_activate = JRoute::_('../index.php?option=com_dropbox&id=' . $row->id);
                        $action        = 'CONNECT';
                    }
                    ?>
                <tr class="row<?php echo $i % 2; ?>">
                    <td class="center"><?php echo JHtml::_('grid.id', $i, $row->id); ?>
                    </td>
                    <td class="folder-id"><?php echo (int)$row->id; ?>
                    </td>
                    <td class="small folder-name"><a href="<?php echo $link; ?>"><?php echo $row->folder; ?>
                    </a>
                    </td>
                    <td class="small folder-status"><?php echo JText::_($state); ?>
                    </td>
                    <td class="small folder-access"><?php echo $this->escape($row->access_level); ?>
                    </td>
                    <td class="center folder-action"><a
                    <?php if ($action == 'CONNECT') {
                        echo ' target="_blank" ';
                    }?>
                        href="<?php echo $link_activate; ?>"><?php echo JText::_($action); ?>
                    </a>
                    </td>
                    <td class="center folder-preview">
                    <?php
                    if ($state == 'CONNECTED') {
                        echo '<a target="_blank"  href="' .
                                JRoute::_('../index.php?option=com_dropbox&id=' . $row->id) . '">
                                    <span class="preview-icon"></span>
                              </a>';
                    }
                    ?>
                    </td>
                </tr>
                    <?php
                }
?>
            <tfoot>
            <tr>
                <td colspan="2"><?php echo JText::_("TOTAL") . ": " . $this->pagination->total;
?></td>
                <td ><?php echo $this->pagination->getLimitBox();
?></td>
                <td colspan="4"><?php
    echo $this->pagination->getListFooter();
?></td>
            </tr>
        </tfoot>
        </table>
    </div>

        <input type="hidden" name="task" value="" />
        <input type="hidden" name="boxchecked" value="0" />
        <?php echo JHtml::_('form.token'); ?>

</form>

