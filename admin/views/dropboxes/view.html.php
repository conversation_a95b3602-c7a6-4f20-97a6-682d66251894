<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// Check to ensure this file is included in Joomla!
\defined('_JEXEC') or die();

jimport('joomla.application.component.view');

/**
 * Dropboxes View
 *
 * @package    Joomla.Dropbox
 * @subpackage Components
 */
// @codingStandardsIgnoreLine because of no namespace set
class DropboxViewDropboxes extends JViewLegacy
{
    protected $items;

    protected $state;

    /**
     * Dropboxes view display method
     *
     * @return void
     **/
    public function display($tpl = null)
    {
        include_once JPATH_COMPONENT . '/helpers/dropbox.php';

        $this->sidebar = JHtmlSidebar::render();
        $document      = JFactory::getDocument();

        $document->addCustomTag('<link href="' . JURI::root() . '/administrator/components/com_dropbox/css/dropbox.css" rel="stylesheet" type="text/css" />');

        // Get data from the model and
        // Assign data to the view
        $this->items      = $this->get('Items');
        $pagination       = $this->get('Pagination');
        $this->pagination = $pagination;
        $this->state      = $this->get('State');

        // Check for errors.
        if (\count($errors = $this->get('Errors'))) {
            throw new Exception('500 ' . implode('<br />', $errors));
            return false;
        }

        // Display the template
        parent::display($tpl);

        // Set the toolbar
        $this->addToolBar();
    }

    protected function addToolBar()
    {
        JToolBarHelper::title(JText::_('Dropbox Manager'), 'dropbox.png');
        JToolBarHelper::deleteList('', 'dropboxes.delete');
        JToolBarHelper::editList('dropbox.edit');
        JToolBarHelper::addNew('dropbox.add');
        JToolBarHelper::preferences('com_dropbox', '500', '700');
    }
}
