<?xml version="1.0" encoding="utf-8"?>
<form 	addrulepath="/administrator/components/com_dropbox/models/rules">
	<fieldset name="details">
		<field name="id" type="hidden" />
		<field 	name="box_type"
		          type="list"
		          label="BOX_TYPE"
		          description=""
		          onchange="hide_show_fields_for_different_box_types()"
		          size="1" class="inputbox" validate="" default="dropbox">
			<option value="dropbox">Dropbox - www.dropbox.com</option>
		</field>
		<field name="folder" type="text" label="FOLDER" description="FOLDER_DESCRIPTION"
		       size="30" class="inputbox" validate="" default=""></field>
		<field name="dropbox_secret" type="text" label="COM_DROPBOX_DROPBOX_SECRET" description="COM_DROPBOX_DROPBOX_SECRET_DESCRIPTION"
		       size="50" class="inputbox" validate="" default=""></field>
		<field name="access"
		       type="accesslevel"
		       label="JFIELD_ACCESS_LABEL"
		       description="JFIELD_ACCESS_DESC"
		       class="inputbox"
		       size="1"
		/>
	</fieldset>
	<fieldset name="userdetails">
		<field name="username" type="text" label="USERNAME" description="USERNAME_PASSWORD_DESCRIPTION"
		       size="30" class="inputbox" validate="" required="false" default=""></field>
		<field name="password" type="text" label="PASSWORD" description="USERNAME_PASSWORD_DESCRIPTION"
		       size="30" class="inputbox" validate="" required="false" default=""></field>
	</fieldset>
	<fields name="params">
		<fieldset name="GENERAL" label="PARAMETERS">
			<field
				name="function_list"
				type="list"
				label="COM_DROPBOX_LIST_FILES"
				description="COM_DROPBOX_LIST_FILES_DESC"
				default=""
			>
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="2">COM_DROPBOX_LIST_JUST_FOLDERS</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field
				name="function_upload"
				type="list"
				label="COM_DROPBOX_UPLOAD"
				description="COM_DROPBOX_UPLOAD_DESC"
				default=""
			>
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="show_search" type="list" default=""

			       label="COM_DROPBOX_SHOW_SEARCH" description="">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field
				name="function_pic"
				type="list"
				label="VIEW_PICTURES"
				description="COM_DROPBOX_VIEW_PICTURES_DESC"
				default=""
			>
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="log_downloads" type="list" default="" label="LOG_DOWNLOADS" description="LOG_DOWNLOADS_DESCR">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="log_uploads" type="list" default="" label="LOG_UPLOADS" description="LOG_UPLOADS_DESCR">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="view_downloads_in_browser" type="list" default="" label="COM_DROPBOX_VIEW_DOWNLOADS_IN_BROWSER" description="COM_DROPBOX_VIEW_DOWNLOADS_IN_BROWSER_DESCR">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="private_user_directories" type="list" default="" label="COM_DROPBOX_PRIVATE_USER_DIRECTORIES" description="COM_DROPBOX_PRIVATE_USER_DIRECTORIES_DESC">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_PRIVATE_USER_DIRECTORIES_NAMED_BY_ID</option>
				<option value="2">COM_DROPBOX_PRIVATE_USER_DIRECTORIES_NAMED_BY_USERNAME</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="notification_email_sender_address" type="text" default="" label="COM_DROPBOX_NOTIFICATION_EMAIL_SENDER_ADDRESS" description="COM_DROPBOX_NOTIFICATION_EMAIL_SENDER_ADDRESS_DESCR"  required="false"/>
			<field name="notification_email_recipient_address" type="text" default="" label="COM_DROPBOX_NOTIFICATION_EMAIL_RECIPIENT_ADDRESS" description="COM_DROPBOX_NOTIFICATION_EMAIL_RECIPIENT_ADDRESS_DESCR"  required="false"/>
			<field name="max_cache_size" type="text" default="" label="COM_DROPBOX_MAX_CACHE_SIZE" description="COM_DROPBOX_MAX_CACHE_SIZE_DESCR"  class="validate-integer" validate="integer"  required="false"/>
			<field name="max_filesize_in_cache" type="text" default="" label="COM_DROPBOX_MAX_FILESIZE_IN_CACHE" description="COM_DROPBOX_MAX_FILESIZE_IN_CACHE_DESCR" class="validate-integer" validate="integer" required="false"/>
			<!--
	<field name="use_local_ssl_certificate" type="list" default="" label="COM_DROPBOX_USE_LOCAL_SSL_CERTIFICATE" description="COM_DROPBOX_USE_LOCAL_SSL_CERTIFICATE_DESCR"><option value="0">COM_DROPBOX_NO</option><option value="1">COM_DROPBOX_YES</option><option value="">JGLOBAL_USE_GLOBAL</option></field>
	-->
			<field name="do_verify_the_peer_ssl_certificate" type="list" default="" label="COM_DROPBOX_DO_VERIFY_PEER_SSL_CERTIFICATE" description="COM_DROPBOX_DO_VERIFY_PEER_SSL_CERTIFICATE_DESCR">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
		</fieldset>
		<fieldset name="LIST" label="PARAMETERS_FOR_LIST_FUNCTION">
			<field name="show_breadcrumbs" type="list" default=""

			       label="COM_DROPBOX_SHOW_BREADCRUMBS" description="COM_DROPBOX_SHOW_BREADCRUMBS_DESC">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="date_format" type="text" default="" label="COM_DROPBOX_DATE_FORMAT"  required="false"/>
			<field name="time_zone" type="text" default="" label="COM_DROPBOX_TIME_ZONE" required="false"/>
			<field name="list_sorting_field" type="radio" default="" label="COM_DROPBOX_LIST_SORTING_FIELD" description="COM_DROPBOX_LIST_SORTING_FIELD_DESC">
				<option value="name">COM_DROPBOX_FILE_NAME</option>
				<option value="modified_date">COM_DROPBOX_FILE_MODIFIED_DATE</option>
				<option value="size">COM_DROPBOX_FILE_SIZE</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="list_sorting_dir" type="radio" default="" label="COM_DROPBOX_LIST_SORTING_DIR" description="">
				<option value="asc">COM_DROPBOX_ASC</option>
				<option value="desc">COM_DROPBOX_DESC</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="list_show_modified_date" type="list" default=""
			       label="COM_DROPBOX_LIST_SHOW_MODIFIED_DATE" description="">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="list_show_size" type="list" default=""
			       label="COM_DROPBOX_LIST_SHOW_SIZE" description="">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
		</fieldset>
		<fieldset name="UPLOAD" label="PARAMETERS_FOR_UPLOAD_FUNCTION">
			<field name="change_folder_after_upload" type="list" default=""

			       label="COM_DROPBOX_CHANGE_FOLDER_AFTER_UPLOAD" description="">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="allow_subfolder_upload" type="list" default=""
			       label="COM_DROPBOX_ALLOW_UPLOAD_INTO_SUBFOLDERS" description="">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="description_for_upload" type="editor"  filter="safehtml" default=""
			       label="COM_DROPBOX_DESCRIPTION_FOR_UPLOAD"
			       description="COM_DROPBOX_DESCRIPTION_FOR_UPLOAD_DESC" rows="5" cols="40" />
			<field name="add_timestamp_to_upload" type="list" default=""
			       label="COM_DROPBOX_ADD_TIMESTAMP_TO_FILE" description="">
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_PREFIX</option>
				<option value="2">COM_DROPBOX_SUFFIX</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
		</fieldset>
		<fieldset name="PIC" label="PARAMETERS_FOR_PIC_FUNCTION">
			<field name="th_size_pic" type="list" default="" label="COM_DROPBOX_PIC_THUMBNAIL_SIZE" description="COM_DROPBOX_PIC_THUMBNAIL_SIZE_DESC" >
				<option value="thumb">COM_DROPBOX_PIC_THUMBNAIL_SIZE_THUMB</option>
				<option value="small">COM_DROPBOX_PIC_THUMBNAIL_SIZE_SMALL</option>
				<option value="medium">COM_DROPBOX_PIC_THUMBNAIL_SIZE_MEDIUM</option>
				<option value="large">COM_DROPBOX_PIC_THUMBNAIL_SIZE_LARGE</option>
				<option value="huge">COM_DROPBOX_PIC_THUMBNAIL_SIZE_HUGE</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="th_per_row" type="text" default=""   label="COM_DROPBOX_PIC_THUMBNAILS_PER_ROW"  required="false"  class="validate-integer" validate="integer" />
			<field name="enl_brdsize" type="text" default=""  label="COM_DROPBOX_PIC_BORDER_THICKNESS" description=""  required="false"  class="validate-integer" validate="integer"  />
			<field name="enl_brdcolor" type="text" default="#ffffff" label="COM_DROPBOX_PIC_BORDER_COLOR" description=""/>
			<field name="enl_brdbck" type="text" default="" label="COM_DROPBOX_PIC_BORDER_BACKGROUND_PIC" description=""  class="validate-integer" validate="integer" />
			<field name="enl_brdround" type="list" default="" label="COM_DROPBOX_PIC_USE_ROUNDED_BORDERS" description=""  >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_maxstep" type="text" default="" label="COM_DROPBOX_PIC_ANI_STEPS" description=""  class="validate-integer" validate="integer" />
			<field name="enl_speed" type="text" default="" label="COM_DROPBOX_PIC_TIME_BETWEEN_STEPS" description=""  class="validate-integer" validate="integer" />
			<field name="enl_ani" type="list" default="" label="COM_DROPBOX_PIC_ANIMATION" description=""   >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_PIC_ANIMATION_FADE</option>
				<option value="2">COM_DROPBOX_PIC_ANIMATION_GLIDE</option>
				<option value="3">COM_DROPBOX_PIC_ANIMATION_BUMPGLIDE</option>
				<option value="4">COM_DROPBOX_PIC_ANIMATION_SMOTHGLIDE</option>
				<option value="5">COM_DROPBOX_PIC_ANIMATION_EXPGLIDE</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_opaglide" type="list" default="" label="COM_DROPBOX_PIC_GLIDE_TRANSPARENCY" description=""    >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_shadow" type="list" default="" label="COM_DROPBOX_PIC_SHADOW_UNDER_BORDER" description=""  >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_shadowsize" type="text" default="10" label="COM_DROPBOX_PIC_SIZE_OF_SHADOW" description=""  class="validate-integer" validate="integer" />
			<field name="enl_shadowcolor" type="text" default="black"  label="COM_DROPBOX_PIC_SHADOW_COLOR" description="" />
			<field name="enl_shadowintens" type="text" default=""  label="COM_DROPBOX_PIC_SHADOW_INTESITY" description=""  required="false" class="validate-integer" validate="integer" />
			<field name="enl_dark" type="list" default="" label="COM_DROPBOX_PIC_DARKEN_SCREEN" description=""  >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="2">COM_DROPBOX_PIC_KEEP_DARK_WHEN_NAV</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_darkprct" type="text" default="50"  label="COM_DROPBOX_PIC_HOW_DARK_WHEN_NAV" description=""  class="validate-integer" validate="integer" />
			<field name="enl_darksteps" type="text" default="20"  label="COM_DROPBOX_PIC_TIME_OF_DARKENING" description=""  class="validate-integer" validate="integer" />
			<field name="enl_center" type="list" default="" label="COM_DROPBOX_PIC_CENTER_PIC" description=""  >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_drgdrop" type="list" default="" label="COM_DROPBOX_PIC_DRAG_N_DROP" description=""   >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_preload" type="list" default="" label="COM_DROPBOX_PIC_PRELOAD" description=""   >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_titlebar" type="list" default="" label="COM_DROPBOX_PIC_SHOW_TITLE_BAR" description=""   >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_keynav" type="list" default="" label="COM_DROPBOX_PIC_KEY_NAVIGATION" description=""   >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_wheelnav" type="list" default="" label="COM_DROPBOX_PIC_MOUSE_WHEEL_NAVIGATION" description=""  >
				<option value="0">COM_DROPBOX_NO</option>
				<option value="1">COM_DROPBOX_YES</option>
				<option value="">JGLOBAL_USE_GLOBAL</option>
			</field>
			<field name="enl_titletxtcol" type="text" default="#444444" label="COM_DROPBOX_PIC_COLOR_OF_TITLE_BAR_TEXT" description="" />
		</fieldset>
	</fields>
</form>
