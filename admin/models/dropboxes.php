<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// Check to ensure this file is included in Joomla!
\defined('_JEXEC') or die('Restricted access');

// import the Jo<PERSON><PERSON> modellist library
jimport('joomla.application.component.modellist');

/**
 * Dropbox Model
 *
 * @package    Joomla.Dropbox
 * @subpackage Components
 */
// @codingStandardsIgnoreLine because of no namespace set
class DropboxModelDropboxes extends JModelList
{
    /**
     * Method to build an SQL query to load the list data.
     *
     * @return string An SQL query
     */
    protected function getListQuery()
    {
        // Create a new query object.
        $db    = JFactory::getDBO();
        $query = $db->getQuery(true);
        // Select some fields
        $query->select('d.id,dropbox_secret,folder');
        // From the dropbox table
        $query->from('#__dropbox as d');

        // Join over the asset groups.
        $query->select('ag.title AS access_level');
        $query->join('LEFT', '#__viewlevels AS ag ON ag.id = d.access');

        $query->order('d.id ASC');

        return $query;
    }

    //    /**
    //     * Dropboxes data array
    //     *
    //     * @var array
    //     */
    //    var $_data;
    //
    //    /**
    //     * Returns the query
    //     * @return string The query to be used to retrieve the rows from the database
    //     */
    //    function _buildQuery()
    //    {
    //        $query = ' SELECT * '
    //            . ' FROM #__dropbox '
    //        ;
    //        return $query;
    //    }
    //
    //    /**
    //     * Retrieves the dropboxes data
    //     * @return array Array of objects containing the data from the database
    //     */
    //    function getData()
    //    {
    //        // Lets load the data if it doesn't already exist
    //        if (empty( $this->_data ))
    //        {
    //            $query = $this->_buildQuery();
    //            $this->_data = $this->_getList( $query );
    //        }
    //
    //        return $this->_data;
    //    }
    //
}
