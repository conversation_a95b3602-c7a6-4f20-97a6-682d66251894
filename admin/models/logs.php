<?php

// No direct access to this file
\defined('_JEXEC') or die('Restricted access');
// import the Joomla modellist library
jimport('joomla.application.component.modellist');

// @codingStandardsIgnoreLine because of no namespace set
class DropboxModelLogs extends JModelList
{
    /**
     * Constructor.
     *
     * @param array    An optional associative array of configuration settings.
     * @see   JController
     * @since 1.6
     */
    public function __construct($config = [])
    {
        if (empty($config['filter_fields'])) {
            $config['filter_fields'] = [
            'id', 'a.id',
            'userId', 'a.userId',
            'folder', 'a.folder',
            'filename', 'a.filename',
            'direction', 'a.direction',
            'time', 'a.time',
            'dropboxId', 'a.dropboxId',
            'cached', 'a.cached',
            ];
        }

        parent::__construct($config);
    }

    /**
     * Method to build an SQL query to load the list data.
     *
     * @return string An SQL query
     */
    protected function getListQuery()
    {
        // Create a new query object.
        $db    = JFactory::getDBO();
        $query = $db->getQuery(true);
        // Select some fields
        $query->select('id,userId,folder,filename,direction ,time,dropboxId,cached');
        // From the logs table
        $query->from('#__dropbox_logs');

        // Add the list ordering clause.
        $query->order($db->escape($this->getState('list.ordering', 'id')) . ' ' . $db->escape($this->getState('list.direction', 'ASC')));

        return $query;
    }
}
