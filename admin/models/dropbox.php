<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access
\defined('_JEXEC') or die('Restricted access');

// import Joomla modelform library
jimport('joomla.application.component.modeladmin');
/**
 * Dropbox Dropbox Model
 *
 * @package    Joomla.Dropbox
 * @subpackage Components
 */
// @codingStandardsIgnoreLine because of no namespace set
class DropboxModelDropbox extends JModelAdmin
{
    /**
     * Method override to check if you can edit an existing record.
     *
     * @param array  $data An array of input data.
     * @param string $key  The name of the key for the primary key.
     *
     * @return boolean
     * @since  1.6
     */
    protected function allowEdit($data = [], $key = 'id')
    {
        // Check specific edit permission then general edit permission.
        return JFactory::getUser()->authorise('core.edit', 'com_dropbox.message.' . ((int) isset($data[$key]) ? $data[$key] : 0)) or parent::allowEdit($data, $key);
    }
    /**
     * Returns a reference to the a Table object, always creating it.
     *
     * @param  type    The table type to instantiate
     * @param  string    A prefix for the table class name. Optional.
     * @param  array    Configuration array for model. Optional.
     * @return JTable A database object
     * @since  1.6
     */
    public function getTable($type = 'Dropbox', $prefix = 'DropboxTable', $config = [])
    {
        return JTable::getInstance($type, $prefix, $config);
    }
    /**
     * Method to get the record form.
     *
     * @param  array   $data     Data for the form.
     * @param  boolean $loadData True if the form is to load its own data (default case), false if not.
     * @return mixed   A JForm object on success, false on failure
     * @since  1.6
     */
    public function getForm($data = [], $loadData = true)
    {
        // Get the form.
        $form = $this->loadForm('com_dropbox.dropbox', 'dropbox', ['control' => 'jform', 'load_data' => $loadData]);
        if (empty($form)) {
            return false;
        }
        return $form;
    }

    /**
     * Method to get the data that should be injected in the form.
     *
     * @return mixed The data for the form.
     * @since  1.6
     */
    protected function loadFormData()
    {
        // Check the session for previously entered form data.
        $data = JFactory::getApplication()->getUserState('com_dropbox.edit.dropbox.data', []);
        if (empty($data)) {
            $data = $this->getItem();
        }
        return $data;
    }

    /**
     * Method to clear a record
     *
     * @access public
     * @return boolean True on success
     */
    public function clear()
    {
        $id  = JFactory::getApplication()->input->get('id');
        $row =& $this->getItem($id);

        $query = ' UPDATE #__dropbox ' .
        '  SET  `dropbox_secret` = "",
							`dropbox_state` = "0", 
							`username` = "",
							`password` = ""   
							WHERE id = ' . $id;
        //echo $query;
        //die();
        $this->_db->setQuery($query);
        $this->_db->execute();

        return true;
    }
}
