<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access

\defined('_JEXEC') or die('Restricted access');

jimport('joomla.application.component.controller');

// @codingStandardsIgnoreLine because of no namespace set
class DropboxController extends JControllerLegacy
{
    /**
     * display task
     *
     * @return void
     */
    public function display($cachable = false, $urlparams = [])
    {
        // set default view if not set
        // JRequest::setVar('view', JRequest::getCmd('view', 'Dropboxes'));

        $this->input->set('view', $this->input->get('view', 'Dropboxes'));

        // call parent behavior
        parent::display($cachable);
    }
}
