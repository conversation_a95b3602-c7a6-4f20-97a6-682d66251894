CREATE TABLE IF NOT EXISTS `#__dropbox` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dropbox_token` varchar(255) DEFAULT NULL,
  `dropbox_refresh_token` varchar(255) DEFAULT NULL,
  `dropbox_expiry_time` bigint NOT NULL DEFAULT 0,
  `dropbox_secret` varchar(50) NOT NULL,
  `dropbox_state` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `folder` varchar(255) NOT NULL,
  `params` text NOT NULL,
  `state` tinyint(3) NOT NULL DEFAULT '0',
  `access` int(11) unsigned NOT NULL DEFAULT '0',
  `box_type` varchar(15) NOT NULL,
  `username` varchar(255) ,
  `password` varchar(255) ,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

CREATE TABLE IF NOT EXISTS `#__dropbox_logs` (
`id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY ,
`userId` INT NULL DEFAULT NULL ,
`folder` VARCHAR( 255 ) NULL DEFAULT NULL ,
`filename` VARCHAR( 255 ) NULL DEFAULT NULL ,
`direction` SET( 'up', 'down' ) NOT NULL DEFAULT 'down',
`time` DATETIME NOT NULL ,
`dropboxId` INT NULL DEFAULT NULL ,
`cached` TINYINT( 1 ) NULL DEFAULT NULL
) ENGINE = MYISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

CREATE TABLE IF NOT EXISTS `#__dropbox_file_locks` (
				`id` int(11) NOT NULL AUTO_INCREMENT,
				`file_name_hash` varchar(255) NOT NULL,
				`lock_type` set('w','r') NOT NULL,
				`time` int(11) NOT NULL,
				PRIMARY KEY (`id`)
				);

