@charset "UTF-8";

.icon-48-dropbox {
    background-image: url("../images/icon-48-dropbox.png");
}
.icon-dropbox::before {
    content: "\33";
}
.form-horizontal .controls {
    padding-top: 3px;
}
.nav-tabs > li {
    margin-bottom: -1px;
}
.nav {
    margin-bottom: 1rem;
}
.nav > li > a {
    border: 1px solid transparent;
    color: var(--template-link-color);
    padding: 1rem;
    margin-right: .25rem;
    border-radius: .25rem .25rem 0 0;
    display: block;
}
.nav > li > a:hover,
.nav > li > a:focus {
    color: var(--body-color);
    background-color: var(--border);
}
.nav > .active > a,
.nav > .active > a:hover {
    background-color: #edf2f7;
    color: var(--body-color);
    border: 1px solid var(--border);
    border-bottom: 1px solid #edf2f7;
    cursor: default;
}
.controls input[type="text"],
.controls input[type="number"],
.controls input[type="email"],
.controls select,
.controls textarea,
.controls .chosen-container {
    width: 19rem;
}
.container-main #subhead-container {
    min-height: 4rem;
}
#adminForm {
    display: flex;
    flex-wrap: wrap;
    word-break: break-word;
}
#adminForm #j-main-container {
    flex-grow: 1;
}
#adminForm .sidebar-nav {
    padding: 0;
    padding-right: 1rem;
    flex-grow: 1;
    min-width: 12rem;
}
#adminForm .sidebar-nav ul a {
    background-color: var(--template-bg-dark-80);
    color: var(--template-text-light);
    border-radius: 0;
    margin: 0;
    padding: 0.5rem;
}
#adminForm .sidebar-nav li a:hover {
    background-color: var(--template-bg-dark-50);
}

#adminForm .sidebar-nav li.active a,
#adminForm .sidebar-nav li.active a:hover
#adminForm .sidebar-nav li.active a:focus {
    background-color: var(--template-bg-dark-60);
}

#adminForm table td.center {
    text-align: center;
}

