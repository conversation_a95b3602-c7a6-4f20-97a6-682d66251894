<?php

/**
 * @package    Joomla.Dropbox
 * @subpackage Components
 * @link       http://www.individual-it.net
 * @license    GNU/GPL
 */

// No direct access
\defined('_JEXEC') or die('Restricted access');

// import Joomla table library
jimport('joomla.database.table');

/**
 * Dropbox Table class
 *
 * @package    Joomla.Dropbox
 * @subpackage Components
 */
// @codingStandardsIgnoreLine because of no namespace set
class DropboxTableDropbox extends JTable
{
    /**
     * Primary Key
     *
     * @var int
     */
    public $id = null;

    /**
     * @var string
     */
    public $folder = null;

    /**
     * @var string
     */
    public $box_type = null;

    /**
     * @var string
     */
    public $username = null;

    /**
     * @var string
     */
    public $password = null;

    /**
     * Access Level
     *
     * @var int
     */
    public $access = null;

    /**
     * @var string
     */
    public $dropbox_secret = null;

    /**
     * @var string
     */
    public $dropbox_token = null;

    /**
     * @var string
     */
    public $dropbox_refresh_token = null;

    /**
     * @var int
     */
    public $dropbox_expiry_time = 0;

    /**
     * Constructor
     *
     * @param object Database connector object
     */
    public function __construct(&$db)
    {
        parent::__construct('#__dropbox', 'id', $db);
    }

    public function bind($array, $ignore = '')
    {
        if (key_exists('params', $array) && \is_array($array['params'])) {
            $registry = new JRegistry();
            $registry->loadArray($array['params']);
            $array['params'] = $registry->toString();
        }
        return parent::bind($array, $ignore);
    }

    /**
     * Overloaded load function
     *
     * @param  int     $pk    primary key
     * @param  boolean $reset reset data
     * @return boolean
     * @see    JTable:load
     */
    public function load($pk = null, $reset = true)
    {
        if (parent::load($pk, $reset)) {
            // Convert the params field to a registry.
            $params = new JRegistry();
            $params->loadString($this->params);
            $this->params = $params;
            return true;
        }
        return false;
    }
}
