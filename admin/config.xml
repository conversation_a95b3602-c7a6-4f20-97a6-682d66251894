<?xml version="1.0" encoding="UTF-8"?>

<config addrulepath="/administrator/components/com_dropbox/models/rules">
        <fieldset
                name="general"
                label="GENERAL_SETTINGS_LABEL">
                <field
                        name="function_list"
                        type="radio"
                        label="COM_DROPBOX_LIST_FILES"
                        description="COM_DROPBOX_LIST_FILES_DESC"
                        default="1"
                >
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>
		<option value="2">COM_DROPBOX_LIST_JUST_FOLDERS</option>
                </field>

                <field
                        name="function_upload"
                        type="radio"
                        label="COM_DROPBOX_UPLOAD"
                        description="COM_DROPBOX_UPLOAD_DESC"
                        default="1"
                >
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>

                </field>

	<field name="show_search" type="radio" label="COM_DROPBOX_SHOW_SEARCH" description="" default="1">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>
	</field>
								
	<field name="function_pic" type="radio" label="VIEW_PICTURES" description="COM_DROPBOX_VIEW_PICTURES_DESC" default="1">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>
	</field>
                
	<field name="log_downloads" type="radio" default="0" label="LOG_DOWNLOADS" description="LOG_DOWNLOADS_DESCR">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>	
			
	</field>                

	<field name="log_uploads" type="radio" default="0" label="LOG_UPLOADS" description="LOG_UPLOADS_DESCR">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>	
			
	</field>		
	
	<field name="view_downloads_in_browser" type="radio" default="0" label="COM_DROPBOX_VIEW_DOWNLOADS_IN_BROWSER" description="COM_DROPBOX_VIEW_DOWNLOADS_IN_BROWSER_DESCR">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>			
	</field>	
	
	<field name="private_user_directories" type="radio" default="0" label="COM_DROPBOX_PRIVATE_USER_DIRECTORIES" description="COM_DROPBOX_PRIVATE_USER_DIRECTORIES_DESC">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_PRIVATE_USER_DIRECTORIES_NAMED_BY_ID</option>	
		<option value="2">COM_DROPBOX_PRIVATE_USER_DIRECTORIES_NAMED_BY_USERNAME</option>
	</field>
	

	<field name="notification_email_sender_address" type="text" default="" label="COM_DROPBOX_NOTIFICATION_EMAIL_SENDER_ADDRESS" description="COM_DROPBOX_NOTIFICATION_EMAIL_SENDER_ADDRESS_DESCR"  required="false"/>

	<field name="notification_email_recipient_address" type="text" default="" label="COM_DROPBOX_NOTIFICATION_EMAIL_RECIPIENT_ADDRESS" description="COM_DROPBOX_NOTIFICATION_EMAIL_RECIPIENT_ADDRESS_DESCR"  required="false"/>

	<field name="max_filesize_in_cache" type="text" default="" label="COM_DROPBOX_MAX_FILESIZE_IN_CACHE" description="COM_DROPBOX_MAX_FILESIZE_IN_CACHE_DESCR"   validate="integer"  required="false"/>

	<field name="max_cache_size" type="text" default="" label="COM_DROPBOX_MAX_CACHE_SIZE" description="COM_DROPBOX_MAX_CACHE_SIZE_DESCR"   validate="integer"  required="false"/>

	<field name="max_writelog_sleeping_time" type="text" default="" label="COM_DROPBOX_MAX_WRITELOCK_SLEEPING_TIME" description="COM_DROPBOX_MAX_WRITELOCK_SLEEPING_TIME_DESCR"   validate="integer"  required="false"/>
	
		<!--
	<field name="use_local_ssl_certificate" type="radio" default="0" label="COM_DROPBOX_USE_LOCAL_SSL_CERTIFICATE" description="COM_DROPBOX_USE_LOCAL_SSL_CERTIFICATE_DESCR">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>	
	</field>	
	-->
	
	<field name="do_verify_the_peer_ssl_certificate" type="radio" default="1" label="COM_DROPBOX_DO_VERIFY_PEER_SSL_CERTIFICATE" description="COM_DROPBOX_DO_VERIFY_PEER_SSL_CERTIFICATE_DESCR">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>	
	</field>	
	
	get('private_user_directories',0)
	
	</fieldset>
	        <fieldset
                name="list_function"
                label="PARAMETERS_FOR_LIST_FUNCTION"
                
        >

	<field name="show_breadcrumbs" type="radio" default="1" label="COM_DROPBOX_SHOW_BREADCRUMBS" description="COM_DROPBOX_SHOW_BREADCRUMBS_DESC">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>
	

	<field name="date_format" type="text" default="d-m-Y H:i:s" label="COM_DROPBOX_DATE_FORMAT" required="true"/>
	<field name="time_zone" type="text" default="Europe/Berlin" label="COM_DROPBOX_TIME_ZONE" required="true"/>
	
	<field name="list_sorting_field" type="radio" default="name" label="COM_DROPBOX_LIST_SORTING_FIELD" description="COM_DROPBOX_LIST_SORTING_FIELD_DESC">
		<option value="name">COM_DROPBOX_FILE_NAME</option>
		<option value="modified_date">COM_DROPBOX_FILE_MODIFIED_DATE</option>
		<option value="size">COM_DROPBOX_FILE_SIZE</option>						
	</field>

	<field name="list_sorting_dir" type="radio" default="asc" label="COM_DROPBOX_LIST_SORTING_DIR" description="">
		<option value="asc">COM_DROPBOX_ASC</option>
		<option value="desc">COM_DROPBOX_DESC</option>
	</field>


	<field name="list_show_modified_date" type="radio" default="1" label="COM_DROPBOX_LIST_SHOW_MODIFIED_DATE" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>
	
	<field name="list_show_size" type="radio" default="1" label="COM_DROPBOX_LIST_SHOW_SIZE" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>	
	
	</fieldset>
		        <fieldset
                name="upload_function"
                label="PARAMETERS_FOR_UPLOAD_FUNCTION"
        >
	<field name="change_folder_after_upload" type="radio" default="1" label="COM_DROPBOX_CHANGE_FOLDER_AFTER_UPLOAD" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>

	<field name="allow_subfolder_upload" type="radio" default="1" label="COM_DROPBOX_ALLOW_UPLOAD_INTO_SUBFOLDERS" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>

	<field name="add_timestamp_to_upload" type="radio" default="0" label="COM_DROPBOX_ADD_TIMESTAMP_TO_FILE" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_PREFIX</option>
		<option value="2">COM_DROPBOX_SUFFIX</option>		
						
	</field> 

 	<field name="description_for_upload" type="editor"  filter="safehtml" default="" label="COM_DROPBOX_DESCRIPTION_FOR_UPLOAD" description="COM_DROPBOX_DESCRIPTION_FOR_UPLOAD_DESC" rows="5" cols="40" />

</fieldset>
		        <fieldset
                name="pic_function"
                label="PARAMETERS_FOR_PIC_FUNCTION"
        >

	<field name="th_size_pic" type="radio" default="medium" label="COM_DROPBOX_PIC_THUMBNAIL_SIZE" description="COM_DROPBOX_PIC_THUMBNAIL_SIZE_DESC">
		<option value="thumb">COM_DROPBOX_PIC_THUMBNAIL_SIZE_THUMB</option>
		<option value="small">COM_DROPBOX_PIC_THUMBNAIL_SIZE_SMALL</option>
		<option value="medium">COM_DROPBOX_PIC_THUMBNAIL_SIZE_MEDIUM</option>		
		<option value="large">COM_DROPBOX_PIC_THUMBNAIL_SIZE_LARGE</option>
		<option value="huge">COM_DROPBOX_PIC_THUMBNAIL_SIZE_HUGE</option>
	</field>
	
	//TODO validierung einfügen
	
	<field name="th_per_row" type="text" default="4"  validate="integer"  required="true"  label="COM_DROPBOX_PIC_THUMBNAILS_PER_ROW" />
	<field name="enl_brdsize" type="text" default="5"  validate="integer"  required="true" label="COM_DROPBOX_PIC_BORDER_THICKNESS" description="" />
	<field name="enl_brdcolor" type="text" default="" label="COM_DROPBOX_PIC_BORDER_COLOR" description="" />
	<field name="enl_brdbck" type="text" default="" label="COM_DROPBOX_PIC_BORDER_BACKGROUND_PIC" description="" />	
	<field name="enl_brdround" type="radio" default="1" validate="integer"  label="COM_DROPBOX_PIC_USE_ROUNDED_BORDERS" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>	
	<field name="enl_maxstep" type="text" default="10"  validate="integer"  label="COM_DROPBOX_PIC_ANI_STEPS" required="true" description="" />	
	<field name="enl_speed" type="text" default="1" validate="integer"  label="COM_DROPBOX_PIC_TIME_BETWEEN_STEPS" required="true" description="" />	
	<field name="enl_ani" type="radio" default="5"  validate="integer" label="COM_DROPBOX_PIC_ANIMATION" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_PIC_ANIMATION_FADE</option>
		<option value="2">COM_DROPBOX_PIC_ANIMATION_GLIDE</option>				
		<option value="3">COM_DROPBOX_PIC_ANIMATION_BUMPGLIDE</option>				
		<option value="4">COM_DROPBOX_PIC_ANIMATION_SMOTHGLIDE</option>				
		<option value="5">COM_DROPBOX_PIC_ANIMATION_EXPGLIDE</option>				
						
	</field>	
	
	<field name="enl_opaglide" type="radio" default="1"  validate="integer" label="COM_DROPBOX_PIC_GLIDE_TRANSPARENCY" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>	
		
	<field name="enl_shadow" type="radio" default="1"  validate="integer" label="COM_DROPBOX_PIC_SHADOW_UNDER_BORDER" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>	
	<field name="enl_shadowsize" type="text" default="10"  validate="integer" required="true" label="COM_DROPBOX_PIC_SIZE_OF_SHADOW" description="" />	
	<field name="enl_shadowcolor" type="text" default=""  label="COM_DROPBOX_PIC_SHADOW_COLOR" description="" />	
	<field name="enl_shadowintens" type="text" default="5"  validate="integer"  required="true" label="COM_DROPBOX_PIC_SHADOW_INTESITY" description="" />	
	<field name="enl_dark" type="radio" default="1" validate="integer"  label="COM_DROPBOX_PIC_DARKEN_SCREEN" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>	
		<option value="2">COM_DROPBOX_PIC_KEEP_DARK_WHEN_NAV</option>								
	</field>	
	<field name="enl_darkprct" type="text" default="50"  validate="integer"  required="true" label="COM_DROPBOX_PIC_HOW_DARK_WHEN_NAV" description="" />	
	<field name="enl_darksteps" type="text" default="20"  validate="integer"  required="true" label="COM_DROPBOX_PIC_TIME_OF_DARKENING" description="" />	
	<field name="enl_center" type="radio" default="1"  validate="integer"  label="COM_DROPBOX_PIC_CENTER_PIC" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>
	<field name="enl_drgdrop" type="radio" default="1"  validate="integer"  label="COM_DROPBOX_PIC_DRAG_N_DROP" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>
	<field name="enl_preload" type="radio" default="1" validate="integer"  label="COM_DROPBOX_PIC_PRELOAD" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>
	<field name="enl_titlebar" type="radio" default="1"  validate="integer" label="COM_DROPBOX_PIC_SHOW_TITLE_BAR" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>	
	<field name="enl_keynav" type="radio" default="1" validate="integer"  label="COM_DROPBOX_PIC_KEY_NAVIGATION" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>	
	<field name="enl_wheelnav" type="radio" default="1"  validate="integer" label="COM_DROPBOX_PIC_MOUSE_WHEEL_NAVIGATION" description="">
		<option value="0">COM_DROPBOX_NO</option>
		<option value="1">COM_DROPBOX_YES</option>				
	</field>		
	<field name="enl_titletxtcol" type="text" default="" label="COM_DROPBOX_PIC_COLOR_OF_TITLE_BAR_TEXT" description="" />	
		

        </fieldset>




</config>


